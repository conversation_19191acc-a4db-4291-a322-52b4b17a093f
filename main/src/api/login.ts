import axios from "axios";


// const host = "https://aiem.waimai.test.sankuai.com";
const host = import.meta.env.VITE_BASE_URL;
// const host ="https://mttbbe.waimai.test.sankuai.com"
/**
 * Model for ID
 */
export type IdType = string;

/**
 * 
 * @param {*} query 
 * @returns 
 */
async function sendForgotVerifyCode(query) {
  const res = await axios({
    method: "get",
    url: `${host}/api/v1/auth/user/forgot/verifyCode`,
    params: query,
  });
  return res;
}

async function sendLoginVerifyCode(query) {
  const res = await axios({
    method: "get",
    url: `${host}/api/v1/auth/user/login/verifyCode`,
    params: query,
  });
  return res;
}

async function ssoAccesstoken(query) {
  const res = await axios({
    method: "get",
    url: `${host}/api/v1/auth/user/getAccessToken`,
    params: query,
  });
  return res;
}

async function signin(query) {
  const res = await axios({
    method: "post",
    url: `${host}/api/v1/auth/user/signin`,
    params: query,
  });
  return res;
}
async function signinWithVerification(query) {
  const res = await axios({
    method: "post",
    url: `${host}/api/v1/auth/user/verification/signin`,
    params: query,
  });
  return res;
}

async function PasswordReset(query) {
  const res = await axios({
    method: "post",
    url: `${host}/api/v1/auth/password/reset`,
    data: query,
  });
  return res;
}

async function tokenRefresh(query) {
  const res = await axios({
    method: "post",
    url: `${host}/api/v1/auth/token/refresh`,
    data: query,
  });
  return res;
}





export default { sendForgotVerifyCode, sendLoginVerifyCode, ssoAccesstoken, signin, signinWithVerification, PasswordReset, tokenRefresh};