import React, { useState, useRef, useEffect } from "react";
import { Form, Input, Button, Flex, message, Spin, ConfigProvider } from "antd";
import { ArrowLeftOutlined, KeyOutlined } from "@ant-design/icons";
import { AppstoreOutlined, DownloadOutlined } from "@ant-design/icons";
import NProgress from "nprogress";
import 'nprogress/nprogress.css';
import Api from "../../api/login";
import Cookies from "js-cookie";

export default () => {
  const [messageApi, contextHolder] = message.useMessage();
  const [registerForm] = Form.useForm();
  const [loginForm] = Form.useForm();
  const [isMobile, setIsMobile] = useState(true);
  const [activeCode, setActiveCode] = useState(true);
  const [isResetPwd, setIsResetPwd] = useState(false);
  const [codeLoading, setCodeLoading] = useState(false);
  const [sendCodeMsg, setSendCodeMsg] = useState("发送验证码");
  const [restSeconds, setRestSeconds] = useState(60);
  const [isLoading, setIsLoading] = useState(true);
  const [token, setToken] = useState({});
  const [isSinglePage, setIsSinglePage] = useState(
    location.pathname === "/login"
  );

  const resetError = () => {
    // 重置错误信息
  };

  const sendCode = async () => {
    try {
      await registerForm.validateFields(["email"]);
      setCodeLoading(true);
      setSendCodeMsg(`60s`);
      const startCountdown = () => {
        let seconds = 60;
        const timer = setInterval(() => {
          seconds--;
          if (seconds === 0) {
            clearInterval(timer);
            setSendCodeMsg("发送验证码");
            setCodeLoading(false);
          } else {
            setSendCodeMsg(`${seconds}s`);
          }
          setRestSeconds(seconds);
        }, 1000);
        return timer;
      };

      const timer = startCountdown();

      const sendVerifyCode = async () => {
        const account = registerForm.getFieldValue("email");
        const api = isResetPwd ? Api.sendForgotVerifyCode : Api.sendLoginVerifyCode;
        
        try {
          await api({ account });
            messageApi.success("验证码发送成功");
        } catch (err) {
          console.error(err);
            messageApi.error(err?.response?.data?.msg || "发送验证码失败");
          resetCodeState();
        clearInterval(timer);
      }
  };

      await sendVerifyCode();
    } catch (err) {
      console.error(err);
      resetCodeState();
    }
  };

  const resetCodeState = () => {
    setSendCodeMsg("发送验证码");
    setCodeLoading(false);
    setRestSeconds(60);
  };

  const submitForm = async () => {
    if (activeCode) {
      // 注册逻辑
      signUp();
    } else {
      // 登录逻辑
      signIn();
    }
  };
  async function signIn() {
    try {
      const values = await loginForm.validateFields();
      Api.signin(values).then((res) => {
        const { data } = res;
        setLocalToken(data);
        messageApi.success("登录成功");
        //  navigate("/workbench");
        window.location.href = "/workbench";
      }).catch((err) => {
        messageApi.error(err?.response?.data?.msg || "登录失败");
      })
    } catch (errorInfo) {
      console.log("loginForm Failed:", errorInfo);
    }
  }

  async function signUp() {
    try {
      const values = await registerForm.validateFields();
      Api.signinWithVerification({
        email: values.email,
        password: values.verifyCode,
      }).then((res) => {
        const { data } = res;
        setLocalToken(data);
        messageApi.success("登录成功");
        window.location.href = "/workbench";
      }).catch((err) => {
        messageApi.error(err?.response?.data?.msg || "登录失败");
      });
    } catch (errorInfo) {
      console.log(" registerFormFailed:", errorInfo);
    }
  }

  const setLocalToken = (data)=>{
    const { token, refresh_token, third_party_info } = data;
    localStorage.setItem("nc-token", token);
    localStorage.setItem("refresh_token", refresh_token);
    if (third_party_info && third_party_info.ssoId) {
      localStorage.setItem("nc-ssoId", third_party_info.ssoId);
      Cookies.set("89872f67d7_ssoid", third_party_info.ssoId);
    }
  }

  const resetPwd = async () => {
    // 重置密码逻辑
    try {
      const values = await registerForm.validateFields();
      console.log("register FormSuccess:1", values);
      Api.PasswordReset({
        verificationCode: values.verifyCode,
        account: values.email,
        password: values.password,
      })
        .then((res) => {
          console.log("res", res);
          messageApi.success("重置成功");
        })
        .catch((err) => {
          console.log("err", err);
          messageApi.error(err?.response?.data?.msg || "重置失败");
        });
    } catch (errorInfo) {
      console.log(" registerFormFailed:", errorInfo);
    }
  };

  const changeType = (val) => {
    setIsMobile(val);
    // 重置登录表单
    loginForm.resetFields();
    // 重置注册表单
    registerForm.resetFields();
    // 重置错误提示
    loginForm.setFields([
      {
        name: 'email',
        errors: [],
      },
      {
        name: 'password',
        errors: [],
      }
    ]);
    registerForm.setFields([
      {
        name: 'email',
        errors: [],
      },
      {
        name: 'verifyCode',
        errors: [],
      }
    ]);
  };

  const changeTab = () => {
    setActiveCode(!activeCode);
    changeType(true);
    // 初始化表单
  };

  const navigateForgotPassword = () => {
    const registerFormValues = registerForm.getFieldsValue();
    const loginFormValues = loginForm.getFieldsValue();
    // 忘记密码逻辑
    Object.keys(registerFormValues).forEach((key) => {
      if (key !== "email") {
        registerForm.setFieldsValue({ [key]: "" });
      } else {
        registerForm.setFieldsValue({ [key]: loginFormValues.email });
      }
    });
    setActiveCode(true);
    setIsResetPwd(true);
  };

  const ssoLogin = async () => {
    // 未获取到有效token且选择进行sso登录时
    if (!token.value) {
      const redirect_uri = encodeURIComponent(
        // `http://localhost:8080/sso/callback?original-url=${encodeURIComponent('/api/v1/auth/user/getAccessToken')}`,
        `${location.origin}/login`
      );

      // console.log('appInfo.envName', appInfo.value.envName)
      // let url = `https://ssosv.sankuai.com/sson/login?redirect_uri=${redirect_uri}&client_id=92645e8788`;
      // if (appInfo.value.envName === "test") {
      //   url = `http://ssosv.it.test.sankuai.com/sson/login?redirect_uri=${redirect_uri}&client_id=21ee50c3a3`;
      // }
      // 线下
      const url = `${
        import.meta.env.VITE_LOGIN
      }?redirect_uri=${redirect_uri}&client_id=${
        import.meta.env.VITE_CLIENT_ID
      }`;
      // 线上
      // const url = `https://ssosv.sankuai.com/sson/login?redirect_uri=${redirect_uri}&client_id=92645e8788`
      window.location.href = url;
    }
  };

  const backSignin = () => {
    const registerFormValues = registerForm.getFieldsValue();

    Object.keys(registerFormValues).forEach((key) => {
      if (key !== "email") {
        registerForm.setFieldsValue({ [key]: "" });
      }
    });

    setIsResetPwd(false);
  };

  const validateEmail = (v: string) =>
    /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@(([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]{2,})$/i.test(
      v
    );

  const validatePhone = (v: string) =>
    /^(13[0-9]|14[5|7|9]|15[0-3|5-9]|16[2|5|6|7]|17[0-8]|18[0-9]|19[8|9])\d{8}$/.test(
      v
    );

  useEffect(() => {
    setIsSinglePage(location.pathname === "/login");
    if (!token.value) {
      const arr = location.search.slice(1)?.split("&");
      const code = arr.find((ele) => ele.startsWith("code="))?.slice(5);
      if (code) {
        setIsLoading(true);
        Api.ssoAccesstoken({ code })
          .then(async (res) => {
            const { data } = res;
            setLocalToken(data);
            sessionStorage.setItem("isSsoLogin", "true");
            messageApi.success("登录成功");
            const rebak = localStorage.getItem("REBACK_URL")
            if (rebak) {
              localStorage.removeItem("REBACK_URL");
              window.location.href = rebak;
            } else { 
              window.location.href = "/workbench";
            }
          })
          .catch(() => setIsLoading(false));
      } else {
        setIsLoading(false);
      }
    }
  }, []);

  useEffect(() => {
    if (isLoading) {
      NProgress.start();
    } else {
      NProgress.done();
    }
  }, [isLoading]);

  return (
    <div>
      {isLoading ? (
        <div className="flex justify-center items-center h-screen w-screen">
          <Spin size="large" />
        </div>
      ) : (
        <div>
          {contextHolder}
          <div
            className={`${
              isSinglePage ? "h-screen" : ""
            }  flex items-center w-[700px]`}
            style={{ borderRadius: 10, margin: "0 auto" }}
          >
            <div
              className="w-[240px] min-w-60 relative rounded-l-lg h-[500px]" // 设置高度
              style={{
                backgroundImage:
                  "url(https://p0.meituan.net/waimaiqact/5a284e3b5cff5f7646d3df85e8db5c96486674.png)",
                backgroundSize: "cover", // 覆盖整个区域
                backgroundPosition: "center", // 居中显示
              }}
            >
              <div className="bottom-5 absolute left-16">
                <Button
                  shape="round"
                  icon={<DownloadOutlined />}
                  onClick={() =>
                    window.open(import.meta.env.VITE_APP_CLIENT_DOWNLOAD_URL)
                  }
                >
                  下载客户端
                </Button>
              </div>
            </div>

            <div className="flex-1 bg-center bg-cover bg-no-repeat">
              <div className="flex  px-6  py-4">
                {isResetPwd && (
                  <ArrowLeftOutlined
                    className="leading-8 text-lg mr-2 px-1 flex items-center cursor-pointer"
                    onClick={backSignin}
                  />
                )}
                <div className="text-[18px] font-semibold">
                  {isResetPwd
                    ? "重置密码"
                    : activeCode
                      ? "验证码登录/注册"
                      : "密码登录"}
                </div>
              </div>
              <div className="p-3 px-6 h-full flex flex-col justify-between">
                <div className="flex h-6 mb-6 border-b border-solid border-gray-300">
                  <div
                    className="flex items-center justify-center text-s cursor-pointer"
                    style={{
                      backgroundColor: isMobile ? "white" : "transparent",
                      color: isMobile ? "#1530C1" : "inherit",
                    }}
                    onClick={() => changeType(true)}
                  >
                    手机号
                  </div>
                  <div className="w-4"></div>
                  <div
                    className="flex items-center justify-center text-s cursor-pointer"
                    style={{
                      backgroundColor: !isMobile ? "white" : "transparent",
                      color: !isMobile ? "#1530C1" : "inherit",
                    }}
                    onClick={() => changeType(false)}
                  >
                    邮箱
                  </div>
                </div>
                {!activeCode ? (
                  <Form
                    form={loginForm}
                    layout="vertical"
                    hideRequiredMark
                    key="loginForm"
                  >
                    <Form.Item
                      name="email"
                      rules={[
                        {
                          required: true,
                          message: isMobile ? "请输入手机号!" : "请输入邮箱!",
                        },
                        {
                          validator: (_, value) => {
                            return new Promise((resolve, reject) => {
                              if (!value?.length) {
                                return resolve();
                              }
                              if (isMobile) {
                                if (validatePhone(value)) {
                                  return resolve();
                                }
                                return reject(new Error("手机号格式不正确"));
                              } else {
                                if (validateEmail(value)) {
                                  return resolve();
                                }
                                return reject(new Error("邮箱格式不正确"));
                              }
                            });
                          },
                        },
                      ]}
                    >
                      <Input
                        placeholder={
                          isMobile ? "请输入手机号" : "请输入邮箱"
                        }
                        onFocus={resetError}
                      />
                    </Form.Item>

                    <Form.Item
                      key="password"
                      label="密码"
                      name="password"
                      rules={[
                        {
                          required: true,
                          message: "请输入密码!",
                        },
                      ]}
                    >
                      <Input.Password
                        key="password"
                        className="password"
                        autoComplete="new-password"
                        placeholder="请输入密码"
                        onFocus={resetError}
                      />
                    </Form.Item>
                  </Form>
                ) : (
                  <Form
                    form={registerForm}
                    layout="vertical"
                    hideRequiredMark
                    key="registerForm"
                  >
                    <Form.Item
                      name="email"
                      rules={[
                        {
                          required: true,
                          message: isMobile ? "请输入手机号!" : "请输入邮箱!",
                        },
                        {
                          validator: (_, value) => {
                            return new Promise((resolve, reject) => {
                              if (!value?.length) {
                                return resolve();
                              }
                              if (isMobile) {
                                if (validatePhone(value)) {
                                  return resolve();
                                }
                                return reject(new Error("手机号格式不正确"));
                              } else {
                                if (validateEmail(value)) {
                                  return resolve();
                                }
                                return reject(new Error("邮箱格式不正确"));
                              }
                            });
                          },
                        },
                      ]}
                    >
                      <Input
                        autoComplete="off"
                        placeholder={isMobile ? "请输入手机号" : "请输入邮箱"}
                        onFocus={resetError}
                      />
                    </Form.Item>
                    <Form.Item
                      label="验证码"
                      name="verifyCode"
                      rules={[
                        {
                          required: true,
                          message: "请输入验证码",
                        },
                      ]}
                      className="relative"
                    >
                      <div>
                        <Input
                          autoComplete="off"
                          placeholder="请输入验证码"
                          onFocus={resetError}
                        />

                        <div className="absolute w-0 h-8 top-0 right-[108px] border-l border-solid border-gray-300"></div>
                        <Button
                          className="absolute text-nc w-21 h-4 bg-white leading-6 top-2 right-1.5 flex justify-center items-center border-none shadow-none text-sm"
                          disabled={codeLoading}
                          onClick={sendCode}
                          type="text"
                        >
                          {sendCodeMsg}
                        </Button>
                      </div>
                    </Form.Item>

                    {isResetPwd && (
                      <>
                        <Form.Item
                          label="新密码"
                          name="password"
                          rules={[
                            { required: true, message: "密码为必填" },
                            { min: 8, message: "密码长度不能少于8位" },
                          ]}
                        >
                          <Input.Password
                            className="password"
                            autoComplete="new-password"
                            placeholder="请输入新密码"
                            onFocus={resetError}
                          />
                        </Form.Item>
                        <Form.Item
                          label="确认新密码"
                          name="confirmPwd"
                          dependencies={["password"]}
                          rules={[
                            {
                              required: true,
                              message: "请输入确认密码",
                            },
                            {
                              validator: (_, value) => {
                                return new Promise((resolve, reject) => {
                                  if (
                                    value &&
                                    value !==
                                      registerForm.getFieldValue("password")
                                  ) {
                                    return reject(new Error("与新密码不一致"));
                                  } else {
                                    return resolve();
                                  }
                                });
                              },
                            },
                          ]}
                        >
                          <Input.Password
                            className="password"
                            placeholder="请输入确认密码"
                            onFocus={resetError}
                          />
                        </Form.Item>
                      </>
                    )}
                  </Form>
                )}
                {!isResetPwd && (
                  <>
                    <Flex vertical gap="small" style={{ width: "100%" }}>
                      <Button
                        type="primary"
                        className="mt-2 mb-2"
                        data-testid="nc-form-signin__submit"
                        onClick={submitForm}
                      >
                        {activeCode ? "登录/注册" : "登录"}
                      </Button>
                    </Flex>

                    <div className="h-6 flex justify-between">
                      <div
                        className="leading-6 text-nc cursor-pointer text-xs"
                        onClick={changeTab}
                      >
                        {activeCode ? "密码登录" : "验证码登录/注册"}
                      </div>
                      <div
                        className="leading-6 text-nc cursor-pointer text-xs"
                        onClick={navigateForgotPassword}
                      >
                        忘记密码?
                      </div>
                    </div>
                    <div className="h-6 flex justify-between items-center">
                      <div className="w-40 h-0 border-t border-solid border-gray-300"></div>
                      <button
                        className="w-20 h-6 text-black rounded-md"
                        onClick={ssoLogin}
                      >
                        <div className="flex items-center text-xs">
                          <KeyOutlined className="mr-1 font-bold text-xs" />
                          <span className="text-[10px]">SSO登录</span>
                        </div>
                      </button>
                      <div className="w-40 h-0 border-t border-solid border-gray-300"></div>
                    </div>
                  </>
                )}
                {isResetPwd && (
                  <Flex vertical gap="small">
                    <Button
                      className="mt-2 mb-4"
                      onClick={resetPwd}
                      type="primary"
                      data-testid="nc-form-signin__submit"
                    >
                      确认
                    </Button>
                  </Flex>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

