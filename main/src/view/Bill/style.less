.steps-container{
    padding: 20px;
    .step{
        .title{
            font-size: 16px;
            font-weight: 500;
            height: 42px;
        }
        .key-value{
            color:red;
            font-size: 16px;
            font-weight: 500;
        }
        .sub-key-value{
            font-size: 16px;
            font-weight: 500;
        }
        .card{
            margin-bottom: 10px;
        }
        

    }
    .two-step{
        margin-top: 30px;
        .key-value{
            color:red;
            font-size: 14px;
            font-weight: 500;
        }

    }
    .three-steps{
        margin-top: 30px;
        .result{
            font-size:22px;
            font-weight: bolder;
    
        }
        .desc{
            font-size:12px;
            color: #666;
        }
    }
//    卡片样式
    .card_title{
       height: 30px; 
       line-height: 30px;
       background: linear-gradient(96deg, rgba(108, 189, 255, 0.1) 0%, rgba(33, 55, 255, 0.1) 100%);
       border-radius: 4px;
       padding-left: 5px;
       font-size: 15px;
       font-weight: 600;
    }


    

}