import React, { createContext, useContext, useState, useEffect } from 'react';
import { fetchBillData } from '../../../api/bill';

export const PricingContext = createContext<any>(null);

export const PricingContextProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState({
    billingOptions: [],
    selectedBillingType: '',
    selectedPlan: null,
    plans: {},
    marketingContent: {},
    isLoading: true
  });

  useEffect(() => {
    const getData = async () => {
      const data = await fetchBillData();
      setState({ ...data, isLoading: false });
    };
    getData();
  }, []);

  const handleBillingTypeChange = (billingType: string) => {
    setState(prev => ({ 
      ...prev, 
      selectedBillingType: billingType, 
      selectedPlan: null 
    }));
  };

  const setSelectedPlan = (planId: string) => {
    setState(prev => ({ ...prev, selectedPlan: planId }));
  };

  return (
    <PricingContext.Provider value={{
      ...state,
      handleBillingTypeChange,
      setSelectedPlan,
    }}>
      {children}
    </PricingContext.Provider>
  );
}; 

// 可选：导出一个自定义 hook 来使用这个 context
export const usePricingContext = () => {
  const context = useContext(PricingContext);
  if (context === undefined) {
    throw new Error('usePricingContext must be used within a PricingContextProvider');
  }
  return context;
};