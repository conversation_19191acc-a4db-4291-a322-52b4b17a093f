export interface PlanPrice {
  amount: string;
  original: string;
}

export interface Feature {
  name: string;
  type: 'LIMITED' | 'UNLIMITED';
  quantity?: number;
  unit?: string;
}

export interface Application {
  name: string;
  features: Feature[];
}

export interface SelectPlan {
  id: string;
  name: string;
  marketingName: string;
  price: PlanPrice;
  pricingMode: 'UNIT_PRICING' | 'PERIOD_PACKAGE' | 'TOTAL_PACKAGE';
  billingUnit: 'NUMBER' | 'TIMES' | 'WEEK' | 'MONTH' | 'QUARTER' | 'HALF_YEAR' | 'YEAR';
  period?: 'ONE_WEEK' | 'ONE_MONTH' | 'THREE_MONTHS' | 'SIX_MONTHS' | 'TWELVE_MONTHS';
  applications?: Application[];
  quantity?: number,
  orderNo?: string
}

// 更新 OrderStepsProps 接口
export interface OrderStepsProps {
  isModalVisible: boolean;
  setIsModalVisible: (visible: boolean) => void;
  selectedPlan: SelectPlan;
  salesPageId: string;
}
