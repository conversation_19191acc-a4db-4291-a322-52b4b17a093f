import React, { useState, useEffect, useCallback } from "react";
import NProgress from "nprogress";
import 'nprogress/nprogress.css';
import { Avatar, Popover, List, Typography, Modal, Config<PERSON>rovider, <PERSON><PERSON>, Divider, Spin, Tooltip,message } from "antd";
import dayjs from 'dayjs';
import {
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  DownOutlined,
  UpOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import RouteApp from "../route/Index";
import UserApi from "../api/user";
import { getCurrentEntitlement, getEntitlementSummary,getInitSelfSpace } from '@/api';
import Login from "./Login";
import "../index.css";
import "./home.less";
import { pathNeedSidebar } from '../config/menus';
import { openOrDownloadApp } from '../../../packages/utils';

import VersionUpdateHeader from './UpdateVersion/header';
import VersionUpdateContent from './UpdateVersion/content';
import globalStateActions from '../store/store';
import { isEE } from '../utils/ipcRenderer';
import apiMain from '../api/main';

const { ipcApiRoute } = apiMain;
import CreatAppModal from "../../../rdc/app-create/src/App"

const MenuItem: any = ({
  icon,
  text,
  active,
  subItems,
  onToggle,
  isOpen,
  onClick,
}) => (
  <li
    className={`mb-2 ${active ? "bg-blue-100 text-blue-700" : "text-gray-700 hover:bg-blue-50"
      } rounded-lg transition-colors duration-200`}
  >
    <a
      href="#"
      className="flex items-center justify-between px-4 py-3"
      onClick={(e) => {
        e.preventDefault();
        onToggle();
        onClick(text);
      }}
    >
      <div className="flex items-center">
        {React.createElement('i', {
          className: `aieicon ${icon} mr-3 ${active ? "text-blue-600" : ""}`,
        })}
        <span>{text}</span>
      </div>
      {subItems &&
        subItems.length > 0 &&
        React.createElement(isOpen ? UpOutlined : DownOutlined, {
          className: `text-sm ${active ? "text-blue-600" : ""}`,
        })}
    </a>
    {isOpen && subItems && subItems.length > 0 && (
      <ul className="ml-6 mt-2"> 
        {subItems.map((subItem, index) => (
          <li key={index} className="mb-2 text-gray-600 hover:text-blue-600">
            <a
              href="#"
              className="block py-2 px-4 hover:bg-blue-50 rounded-lg transition-colors duration-200"
              onClick={(e) => {
                e.preventDefault();
                onClick(`${text} - ${subItem}`);
              }}
            >
              {subItem}
            </a>
          </li>
        ))}
      </ul>
    )}
  </li>
);

const ToolbarButton: any = ({ icon, onClick, label }) => (
  <div className="relative group">
    <button
      className="text-gray-600 hover:text-blue-600 p-2 rounded-full transition-colors duration-200 text-xs flex items-center"
      onClick={onClick}
    >
      {icon}
      <span className="ml-2">{label}</span>
    </button>
  </div>
);

const createMenuItem = (
  icon,
  text,
  activeRule,
  sidebarHidden = true,
  hidden = true
) => {
  return {
    icon,
    text,
    activeRule,
    sidebarHidden, // 是否展示主站左侧栏 true 不展示
    hidden, // 菜单是否展示在主站左侧栏内 true 不展示
  };
};
//二级路由的根目录需要放在最下面。否则有匹配问题
const menuItems: any = [
  createMenuItem('aieicon-terminal-window-fill', "首页", "/workbench", false, false),
  createMenuItem('aieicon-smartphone-fill', "云手机", "/manage/home", false, false),
  createMenuItem('aieicon-mac-fill', "云电脑", "/manage/pc", false, false),
  createMenuItem('aieicon-function-fill', "工作空间", "/app/workspace", false, false),
  createMenuItem('', "套餐", "/main/bill", false, true),
];
// 底部菜单 
const bottomMenuItems = [
  createMenuItem("aieicon-community-line", "社区", "/main/community", false, false),
  createMenuItem("aieicon-book-read-line", "教程", "/main/docs", false, false),
];

// Recursive function to extract paths and create menu items
const extractMenuItems = (menuObject: any) => {
  return Object.entries(menuObject).flatMap(([key, value]) => {
    if (typeof value === 'string') {
      return createMenuItem('', '', value, false, true);
    } else if (typeof value === 'object') {
      return extractMenuItems(value); // Recursive call for nested objects
    }
    return []; // Return an empty array for unexpected types
  });
};

// Generate the menu items based on pathNeedSidebar
const menusNeedSidebar = extractMenuItems(pathNeedSidebar);

const mainRoutes = ["/user", "/main"];

const toolItems = [
  {
    icon: <i className="aieicon aieicon-computer-line"></i>,
    tooltip: "客户端",
  },
  {
    icon: <i className="aieicon aieicon-customer-service-2-line"></i>,
    tooltip: "联系客服",
  },
];

const handleUserRouteClick = async (path) => {
  if (path === "/user/logout") {
    const res = await UserApi.signout({});
    localStorage.removeItem("nc-token");
    localStorage.removeItem("refresh_token");
    window.location.reload();
    console.log(res);
  } else {
    window.location.href = path;
  }
};

const Sidebar = ({ onMenuClick, toolbarClick, setCreatAppOpen }) => {
  const [userInfo, setUserInfo] = useState({} as any);
  const [entitlementSummary, setEntitlementSummary] = useState<any>([]);
  const [currentEntitlementLoading, setCurrentEntitlementLoading] = useState(false);
  const getUser = async () => {
    const res = await UserApi.getUserInfo({});
    const { data } = res;
    setUserInfo(data);
    localStorage.setItem("userInfo", JSON.stringify(data));
  };

  const handleFetchCurrentEntitlement = async () => {
    setCurrentEntitlementLoading(true);
    const res = await getCurrentEntitlement();
    if (res.code === 0) {
      setCurrentEntitlement(res.data);
    }
    setCurrentEntitlementLoading(false);
  };
  // 初始化个人空间
  const handleInitSelfSpace = async () => {
      const res=await getInitSelfSpace()
  }

  const handleFetchEntitlementSummary = async () => {
    const res = await getEntitlementSummary();
    if (res.code === 0) {
      setEntitlementSummary(res.data);
    }
  };

  useEffect(() => {
    if (window.location.pathname !== "/login") {
      const token = localStorage.getItem("nc-token")
      if (token) {
        getUser();
        handleFetchCurrentEntitlement();
        handleInitSelfSpace()
      }
    }
  }, []);
  const Content = () => {
    const menuItems = [
      { icon: UserOutlined, path: "/user/info", label: "用户中心" },
      { icon: SettingOutlined, path: "/workbench/setting", label: "后台" },
      { icon: LogoutOutlined, path: "/user/logout", label: "退出" },
    ];

    return (
      <List
        size="small"
        itemLayout="horizontal"
        dataSource={menuItems}
        renderItem={({ icon, path, label }) => (
          <List.Item
            onClick={() => handleUserRouteClick(path)}
            className="w-[150px] cursor-pointer hover:bg-gray-100 transition-colors duration-200"
          >
            <List.Item.Meta
              avatar={React.createElement(icon)}
              title={<Typography.Text>{label}</Typography.Text>}
            />
          </List.Item>
        )}
      />
    );
  };

  const [openMenus, setOpenMenus] = React.useState({});
  const [activeMenu, setActiveMenu] = React.useState(null);
  const [currentEntitlement, setCurrentEntitlement] = useState<any>(null);

  // 缓存 setCreatAppOpen 函数的引用
  const handleCreateApp = useCallback(() => {
    setCreatAppOpen(true);
  }, [setCreatAppOpen]);

  const toggleMenu = (item, index) => {
    const { activeRule } = item;
    setOpenMenus((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }));
    setActiveMenu(activeRule);
  };

  // 页面初始化时设置activeMenu
  useEffect(() => {
    const currentPath = window.location.pathname;
    const activeIndex = [...menuItems, ...bottomMenuItems].find((item) => 
      currentPath.startsWith(item.activeRule)
    );
    setActiveMenu(activeIndex?.activeRule || '/workbench');
  }, [...menuItems, ...bottomMenuItems]);

  const footerClick = (tooltip) => {
    toolbarClick(tooltip);
  };

  const contentRender = () => {
    return (
      <div className="max-h-[460px] min-w-[340px] max-w-md mx-auto bg-white rounded-lg shadow-md overflow-y-auto scrollbar-hide">
        {entitlementSummary?.length === 0 && (
          <div className="p-10 flex items-center justify-center">
            <Spin tip="加载中..." />
          </div>
        )}
        {entitlementSummary?.length > 0 && entitlementSummary.map((item, index) => (
          <div key={item.productId} className="p-0">
            <VersionUpdateHeader entitlementSummary={item} index={index} />
            <VersionUpdateContent entitlementSummary={item} />
          </div>
        ))}
      </div>
    );
  };

  const handleCheckLoginStatusAndGoToBill = () => {
    if (localStorage.getItem("nc-token")) {
      window.location.href = "/main/bill";
    } else {
      globalStateActions.setGlobalState({ isLoggedIn: false });
    }
  };

  return (
    <div className="w-50 h-screen bg-white shadow-lg flex flex-col max-w-[200px]">
      <div className="flex-grow overflow-y-auto">
        <div className="px-4">
          <img
            src="https://p0.meituan.net/waimaiqact/4a3f93496f499673ac5ec7fa57a72b665152.png"
            alt="AI Worker"
          />
          <nav>
            <Button
              type="primary"
              block
              className="mb-4 flex items-center h-[40px] justify-start px-3 text-[14px] font-[600]"
              onClick={handleCreateApp}
              icon={<i className="aieicon aieicon-add-line ml-6" />}
            >
              创建应用
            </Button>
            <ul>
              {menuItems.map((item, index) =>
                item.hidden ? null : (
                  <MenuItem
                    key={item.activeRule}
                    icon={item.icon}
                    text={item.text}
                    active={activeMenu === item.activeRule}
                    subItems={item.subItems}
                    onToggle={() => toggleMenu(item, index)}
                    isOpen={openMenus[index]}
                    onClick={() => onMenuClick(item)}
                  />
                ),
              )}
            </ul>
            <div>
              <Divider className="px-0 mt-2 mb-2" />
            </div>
            <ul>
              {bottomMenuItems.map((item, index) =>
                item.hidden ? null : (
                  <MenuItem
                    key={item.activeRule}
                    icon={item.icon}
                    text={item.text}
                    active={activeMenu === item.activeRule}
                    onToggle={() => toggleMenu(item, index)}
                    isOpen={openMenus[index]}
                    onClick={() => onMenuClick(item)}
                  />
                ),
              )}
            </ul>
          </nav>
        </div>
      </div>
      {/* 免费版 */}
      {userInfo && userInfo.display_name && (
        <>
          <div className="pl-3 pr-3 pb-2">
            <div
              className="w-full h-[60px] flex flex-row justify-between items-center pb-0 sm:p-2 flex-grow self-stretch bg-gradient-to-r from-blue-300 via-blue-100 to-white rounded-md"
              style={{
                background:
                  "linear-gradient(108deg, rgba(113, 167, 247, 0.16) 4%, rgba(26, 81, 232, 0.04) 47%, rgba(101, 140, 217, 0.2) 100%), #FFFFFF",
              }}
            >
              {!currentEntitlement && !currentEntitlementLoading && (
                <div className="flex items-center justify-center w-full" onClick={(e) => e.preventDefault()}>
                  <span className="text-gray-500">暂无版本信息</span>
                </div>
              )}
              {currentEntitlementLoading && (
                <div className="flex items-center justify-center w-full">
                  <Spin size="small" />
                </div>
              )}
              {currentEntitlement && (
                <Popover
                  onOpenChange={(isOpen) => isOpen && handleFetchEntitlementSummary()}
                  trigger="click"
                  placement="right"
                  title=""
                  content={contentRender}
                  arrow={false}
                  className="version-popover"
                  overlayInnerStyle={{ padding: 0 }}
                >
                  <div className="flex items-center cursor-pointer">
                    <div className="flex items-center">
                      <i className="aieicon aieicon-crown-o-color pr-2 text-[#7C9EFB]" />
                      <div>
                        <Tooltip title={currentEntitlement?.productName}>
                          <p className="text-[14px] truncate max-w-[120px]">{currentEntitlement?.productName}</p>
                        </Tooltip>
                        <p className="text-xs font-normal leading-5 tracking-normal text-[#646971]">
                          截止至:{currentEntitlement?.effectiveEndTime ? dayjs(currentEntitlement.effectiveEndTime).format('YYYY.MM.DD') : ''}
                        </p>
                      </div>
                    </div>
                    <i className="aieicon aieicon-arrow-right-s-line pl-2 text-[#646971]" />
                  </div>
                </Popover>
              )}
            </div>
          </div>
        </>
      )}
      <div className="p-3">
        <Button
          type="primary"
          style={{
            border: "none",
            background: "linear-gradient(104deg, #E5B784 0%, #B67E58 100%)",
          }}
          block
          onClick={handleCheckLoginStatusAndGoToBill}
        >
          <i className="aieicon aieicon-vip-crown-2-fill"></i>
          升级版本
        </Button>
      </div>
      <div className="p-3 border-t border-gray-200">
        {userInfo && userInfo.display_name ? (
          <Popover placement="right" title="" content={Content}>
            <div className="pl-3 pr-3 text-center h-20 flex items-center justify-center">
              <div>
                <Avatar
                  src={
                    userInfo && userInfo.avatar
                      ? userInfo.avatar
                      : "https://s3plus.sankuai.com/aiagent-bucket/20241021/compress_2c8fdbd861146b2511d723fc06e2e540.png"
                  }
                  size={42}
                  className="rounded-full hover:ring-2 hover:ring-blue-500 transition-all duration-300 ease-in-out cursor-pointer"
                />

                <p className="font-medium text-sm text-gray-800">
                  {userInfo ? userInfo.display_name : "未登录"}
                </p>
              </div>
            </div>
          </Popover>
        ) : (
          <Button
            type="primary"
            block
            onClick={() => {
              toolbarClick("登录/注册");
            }}
          >
            登录/注册
          </Button>
        )}
      </div>

      <div className="bg-gray-100 p-2 flex justify-around">
        {toolItems.map((item, index) => (
          <React.Fragment key={index}>
            <ToolbarButton
              icon={item.icon}
              onClick={() => footerClick(item.tooltip)}
              label={item.tooltip}
            />
            {index < toolItems.length - 1 && (
              <div className="border-l border-gray-300 h-full mx-2"></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

const App = () => {
  const [isRouterInMain, setIsRouterInMain] = useState(false);
  const [siddebarHidden, setSidebarHidden] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [creatAppOpen, setCreatAppOpen] = useState(false);
  // const spaceId = JSON.parse(localStorage.getItem("currentWorkspace")).spaceId;
  // console.log(spaceId,'12333');
  useEffect(() => {
    const path = window.location.pathname;
    setIsRouterInMain(mainRoutes.some((route) => path.startsWith(route)));
    const currentmenu = [...menusNeedSidebar, ...menuItems, ...bottomMenuItems].filter((item) => path === item.activeRule);
    setSidebarHidden(!(currentmenu?.length > 0 && !currentmenu[0].sidebarHidden));
    setIsLoading(false);
  }, [window.location.pathname]);

  // 监听路由变化
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isLoading) {
      NProgress.start();
    } else {
      NProgress.done();
    }
  }, [isLoading]);

  const handleMenuClick = (item) => {
    if (item.sidebarHidden) {
      setSidebarHidden(true);
    } else {
      setSidebarHidden(false);
    }
    if (mainRoutes.some((route) => item.activeRule.startsWith(route))) {
      setIsRouterInMain(true);
    } else {
      setIsRouterInMain(false);
    }
    history.pushState(null, item.activeRule, item.activeRule);
  };

  const handleClick = (item) => {
    if (item === "登录/注册") {
      setIsModalOpen(true);
    } else if (item === "客户端") {
      openOrDownloadApp(import.meta.env.VITE_APP_CLIENT_PROTOCOL, import.meta.env.VITE_APP_CLIENT_DOWNLOAD_URL)
    }
  };

  return (
    <div className="flex h-screen w-screen">
      {!siddebarHidden ? (
        <Sidebar 
          onMenuClick={handleMenuClick} 
          toolbarClick={handleClick} 
          setCreatAppOpen={setCreatAppOpen}
        />
      ) : (
        <></>
      )}

      <div className="flex-1 overflow-y-auto border-l bg-main-bg-gradient">
        {isRouterInMain ? <RouteApp /> : <div id="subapp-viewport"></div>}
      </div>

      <ConfigProvider
        theme={{
          components: {
            Modal: {
              // @ts-ignore
              contentPadding: 0,
            },
          },
        }}
      >
        <Modal
          title={null}
          visible={isModalOpen}
          footer={null}
          width={700}
          onCancel={() => setIsModalOpen(false)}
          centered
        >
          <Login />
        </Modal>
      </ConfigProvider>
      <CreatAppModal
        creatAppOpen={creatAppOpen}
        setCreatAppOpen={setCreatAppOpen}
      />
    </div>
  );
};

export default App;
