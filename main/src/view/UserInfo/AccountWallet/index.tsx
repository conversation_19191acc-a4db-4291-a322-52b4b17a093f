import * as React from 'react';
import AccountWallet from './accountWallet';
import BusinessRecord from './businessRecord';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

const AccountWalletPage: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <div className="flex flex-col min-h-screen bg-white w-[80%] min-w-[800px] mx-auto mt-4 rounded-lg">
        <div className="py-9 px-6">
          <AccountWallet />
          <BusinessRecord />
        </div>
      </div>
    </ConfigProvider>
  );
};

export default AccountWalletPage;
