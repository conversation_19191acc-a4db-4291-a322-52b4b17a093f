import React from 'react'
import { Button, Flex, Layout, Space, Table, Descriptions, Card } from 'antd';
import { PlusOutlined, MoreOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const { Header, Content } = Layout;

const tableCols = [
  {
    title: '创建时间',
    dataIndex: 'createDate',
    key: 'createDate',
  },
  {
    title: '应用',
    dataIndex: 'appName',
    key: 'appName',
  },
  {
    title: '令牌',
    dataIndex: 'token',
    key: 'token',
  },
  {
    title: '有效期',
    dataIndex: 'validDate',
    key: 'validDate',
  },
  {
    title: '操作',
    dataIndex: 'key',
    key: 'operation',
    render: (value, row) => {
      return (
        <Space>
          <Button
            size='small'
            icon={<EditOutlined />}
            onClick={() => { }}
          />
          <Button
            size='small'
            icon={<DeleteOutlined />}
            onClick={() => { }}
          />
          <Button
            size='small'
            icon={<MoreOutlined />}
            onClick={() => { }}
          />
        </Space>
      );
    },
  },
];

const mockTableData = [
  {
    key: '1',
    createDate: '9月20日 15:49',
    appName: '用例生成',
    token: 'uKmEtVmW48f6Oflk3GRM1Fn531icdrdi0796MZd3',
    validDate: '永久有效',
  },
  {
    key: '2',
    createDate: '9月20日 15:49',
    appName: '小红书运营',
    token: 'uKmEtVmW48f6Oflk3GRM1Fn531icdrdi0796MZd3',
    validDate: '9月20日～10月20日',
  }
];

const mockUserInfo = [
  {
    key: '1',
    label: '用户姓名',
    children: <p>张三</p>,
  },
  {
    key: '2',
    label: '邮件地址',
    children: <p><EMAIL></p>,
  },
  {
    key: '3',
    label: 'MIS 号',
    children: <p>meituan12345</p>,
  },
  {
    key: '4',
    label: '手机号',
    children: <p>123-4567-8910</p>,
  },
  {
    key: '5',
    label: '密码',
    children: <p>**********</p>,
  }
];


const UserInfo: React.FC = () => (
  <Layout className='h-screen bg-white'>
    <Header className='bg-white h-fit my-4'>
      <p className='text-2xl font-semibold mt-2'>用户中心</p>
    </Header>
    <Content className='px-[50px]'>
      {/* @ts-ignore */}
      <Card className='shadow-[0_2px_6px_0px_rgba(0,0,0,0.1)] mb-4'>
        <Descriptions
          title="用户基本信息"
          items={mockUserInfo}
          column={1}
          extra={
            <a className='text-[#1677ff]'>编辑</a>
          }
        />
      </Card>
      {/* @ts-ignore */}
      <Card className='shadow-[0_2px_6px_0px_rgba(0,0,0,0.1)]'>
        <Flex align='center' justify='space-between' className='mb-4'>
          <p className='font-semibold text-base'>应用 API 令牌</p>
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={() => { }}
          >
            <p className='font-semibold'>添加</p>
          </Button>
        </Flex>
        <Table
          columns={tableCols}
          dataSource={mockTableData}
          pagination={false}
        />
      </Card>
    </Content>
  </Layout>
)

export default UserInfo;


