/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      spacing: {
        "20px": "20px",
        "84px": "84px",
        "96px": "96px",
        "480px": "480px",
      },
      borderRadius: {
        lg: "12px",
        md: "6px",
      },
      colors: {
        "gray-300": "#E5E7EA",
        nc: "#1530C1", // 根据实际颜色调整
        "main-bg-blue": "#F1F0FC",
        "custom-gray": "#646971", // 自定义灰色
        "custom-orange": "#F3682C", //自定义橘色
      },
      backgroundImage: {
        "main-bg-gradient":
          "linear-gradient(135deg, #F1F0FC 0%, white 50%, #F1F0FC 100%)",
        "bill-produce-bg-gradient":
          "linear-gradient(96deg, rgba(108, 189, 255, 0.1) 0%, rgba(33, 55, 255, 0.1) 100%)", // 产品渐变背景
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      addUtilities({
        '.scrollbar-hide': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',
          /* Firefox */
          'scrollbar-width': 'none',
          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      })
    }
  ],
};
