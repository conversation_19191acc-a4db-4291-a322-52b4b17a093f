import { useState, useRef, useCallback, useEffect } from 'react';
import { Message } from 'ai';
import { v4 as uuidv4 } from 'uuid';
import { createContainer } from '@/services/docker-service';
import { callLLMAnalysis } from '@/services/opencode-service';
import { useSSO } from '@/components/providers/sso-provider';

// 扩展Message类型，添加自定义属性
interface ExtendedMessage extends Message {
    isWaiting?: boolean;
    sender?: {
        name?: string;
        avatar?: string;
    };
}

interface MarioWebSocketOptions {
    url: string;
    enabled: boolean;
    onCreateContainer?: (
        containerId: string,
        containerName: string,
        webUrl: string,
        terminalUrl: string,
        webPort: number
    ) => void;
    onMuseInput?: (museInput: { port?: number; repository?: string; user_mis?: string; mario_case?: string }) => void;
    username?: string;
    heartbeatInterval?: number; // 心跳间隔时间，默认5秒
}

interface MarioMessage {
    type?: string;
    message?: string;
    content?: string;
    node?: string;
    status?: string;
    interrupt_type?: string;
    sender?: {
        name?: string;
        avatar?: string;
    };
    muse_input?: {
        port?: number;
        repository?: string;
        user_mis?: string;
        mario_case?: string;
    };
}

// 流式输出配置
interface StreamConfig {
    chunkSize: number; // 每次输出的字符数
    intervalMs: number; // 输出间隔(毫秒)
}

export function useMarioWebSocket({
    url,
    enabled,
    onCreateContainer,
    onMuseInput,
    username = 'muse',
    heartbeatInterval = 30000 // 默认30秒
}: MarioWebSocketOptions) {
    const { userInfo } = useSSO();
    const [isConnected, setIsConnected] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [messages, setMessages] = useState<ExtendedMessage[]>([]);
    const webSocketRef = useRef<WebSocket | null>(null);
    const pendingMessageRef = useRef<string | null>(null);
    // 保存当前中断类型
    const interruptTypeRef = useRef<string | null>(null);
    // 消息队列，用于确保消息按顺序完整显示
    const messageQueueRef = useRef<MarioMessage[]>([]);
    // 标记是否正在处理消息
    const isProcessingRef = useRef<boolean>(false);
    // 标记是否是第一条用户消息
    const isFirstMessageRef = useRef<boolean>(true);
    // 流式输出配置
    const streamConfigRef = useRef<StreamConfig>({
        chunkSize: 8, // 每次输出2个字符
        intervalMs: 25 // 25毫秒输出一次
    });
    // 心跳定时器引用
    const heartbeatTimerRef = useRef<NodeJS.Timeout | null>(null);

    // 停止心跳机制 - 提前定义，避免依赖循环
    const stopHeartbeat = useCallback(() => {
        if (heartbeatTimerRef.current) {
            console.log('停止WebSocket心跳机制');
            clearInterval(heartbeatTimerRef.current);
            heartbeatTimerRef.current = null;
        }
    }, []);

    // 心跳机制：发送心跳消息
    const sendHeartbeat = useCallback(() => {
        if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
            console.log('发送WebSocket心跳消息');
            webSocketRef.current.send(JSON.stringify({ type: 'heartbeat' }));
        } else {
            // 如果连接已关闭，停止心跳
            console.log('WebSocket连接已关闭，停止心跳');
            stopHeartbeat();
        }
    }, [stopHeartbeat]);

    // 启动心跳机制
    const startHeartbeat = useCallback(() => {
        // 先停止可能存在的心跳定时器
        stopHeartbeat();
        console.log('启动WebSocket心跳机制');
        heartbeatTimerRef.current = setInterval(sendHeartbeat, heartbeatInterval);
    }, [sendHeartbeat, heartbeatInterval, stopHeartbeat]);

    // 将完整消息转换为流式输出
    const streamifyMessage = useCallback((message: string, callback: (chunk: string, isLast: boolean) => void) => {
        const { chunkSize, intervalMs } = streamConfigRef.current;
        const totalChunks = Math.ceil(message.length / chunkSize);
        let currentChunk = 0;

        const streamInterval = setInterval(() => {
            if (currentChunk >= totalChunks) {
                clearInterval(streamInterval);
                callback('', true); // 发送空字符串表示结束
                return;
            }

            const start = currentChunk * chunkSize;
            const end = Math.min(start + chunkSize, message.length);
            const chunk = message.substring(start, end);

            callback(chunk, false);
            currentChunk++;
        }, intervalMs);

        // 返回清理函数
        return () => clearInterval(streamInterval);
    }, []);

    // 处理消息队列
    const processMessageQueue = useCallback(() => {
        if (isProcessingRef.current || messageQueueRef.current.length === 0) {
            return;
        }

        isProcessingRef.current = true;
        const data = messageQueueRef.current.shift()!;

        // 获取消息内容
        const content =
            data.type === 'interrupt' ? data.message || '请提供更多信息' : data.content || data.message || '';

        if (!content) {
            isProcessingRef.current = false;
            setTimeout(() => processMessageQueue(), 0);
            return;
        }

        // 如果是中断消息，保存中断类型
        if (data.type === 'interrupt') {
            interruptTypeRef.current = data.interrupt_type || null;
        }

        // 创建新消息，初始显示等待状态
        const messageId = uuidv4();
        setMessages((prev) => [
            ...prev,
            {
                id: messageId,
                role: 'assistant',
                content: '思考中...', // 初始等待状态
                createdAt: new Date(),
                // 添加Mario助手的信息
                sender: {
                    name: 'Mario助手',
                    avatar: '🧙‍♂️' // 使用魔法师emoji作为头像
                },
                // 添加interrupt类型标识
                isInterrupt: data.type === 'interrupt',
                interruptType: data.type === 'interrupt' ? data.interrupt_type : undefined
            }
        ]);

        // 开始流式输出
        let accumulatedContent = '';
        const cleanupStreamify = streamifyMessage(content, (chunk, isLast) => {
            if (!isLast) {
                accumulatedContent += chunk;
                // 更新消息内容
                setMessages((prev) =>
                    prev.map((msg) => (msg.id === messageId ? { ...msg, content: accumulatedContent } : msg))
                );
            } else {
                // 消息处理完成
                isProcessingRef.current = false;
                setIsLoading(false);

                // 标记interrupt类型消息已完成
                if (data.type === 'interrupt') {
                    setMessages((prev) =>
                        prev.map((msg) =>
                            msg.id === messageId
                                ? { ...msg, content: accumulatedContent, isInterruptCompleted: true }
                                : msg
                        )
                    );
                }

                // 清除之前的等待消息（如果存在）
                setMessages((prev) => prev.filter((msg) => !msg.isWaiting));

                // 检查是否是"任务处理完成"消息
                const isTaskCompleted = content.includes('用户本次任务已结束');

                // 添加一个"等待下一条消息"的状态，但不为interrupt类型消息和任务完成消息添加等待状态
                if (messageQueueRef.current.length === 0 && data.type !== 'interrupt' && !isTaskCompleted) {
                    const waitingId = uuidv4();
                    setMessages((prev) => [
                        ...prev,
                        {
                            id: waitingId,
                            role: 'assistant',
                            content: '等待中...', // 简单固定文本
                            createdAt: new Date(),
                            sender: {
                                name: 'Mario助手',
                                avatar: '🧙‍♂️'
                            },
                            isWaiting: true // 标记这是一个等待消息
                        }
                    ]);
                }

                // 处理下一条消息或结束处理
                if (messageQueueRef.current.length > 0) {
                    setTimeout(() => processMessageQueue(), 0);
                }
            }
        });

        // 返回清理函数
        return () => {
            if (typeof cleanupStreamify === 'function') {
                cleanupStreamify();
            }
        };
    }, [streamifyMessage]);

    // 清除等待状态的通用函数
    const clearWaitingState = useCallback(() => {
        // 不再需要清理计时器，因为我们不再使用计时器
        // 只需要移除等待消息
        setMessages((prev) => prev.filter((msg) => !msg.isWaiting));
    }, []);

    // 添加消息到队列
    const addMessageToQueue = useCallback(
        (data: MarioMessage) => {
            // 收到新消息时，清除等待下一条消息的状态和动画
            clearWaitingState();

            messageQueueRef.current.push(data);
            if (!isProcessingRef.current) {
                processMessageQueue();
            }
        },
        [processMessageQueue, clearWaitingState]
    );

    // 处理WebSocket消息
    const handleWebSocketMessage = useCallback(
        (data: MarioMessage) => {
            // 特殊处理reporter_node_chain_end类型的消息
            if (data.type === 'reporter_node_chain_end' && data.muse_input) {
                console.log('收到reporter_node_chain_end消息:', data);
                console.log('muse_input参数:', data.muse_input);

                const { port, repository, user_mis, mario_case } = data.muse_input;
                console.log('提取的参数:');
                console.log('- port (对应remote_port):', port);
                console.log('- repository:', repository);
                console.log('- user_mis:', user_mis);
                console.log('- mario_case:', mario_case);

                // 调用回调函数，将muse_input参数传递给父组件
                if (onMuseInput) {
                    onMuseInput(data.muse_input);
                }
            }

            // 检查是否是markdownor节点且状态为completed
            if (data.node === 'markdownor' && data.status === 'completed') {
                console.log('检测到markdownor节点completed状态，准备打开画布tab');

                // 触发打开画布tab的事件
                const event = new CustomEvent('openCanvasTab', {
                    detail: {
                        message: data.content || data.message || '',
                        timestamp: new Date().toISOString()
                    }
                });
                window.dispatchEvent(event);
            }

            // 检查是否是model_response事件且节点为generator或corrector
            if (data.type === 'model_response' && (data.node === 'generator' || data.node === 'corrector')) {
                console.log(`检测到${data.node}节点的model_response事件，准备打开用例生成tab`);

                // 触发打开用例生成tab的事件
                const event = new CustomEvent('openTestCaseTab', {
                    detail: {
                        message: data.content || data.message || '',
                        node: data.node,
                        timestamp: new Date().toISOString()
                    }
                });
                window.dispatchEvent(event);
            }

            addMessageToQueue(data);
        },
        [addMessageToQueue, onMuseInput]
    );

    // 连接WebSocket
    useEffect(() => {
        if (!enabled || !url) return;

        const connectWebSocket = () => {
            try {
                const ws = new WebSocket(url);
                webSocketRef.current = ws;

                ws.onopen = () => {
                    console.log('Mario WebSocket连接成功');
                    setIsConnected(true);
                    setError(null);

                    // 启动心跳机制
                    startHeartbeat();
                };

                ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (err) {
                        console.error('解析WebSocket消息失败:', err);
                        setError('解析消息失败');
                    }
                };

                ws.onerror = (event) => {
                    console.error('WebSocket错误:', event);
                    setError('WebSocket连接错误');
                    setIsConnected(false);
                    stopHeartbeat();
                };

                ws.onclose = () => {
                    console.log('WebSocket连接关闭');
                    setIsConnected(false);
                    stopHeartbeat();
                };

                return ws;
            } catch (err) {
                console.error('创建WebSocket连接失败:', err);
                setError('创建WebSocket连接失败');
                setIsConnected(false);
                return null;
            }
        };

        const ws = connectWebSocket();

        return () => {
            // 确保先停止心跳，再关闭连接
            stopHeartbeat();
            if (ws && ws.readyState !== WebSocket.CLOSED && ws.readyState !== WebSocket.CLOSING) {
                ws.close();
            }
        };
    }, [url, enabled, handleWebSocketMessage, stopHeartbeat, startHeartbeat]);

    // 处理容器创建
    const handleContainerCreation = useCallback(async () => {
        try {
            setIsLoading(true);

            // 清除之前的等待消息（如果存在）
            setMessages((prev) => prev.filter((msg) => !msg.isWaiting));

            // 添加等待状态消息
            const waitingId = uuidv4();
            setMessages((prev) => [
                ...prev,
                {
                    id: waitingId,
                    role: 'assistant',
                    content: '等待中...', // 简单固定文本
                    createdAt: new Date(),
                    sender: {
                        name: 'Mario助手',
                        avatar: '🧙‍♂️'
                    },
                    isWaiting: true // 标记这是一个等待消息
                }
            ]);

            // 先创建一个临时的容器信息，用于显示创建过程
            const tempContainerId = 'temp-' + uuidv4().slice(0, 8);
            const tempContainerName = '正在创建容器...';

            // 先触发容器面板显示，展示创建过程
            if (onCreateContainer) {
                onCreateContainer(
                    tempContainerId,
                    tempContainerName,
                    '', // 临时空URL
                    '',
                    0
                );
            }

            // 创建容器
            const result = await createContainer(username);

            if (result.success && result.container) {
                const { id, name, webUrl, terminalUrl, webPort } = result.container;

                // 更新为真实的容器信息
                if (onCreateContainer) {
                    onCreateContainer(id, name, webUrl, terminalUrl, webPort);
                }

                // 清除等待消息
                clearWaitingState();

                // 添加容器创建成功消息到队列
                addMessageToQueue({
                    message: `容器已创建成功，环境已准备就绪！`
                });

                // 发送容器端口信息到WebSocket
                if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
                    webSocketRef.current.send(
                        JSON.stringify({
                            input: '是',
                            port: webPort
                        })
                    );
                }
            } else {
                throw new Error(result.error || '创建容器失败');
            }
        } catch (err) {
            console.error('创建容器失败:', err);
            const errorMessage = err instanceof Error ? err.message : '创建容器失败';

            // 清除等待消息
            clearWaitingState();

            // 添加错误消息到队列
            addMessageToQueue({
                message: `创建容器失败: ${errorMessage}`
            });

            setError(errorMessage);
        } finally {
            setIsLoading(false);
        }
    }, [username, onCreateContainer, addMessageToQueue, clearWaitingState]);

    // 发送消息
    const sendMessage = useCallback(
        async (content: string) => {
            if (!isConnected || !webSocketRef.current) {
                pendingMessageRef.current = content;
                setError('WebSocket未连接');
                return;
            }

            try {
                setIsLoading(true);

                // 添加用户消息到消息列表
                setMessages((prev) => [
                    ...prev,
                    {
                        id: uuidv4(),
                        role: 'user',
                        content,
                        createdAt: new Date()
                    }
                ]);

                // 清除之前的等待消息（如果存在）
                setMessages((prev) => prev.filter((msg) => !msg.isWaiting));

                // 检查用户的输入是否为approve类型
                if (interruptTypeRef.current === 'approve') {
                    // 调用LLM分析接口
                    try {
                        const analysisResult = await callLLMAnalysis('approve', content.trim());
                        console.log('LLM分析结果:', analysisResult);
                        if (analysisResult === 'YES') {
                            console.log('LLM分析结果为YES，准备创建容器');
                            console.log('analysisResult:', analysisResult);
                            // 创建容器
                            handleContainerCreation();
                            console.log('handleContainerCreation已调用');
                            return; // 直接返回，不继续执行后续逻辑
                        }
                    } catch (error) {
                        console.error('LLM分析失败:', error);
                        // 重新抛出异常
                        throw error;
                    }
                }

                // 原有逻辑：非approve类型或LLM分析结果不是YES时执行
                // 构建要发送的消息内容
                let messageContent = content;

                // 如果是第一条消息且有用户信息，添加usermis信息
                if (isFirstMessageRef.current && userInfo) {
                    const usermis = userInfo.loginName || userInfo.username;
                    if (usermis) {
                        messageContent = `${content}, usermis：${usermis}`;
                    }
                    isFirstMessageRef.current = false;
                }

                // 发送消息到WebSocket服务器
                webSocketRef.current.send(JSON.stringify({ input: messageContent }));

                // 添加等待状态消息 - 用户发送消息后总是显示等待效果
                const waitingId = uuidv4();
                setMessages((prev) => [
                    ...prev,
                    {
                        id: waitingId,
                        role: 'assistant',
                        content: '等待中...', // 简单固定文本
                        createdAt: new Date(),
                        sender: {
                            name: 'Mario助手',
                            avatar: '🧙‍♂️'
                        },
                        isWaiting: true // 标记这是一个等待消息
                    }
                ]);
            } catch (err) {
                console.error('发送消息失败:', err);
                setError('发送消息失败');
                setIsLoading(false);
            }
        },
        [isConnected, handleContainerCreation]
    );

    // 清空消息
    const clearMessages = useCallback(() => {
        setMessages([]);
        interruptTypeRef.current = null;
        messageQueueRef.current = [];
        isProcessingRef.current = false;
        isFirstMessageRef.current = true; // 重置第一条消息标记

        // 清理等待下一条消息的定时器
        clearWaitingState();
    }, [clearWaitingState]);

    // 设置流式输出配置
    const setStreamConfig = useCallback((config: Partial<StreamConfig>) => {
        streamConfigRef.current = {
            ...streamConfigRef.current,
            ...config
        };
    }, []);

    // 组件卸载时清理资源
    useEffect(() => {
        return () => {
            // 清理WebSocket连接
            if (webSocketRef.current) {
                webSocketRef.current.close();
            }

            // 停止心跳
            stopHeartbeat();

            // 清理等待状态
            clearWaitingState();

            // 清理任何可能的定时器
            isProcessingRef.current = false;
        };
    }, [clearWaitingState, stopHeartbeat]);

    return {
        isConnected,
        isLoading,
        error,
        messages,
        sendMessage,
        clearMessages,
        setStreamConfig
    };
}
