import {
  CopilotRuntime,
  ExperimentalEmptyAdapter,
  copilotRuntimeNextJSAppRouterEndpoint,
} from "@copilotkit/runtime";
import { MastraAgent } from "@ag-ui/mastra"
import { MastraClient } from "@mastra/client-js";
import { NextRequest } from "next/server";

// 1. Base address for the Mastra server
const MASTRA_URL = process.env.MASTRA_URL || "http://localhost:4111";
 
// 2. You can use any service adapter here for multi-agent support.
const serviceAdapter = new ExperimentalEmptyAdapter();

// 3. 缓存 runtime 实例以避免重复创建
let runtimeCache: CopilotRuntime | null = null;

/**
 * 获取或创建 CopilotRuntime 实例
 * @returns Promise<CopilotRuntime>
 */
async function getRuntime(): Promise<CopilotRuntime> {
  if (!runtimeCache) {
    try {
      // 创建 CopilotRuntime 实例并利用 Mastra AG-UI 集成获取远程代理
      runtimeCache = new CopilotRuntime({
        agents: await MastraAgent.getRemoteAgents({
          mastraClient: new MastraClient({ baseUrl: MASTRA_URL }),
        }),
      });
    } catch (error) {
      console.error('Failed to create CopilotRuntime:', error);
      // 如果获取远程代理失败，创建一个没有代理的运行时
      runtimeCache = new CopilotRuntime({
        agents: {}, // 使用空对象而不是空数组，符合 Record<string, AbstractAgent> 类型
      });
    }
  }
  return runtimeCache;
}

// 4. Build a Next.js API route that handles the CopilotKit runtime requests.
export const POST = async (req: NextRequest) => {
  try {
    const runtime = await getRuntime();
    const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
      runtime,
      serviceAdapter,
      endpoint: "/api/copilotkit",
    });
 
    return handleRequest(req);
  } catch (error) {
    console.error('CopilotKit API error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }), 
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};