import { useState, useCallback, useRef, useEffect } from 'react';
import { UIState, DialogState } from '../components/types';

export function useUIState() {
  const [uiState, setUIState] = useState<UIState>({
    activeTab: 'code',
    isFullscreen: false,
    showCreatePanel: false,
    showStopPanel: false,
  });

  const [dialogState, setDialogState] = useState<DialogState>({
    isTestDialogOpen: false,
    isPRDialogOpen: false,
  });

  const tabsContainerRef = useRef<HTMLDivElement>(null);

  // 切换全屏
  const toggleFullscreen = useCallback(() => {
    setUIState(prev => ({ ...prev, isFullscreen: !prev.isFullscreen }));
  }, []);

  // 设置活动标签
  const setActiveTab = useCallback((tab: string) => {
    setUIState(prev => ({ ...prev, activeTab: tab }));
  }, []);

  // 显示创建面板
  const showCreatePanel = useCallback(() => {
    setUIState(prev => ({ ...prev, showCreatePanel: true, showStopPanel: false }));
  }, []);

  // 显示停止面板
  const showStopPanel = useCallback(() => {
    setUIState(prev => ({ ...prev, showStopPanel: true, showCreatePanel: false }));
  }, []);

  // 隐藏面板
  const hidePanels = useCallback(() => {
    setUIState(prev => ({ ...prev, showCreatePanel: false, showStopPanel: false }));
  }, []);

  // 打开测试对话框
  const openTestDialog = useCallback(() => {
    setDialogState(prev => ({ ...prev, isTestDialogOpen: true }));
  }, []);

  // 关闭测试对话框
  const closeTestDialog = useCallback(() => {
    setDialogState(prev => ({ ...prev, isTestDialogOpen: false }));
  }, []);

  // 打开PR对话框
  const openPRDialog = useCallback(() => {
    setDialogState(prev => ({ ...prev, isPRDialogOpen: true }));
  }, []);

  // 关闭PR对话框
  const closePRDialog = useCallback(() => {
    setDialogState(prev => ({ ...prev, isPRDialogOpen: false }));
  }, []);

  // 刷新当前标签
  const refreshCurrentTab = useCallback(() => {
    if (uiState.activeTab === 'code') {
      const event = new CustomEvent('refreshCodeFrame');
      window.dispatchEvent(event);
    } else {
      const event = new CustomEvent('refreshTerminalFrame');
      window.dispatchEvent(event);
    }
  }, [uiState.activeTab]);

  // 处理ESC键退出全屏
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && uiState.isFullscreen) {
        toggleFullscreen();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [uiState.isFullscreen, toggleFullscreen]);

  return {
    uiState,
    dialogState,
    tabsContainerRef,
    toggleFullscreen,
    setActiveTab,
    showCreatePanel,
    showStopPanel,
    hidePanels,
    openTestDialog,
    closeTestDialog,
    openPRDialog,
    closePRDialog,
    refreshCurrentTab,
  };
}