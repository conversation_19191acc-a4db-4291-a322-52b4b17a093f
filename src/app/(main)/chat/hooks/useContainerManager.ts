import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { ContainerInfo, LoadingState } from '../components/types';

export function useContainerManager() {
  const [containerInfo, setContainerInfo] = useState<ContainerInfo>({
    id: null,
    name: null,
    ports: {},
    isHealthy: false,
    webUrl: null,
    terminalUrl: null,
  });

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    isTestLoading: false,
    isPRLoading: false,
    isCreating: false,
    isStopping: false,
    healthCheckInProgress: false,
    loadingMessage: '正在创建容器...',
  });

  const healthCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const healthCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 健康检查函数
  const performHealthCheck = useCallback(async (containerId: string) => {
    try {
      const response = await fetch('/api/container/health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ containerId }),
      });
      const data = await response.json();
      return data.isHealthy;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }, []);

  // 开始健康检查
  const startHealthCheck = useCallback((containerId: string) => {
    setLoadingState(prev => ({ ...prev, healthCheckInProgress: true }));
    
    const checkHealth = async () => {
      const isHealthy = await performHealthCheck(containerId);
      if (isHealthy) {
        setContainerInfo(prev => ({ ...prev, isHealthy: true }));
        setLoadingState(prev => ({ ...prev, healthCheckInProgress: false }));
        if (healthCheckIntervalRef.current) {
          clearInterval(healthCheckIntervalRef.current);
        }
        if (healthCheckTimeoutRef.current) {
          clearTimeout(healthCheckTimeoutRef.current);
        }
      }
    };

    // 立即检查一次
    checkHealth();
    
    // 每5秒检查一次
    healthCheckIntervalRef.current = setInterval(checkHealth, 5000);
    
    // 3分钟后停止检查
    healthCheckTimeoutRef.current = setTimeout(() => {
      if (healthCheckIntervalRef.current) {
        clearInterval(healthCheckIntervalRef.current);
      }
      setLoadingState(prev => ({ ...prev, healthCheckInProgress: false }));
      toast.error('容器健康检查超时，请检查容器状态');
    }, 180000);
  }, [performHealthCheck]);

  // 停止健康检查
  const stopHealthCheck = useCallback(() => {
    if (healthCheckIntervalRef.current) {
      clearInterval(healthCheckIntervalRef.current);
      healthCheckIntervalRef.current = null;
    }
    if (healthCheckTimeoutRef.current) {
      clearTimeout(healthCheckTimeoutRef.current);
      healthCheckTimeoutRef.current = null;
    }
    setLoadingState(prev => ({ ...prev, healthCheckInProgress: false }));
  }, []);

  // 创建容器
  const createContainer = useCallback(async (chatId?: string) => {
    setLoadingState(prev => ({ ...prev, isLoading: true, loadingMessage: '正在创建容器...' }));
    
    try {
      const response = await fetch('/api/container/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chatId }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        const { containerId, containerName, webPort, terminalPort } = data;
        
        setContainerInfo({
          id: containerId,
          name: containerName,
          ports: { webPort, terminalPort },
          isHealthy: false,
          webUrl: `https://web-${webPort}.mario.meituan.com`,
          terminalUrl: `https://terminal-${terminalPort}.mario.meituan.com`,
        });
        
        setLoadingState(prev => ({ ...prev, loadingMessage: '容器创建成功，正在进行健康检查...' }));
        startHealthCheck(containerId);
        
        toast.success('容器创建成功！');
      } else {
        throw new Error(data.error || '创建容器失败');
      }
    } catch (error) {
      console.error('创建容器失败:', error);
      toast.error(error instanceof Error ? error.message : '创建容器失败');
    } finally {
      setLoadingState(prev => ({ ...prev, isLoading: false }));
    }
  }, [startHealthCheck]);

  // 停止容器
  const stopContainer = useCallback(async () => {
    if (!containerInfo.id) return;
    
    setLoadingState(prev => ({ ...prev, isLoading: true, loadingMessage: '正在停止容器...' }));
    
    try {
      const response = await fetch('/api/container/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ containerId: containerInfo.id }),
      });
      
      if (response.ok) {
        // 清理状态
        setContainerInfo({
          id: null,
          name: null,
          ports: {},
          isHealthy: false,
          webUrl: null,
          terminalUrl: null,
        });
        
        stopHealthCheck();
        toast.success('容器已停止');
      } else {
        const data = await response.json();
        throw new Error(data.error || '停止容器失败');
      }
    } catch (error) {
      console.error('停止容器失败:', error);
      toast.error(error instanceof Error ? error.message : '停止容器失败');
    } finally {
      setLoadingState(prev => ({ ...prev, isLoading: false }));
    }
  }, [containerInfo.id, stopHealthCheck]);

  // 刷新容器信息
  const refreshContainer = useCallback(async () => {
    if (!containerInfo.id) return;
    
    try {
      const response = await fetch('/api/container/info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ containerId: containerInfo.id }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setContainerInfo(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('刷新容器信息失败:', error);
    }
  }, [containerInfo.id]);

  // 清理函数
  useEffect(() => {
    return () => {
      stopHealthCheck();
    };
  }, [stopHealthCheck]);

  return {
    containerInfo,
    loadingState,
    createContainer,
    stopContainer,
    refreshContainer,
    startHealthCheck,
    stopHealthCheck,
  };
}