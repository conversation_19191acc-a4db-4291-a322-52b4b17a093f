'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Button } from '@/components/ui/button';
import { Copy, Download } from 'lucide-react';
import { useFileOperations } from '../hooks/useFileOperations';

interface CodePreviewProps {
  content: string;
}

export function CodePreview({ content }: CodePreviewProps) {
  const { copyToClipboard, downloadCode, getContentStats } = useFileOperations();
  const { characterCount, codeBlockCount, codeBlocks } = getContentStats(content);

  const handleCopyAll = () => {
    const allCode = codeBlocks.join('\n\n');
    copyToClipboard(allCode);
  };

  const handleDownloadAll = () => {
    const allCode = codeBlocks.join('\n\n');
    downloadCode(allCode, 'generated-code.txt');
  };

  return (
    <div className='flex h-full flex-col bg-white dark:bg-gray-900'>
      {/* 头部工具栏 */}
      <div className='flex items-center justify-between border-b bg-gray-50 px-4 py-2 dark:border-gray-700 dark:bg-gray-800'>
        <div className='flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400'>
          <span>字符数: {characterCount.toLocaleString()}</span>
          <span>代码块: {codeBlockCount}</span>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleCopyAll}
            disabled={codeBlockCount === 0}
            className='flex items-center gap-1.5'>
            <Copy className='h-4 w-4' />
            复制全部
          </Button>
          <Button
            variant='outline'
            size='sm'
            onClick={handleDownloadAll}
            disabled={codeBlockCount === 0}
            className='flex items-center gap-1.5'>
            <Download className='h-4 w-4' />
            下载
          </Button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className='flex-1 overflow-auto p-4'>
        <div className='prose prose-sm max-w-none dark:prose-invert'>
          <ReactMarkdown
            components={{
            code({ inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              const codeContent = String(children).replace(/\n$/, '');
              
              return !inline && match ? (
                <div className='relative'>
                  <div className='absolute right-2 top-2 z-10'>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => copyToClipboard(codeContent)}
                      className='h-8 w-8 p-0 text-gray-400 hover:text-gray-600'>
                      <Copy className='h-3.5 w-3.5' />
                    </Button>
                  </div>
                  <SyntaxHighlighter
                    style={oneDark}
                    language={match[1]}
                    PreTag='div'
                    className='rounded-md'
                    {...props}>
                    {codeContent}
                  </SyntaxHighlighter>
                </div>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },
            h1: ({ children }) => (
              <h1 className='mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100'>
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 className='mb-3 text-xl font-semibold text-gray-900 dark:text-gray-100'>
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 className='mb-2 text-lg font-medium text-gray-900 dark:text-gray-100'>
                {children}
              </h3>
            ),
            p: ({ children }) => (
              <p className='mb-4 leading-relaxed text-gray-700 dark:text-gray-300'>
                {children}
              </p>
            ),
            ul: ({ children }) => (
              <ul className='mb-4 list-disc pl-6 text-gray-700 dark:text-gray-300'>
                {children}
              </ul>
            ),
            ol: ({ children }) => (
              <ol className='mb-4 list-decimal pl-6 text-gray-700 dark:text-gray-300'>
                {children}
              </ol>
            ),
            li: ({ children }) => (
              <li className='mb-1'>{children}</li>
            ),
            blockquote: ({ children }) => (
              <blockquote className='border-l-4 border-blue-500 bg-blue-50 p-4 italic text-blue-900 dark:bg-blue-900/20 dark:text-blue-100'>
                {children}
              </blockquote>
            ),
            table: ({ children }) => (
              <div className='mb-4 overflow-x-auto'>
                <table className='min-w-full border-collapse border border-gray-300 dark:border-gray-600'>
                  {children}
                </table>
              </div>
            ),
            th: ({ children }) => (
              <th className='border border-gray-300 bg-gray-100 px-4 py-2 text-left font-semibold dark:border-gray-600 dark:bg-gray-700'>
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className='border border-gray-300 px-4 py-2 dark:border-gray-600'>
                {children}
              </td>
            ),
          }}>}})>
            {content}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
}