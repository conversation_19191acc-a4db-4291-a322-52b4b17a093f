'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Play, Square } from 'lucide-react';
import { useContainerManager } from '../hooks/useContainerManager';
import { useFileOperations } from '../hooks/useFileOperations';
import { useApiOperations } from '../hooks/useApiOperations';
import { useUIState } from '../hooks/useUIState';
import { CodePreview } from './CodePreview';
import { ContainerInfo } from './ContainerInfo';
import { ContainerOperationPanel } from './ContainerOperationPanel';
import { ContainerWorkspace } from './ContainerWorkspace';
import { MuseInput } from './types';

// 动态导入聊天组件
const ChatContainer = dynamic(() => import('@/components/ChatContainer'), {
  ssr: false,
});

interface MainContentProps {
  isMarioMode: boolean;
}

export function MainContent({ isMarioMode }: MainContentProps) {
  const [museInput, setMuseInput] = useState<MuseInput | null>(null);
  const [generatedContent, setGeneratedContent] = useState<string>('');

  // 使用自定义hooks
  const {
    containerInfo,
    loadingState,
    createContainer,
    stopContainer,
    refreshContainer,
  } = useContainerManager();

  const { executeTest, createPR } = useApiOperations();
  
  const {
    uiState,
    dialogState,
    tabsContainerRef,
    toggleFullscreen,
    setActiveTab,
    showCreatePanel,
    showStopPanel,
    hidePanels,
    openTestDialog,
    closeTestDialog,
    openPRDialog,
    closePRDialog,
    refreshCurrentTab,
  } = useUIState();

  // 构建容器URL
  const webUrl = containerInfo?.isHealthy && containerInfo.ports?.code
    ? `http://localhost:${containerInfo.ports.code}`
    : '';
  
  const terminalUrl = containerInfo?.isHealthy && containerInfo.ports?.terminal
    ? `http://localhost:${containerInfo.ports.terminal}`
    : '';

  // 处理容器操作
  const handleCreateContainer = async () => {
    await createContainer();
    hidePanels();
  };

  const handleStopContainer = async () => {
    await stopContainer();
    hidePanels();
  };

  // 处理API操作
  const handleExecuteTest = async () => {
    try {
      await executeTest(museInput, (loading) => {
        // 这里可以添加测试加载状态的处理
      });
      closeTestDialog();
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  const handleCreatePR = async () => {
    try {
      await createPR(museInput, (loading) => {
        // 这里可以添加PR加载状态的处理
      });
      closePRDialog();
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  // 监听聊天消息更新
  useEffect(() => {
    const handleMuseInputUpdate = (event: CustomEvent) => {
      setMuseInput(event.detail);
    };

    const handleContentUpdate = (event: CustomEvent) => {
      setGeneratedContent(event.detail);
    };

    window.addEventListener('museInputUpdate', handleMuseInputUpdate as EventListener);
    window.addEventListener('contentUpdate', handleContentUpdate as EventListener);

    return () => {
      window.removeEventListener('museInputUpdate', handleMuseInputUpdate as EventListener);
      window.removeEventListener('contentUpdate', handleContentUpdate as EventListener);
    };
  }, []);

  // Mario模式布局
  if (isMarioMode) {
    return (
      <div className='flex h-screen'>
        {/* 左侧聊天区域 */}
        <div className='w-1/2 border-r'>
          <ChatContainer />
        </div>

        {/* 右侧面板区域 */}
        <div className='flex w-1/2 flex-col'>
          <ResizablePanelGroup direction='vertical' className='h-full'>
            {/* 上半部分：代码预览和容器信息 */}
            <ResizablePanel defaultSize={50} minSize={30}>
              <Tabs defaultValue='code' className='h-full'>
                <div className='border-b px-4 py-2'>
                  <TabsList>
                    <TabsTrigger value='code'>代码预览</TabsTrigger>
                    <TabsTrigger value='info'>用例信息</TabsTrigger>
                  </TabsList>
                </div>
                
                <TabsContent value='code' className='h-full m-0 p-0'>
                  <CodePreview content={generatedContent} />
                </TabsContent>
                
                <TabsContent value='info' className='h-full m-0 p-0'>
                  <ContainerInfo
                    containerInfo={containerInfo}
                    museInput={museInput}
                    loadingState={loadingState}
                    dialogState={dialogState}
                    onExecuteTest={handleExecuteTest}
                    onCreatePR={handleCreatePR}
                    onOpenTestDialog={openTestDialog}
                    onCloseTestDialog={closeTestDialog}
                    onOpenPRDialog={openPRDialog}
                    onClosePRDialog={closePRDialog}
                  />
                </TabsContent>
              </Tabs>
            </ResizablePanel>

            <ResizableHandle />

            {/* 下半部分：容器操作区域 */}
            <ResizablePanel defaultSize={50} minSize={30}>
              {/* 容器操作面板或工作区 */}
              {(uiState.showCreatePanel || uiState.showStopPanel) ? (
                <ContainerOperationPanel
                  containerInfo={containerInfo}
                  loadingState={loadingState}
                  showCreatePanel={uiState.showCreatePanel}
                  showStopPanel={uiState.showStopPanel}
                  onCreateContainer={handleCreateContainer}
                  onStopContainer={handleStopContainer}
                />
              ) : (
                <div className='flex h-full flex-col'>
                  {/* 容器控制按钮 */}
                  <div className='border-b px-4 py-2'>
                    <div className='flex items-center justify-between'>
                      <div className='text-sm font-medium'>开发环境</div>
                      <div className='flex gap-2'>
                        {!containerInfo ? (
                          <Button size='sm' onClick={showCreatePanel}>
                            <Play className='mr-1 h-3 w-3' />
                            创建容器
                          </Button>
                        ) : (
                          <Button size='sm' variant='destructive' onClick={showStopPanel}>
                            <Square className='mr-1 h-3 w-3' />
                            停止容器
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* 容器工作区 */}
                  <div className='flex-1'>
                    <ContainerWorkspace
                      containerInfo={containerInfo}
                      uiState={uiState}
                      webUrl={webUrl}
                      terminalUrl={terminalUrl}
                      onTabChange={setActiveTab}
                      onRefresh={refreshCurrentTab}
                      onToggleFullscreen={toggleFullscreen}
                      tabsContainerRef={tabsContainerRef}
                    />
                  </div>
                </div>
              )}
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    );
  }

  // 非Mario模式布局
  return (
    <div className='flex h-screen'>
      {/* 左侧聊天区域 */}
      <div className='w-1/2 border-r'>
        <ChatContainer />
      </div>

      {/* 右侧容器操作区域 */}
      <div className='w-1/2'>
        {(uiState.showCreatePanel || uiState.showStopPanel) ? (
          <ContainerOperationPanel
            containerInfo={containerInfo}
            loadingState={loadingState}
            showCreatePanel={uiState.showCreatePanel}
            showStopPanel={uiState.showStopPanel}
            onCreateContainer={handleCreateContainer}
            onStopContainer={handleStopContainer}
          />
        ) : (
          <div className='flex h-full flex-col'>
            {/* 容器信息头部 */}
            {containerInfo && (
              <div className='border-b px-4 py-3'>
                <div className='flex items-center justify-between'>
                  <div>
                    <div className='font-medium'>{containerInfo.name}</div>
                    <div className='text-sm text-gray-500'>
                      ID: {containerInfo.id} | 端口: {containerInfo.ports?.code || 'N/A'}
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <div className='text-sm text-gray-500'>
                      CPU: {containerInfo.cpu || 'N/A'} | 内存: {containerInfo.memory || 'N/A'}
                    </div>
                    <Button size='sm' variant='destructive' onClick={showStopPanel}>
                      <Square className='mr-1 h-3 w-3' />
                      停止
                    </Button>
                  </div>
                </div>
              </div>
            )}
            
            {/* 容器工作区 */}
            <div className='flex-1'>
              {!containerInfo ? (
                <div className='flex h-full items-center justify-center'>
                  <Button onClick={showCreatePanel}>
                    <Play className='mr-2 h-4 w-4' />
                    创建开发容器
                  </Button>
                </div>
              ) : (
                <ContainerWorkspace
                  containerInfo={containerInfo}
                  uiState={uiState}
                  webUrl={webUrl}
                  terminalUrl={terminalUrl}
                  onTabChange={setActiveTab}
                  onRefresh={refreshCurrentTab}
                  onToggleFullscreen={toggleFullscreen}
                  tabsContainerRef={tabsContainerRef}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}