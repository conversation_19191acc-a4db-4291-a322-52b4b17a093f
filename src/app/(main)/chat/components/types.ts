// 共享类型定义
export interface MuseInput {
  mario_case?: string;
  repository?: string;
  user_mis?: string;
}

export interface ContainerPorts {
  webPort?: string;
  terminalPort?: string;
}

export interface ContainerInfo {
  id: string | null;
  name: string | null;
  ports: ContainerPorts;
  isHealthy: boolean;
  webUrl: string | null;
  terminalUrl: string | null;
  cpu?: string;
  memory?: string;
}

export interface LoadingState {
  isLoading: boolean;
  isTestLoading: boolean;
  isPRLoading: boolean;
  isCreating: boolean;
  isStopping: boolean;
  healthCheckInProgress: boolean;
  loadingMessage: string;
}

export interface DialogState {
  isTestDialogOpen: boolean;
  isPRDialogOpen: boolean;
}

export interface UIState {
  activeTab: string;
  isFullscreen: boolean;
  showCreatePanel: boolean;
  showStopPanel: boolean;
}