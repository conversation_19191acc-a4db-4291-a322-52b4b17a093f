import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { weatherTool } from "@/mastra/tools";
import { getOpenAIConfig } from "@/mastra/config";

/**
 * 获取OpenAI配置并创建客户端
 */
const openAIConfig = getOpenAIConfig();
const customOpenAI = openai({
  apiKey: openAIConfig.apiKey,
  baseURL: openAIConfig.baseURL,
});

/**
 * 天气助手Agent
 * 使用自定义OpenAI配置和指定的模型
 */
export const weatherAgent = new Agent({
  name: "Weather Agent",
  tools: { weatherTool },
  model: customOpenAI(openAIConfig.model),
  instructions: `
      You are a helpful assistant.
`,
});
