/**
 * AI配置文件
 * 集中管理OpenAI和其他AI服务的配置
 */

/**
 * OpenAI配置接口
 */
export interface OpenAIConfig {
  apiKey: string;
  baseURL: string;
  model: string;
}

/**
 * 获取OpenAI配置
 * 从环境变量中读取配置信息
 */
export const getOpenAIConfig = (): OpenAIConfig => {
  const apiKey = process.env.OPENAI_API_KEY;
  const baseURL = process.env.OPENAI_BASE_URL;
  const model = process.env.OPENAI_MODEL || 'gpt-4o-2024-11-20';

  if (!apiKey) {
    throw new Error('OPENAI_API_KEY environment variable is required');
  }

  if (!baseURL) {
    throw new Error('OPENAI_BASE_URL environment variable is required');
  }

  return {
    apiKey,
    baseURL,
    model,
  };
};

/**
 * 默认的OpenAI配置
 */
export const DEFAULT_OPENAI_CONFIG: Partial<OpenAIConfig> = {
  model: 'gpt-4o-2024-11-20',
  baseURL: 'https://aigc.sankuai.com/v1/openai/native/',
};