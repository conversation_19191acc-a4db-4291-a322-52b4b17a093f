import { createTool } from "@mastra/core/tools";
import { createOpenAI } from "@ai-sdk/openai";
import { generateText } from "ai";

import { z } from "zod";

// 配置自定义的 OpenAI 客户端
const customOpenAI = createOpenAI({
  apiKey: "1914304559263223873",
  baseURL: "https://aigc.sankuai.com/v1/openai/native/",
});

const images = ["Bonsai_Tree_Potted_Japanese_Art_Green_Foliage.jpeg", "Cherry_Blossoms_Sakura_Night_View_City_Lights_Japan.jpg", "Ginkaku-ji_Silver_Pavilion_Kyoto_Japanese_Garden_Pond_Reflection.jpg", "Itsukushima_Shrine_Miyajima_Floating_Torii_Gate_Sunset_Long_Exposure.jpg", "Mount_Fuji_Lake_Reflection_Cherry_Blossoms_Sakura_Spring.jpg", "Osaka_Castle_Turret_Stone_Wall_Pine_Trees_Daytime.jpg", "Senso-ji_Temple_Asakusa_Cherry_Blossoms_Kimono_Umbrella.jpg", "Shirakawa-go_Gassho-zukuri_Thatched_Roof_Village_Aerial_View.jpg", "Takachiho_Gorge_Waterfall_River_Lush_Greenery_Japan.jpg", "Tokyo_Skyline_Night_Tokyo_Tower_Mount_Fuji_View.jpg"];

// 生成俳句的辅助函数
const generateHaiku = async (topic: string) => {
  const LLM = customOpenAI("gpt-4o-2024-11-20");
  
  // 随机选择3张图片
  const selectedImages = images.sort(() => 0.5 - Math.random()).slice(0, 3);
  
  const response = await generateText({
    model: LLM,
    prompt: `Create a haiku about "${topic}". 
    Return the response in this exact JSON format:
    {
      "japanese": ["line1", "line2", "line3"],
      "english": ["line1", "line2", "line3"]
    }
    
    Make sure the haiku follows the traditional 5-7-5 syllable pattern.
    The Japanese version should be authentic and poetic.
    The English version should be a beautiful translation.`,
  });
  
  try {
    const haikuData = JSON.parse(response.text);
    return {
      japanese: haikuData.japanese,
      english: haikuData.english,
      image_names: selectedImages
    };
  } catch (error) {
    console.error("Failed to parse haiku JSON:", error);
    // 返回默认俳句
    return {
      japanese: ["春の風", "桜散りゆく", "静寂かな"],
      english: ["Spring wind blows", "Cherry blossoms falling", "In peaceful silence"],
      image_names: selectedImages
    };
  }
};

// 提取俳句主题的工具
export const haikuTopicTool = createTool({
  id: "haikuTopicTool",
  description: "Extract the haiku topic from the user's message",
  inputSchema: z.object({
    topic: z.string().describe("The topic for the haiku"),
  }),
  outputSchema: z.string(),
  execute: async ({ context: { topic } }) => {
    console.log("Extracted haiku topic:", topic);
    return topic;
  },
});

// 生成俳句的工具
export const haikuGenerateTool = createTool({
  id: "haikuGenerateTool",
  description: `Generate a haiku about a given topic with 3 selected images from the available list.`,
  inputSchema: z.object({
    topic: z.string().describe("The topic for the haiku"),
  }),
  outputSchema: z.object({
    japanese: z.array(z.string()).describe("Three lines of Japanese haiku"),
    english: z.array(z.string()).describe("Three lines of English haiku"),
    image_names: z.array(z.string()).describe("Three image names for the haiku"),
  }),
  execute: async ({ context: { topic } }) => {
    console.log("Generating haiku for topic:", topic);
    const haiku = await generateHaiku(topic);
    return haiku;
  },
});