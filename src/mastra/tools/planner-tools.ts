import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { generateText } from "ai";
import { openai } from "@ai-sdk/openai";

/**
 * 任务创建工具 - 创建和管理项目任务
 */
export const taskCreationTool = createTool({
  id: "taskCreationTool",
  description: "创建项目任务，包括任务分解和详细规划",
  inputSchema: z.object({
    projectName: z.string().describe("项目名称"),
    description: z.string().describe("项目描述"),
    requirements: z.array(z.string()).describe("项目需求列表")
  }),
  outputSchema: z.object({
    tasks: z.array(z.object({
      id: z.string(),
      title: z.string(),
      description: z.string(),
      priority: z.enum(["high", "medium", "low"]),
      estimatedHours: z.number(),
      dependencies: z.array(z.string()),
      status: z.enum(["pending", "in-progress", "completed"])
    })),
    timeline: z.string(),
    totalEstimatedHours: z.number()
  }),
  execute: async ({ context: { projectName, description, requirements } }) => {
    console.log(`正在为项目 "${projectName}" 创建任务`);
    
    const LLM = openai("gpt-4o");
    
    try {
      const prompt = `
        请为以下项目创建详细的任务分解：
        项目名称：${projectName}
        项目描述：${description}
        需求列表：${requirements.join(', ')}
        
        请将项目分解为具体的任务，每个任务包含：
        - 任务标题
        - 详细描述
        - 优先级（high/medium/low）
        - 预估工时（小时）
        - 依赖关系
        
        请以结构化的方式返回结果。
      `;
      
      const response = await generateText({
        model: LLM,
        prompt: prompt
      });
      
      // 模拟任务创建（实际应用中可以解析AI响应）
      const tasks = requirements.map((req, index) => ({
        id: `task-${index + 1}`,
        title: `实现${req}`,
        description: `详细实现${req}功能，包括设计、开发和测试`,
        priority: index === 0 ? "high" as const : index === 1 ? "medium" as const : "low" as const,
        estimatedHours: Math.floor(Math.random() * 20) + 5,
        dependencies: index > 0 ? [`task-${index}`] : [],
        status: "pending" as const
      }));
      
      const totalEstimatedHours = tasks.reduce((sum, task) => sum + task.estimatedHours, 0);
      const timeline = `预计完成时间：${Math.ceil(totalEstimatedHours / 8)}个工作日`;
      
      return {
        tasks,
        timeline,
        totalEstimatedHours
      };
    } catch (error) {
      console.error('任务创建失败:', error);
      return {
        tasks: [],
        timeline: "无法生成时间线",
        totalEstimatedHours: 0
      };
    }
  }
});

/**
 * 优先级排序工具 - 对任务进行优先级排序
 */
export const prioritizationTool = createTool({
  id: "prioritizationTool",
  description: "根据多种因素对任务进行优先级排序",
  inputSchema: z.object({
    tasks: z.array(z.object({
      id: z.string(),
      title: z.string(),
      impact: z.number().min(1).max(10).describe("影响程度 1-10"),
      urgency: z.number().min(1).max(10).describe("紧急程度 1-10"),
      effort: z.number().min(1).max(10).describe("工作量 1-10")
    })),
    criteria: z.array(z.string()).optional().describe("额外的排序标准")
  }),
  outputSchema: z.object({
    prioritizedTasks: z.array(z.object({
      id: z.string(),
      title: z.string(),
      priority: z.enum(["critical", "high", "medium", "low"]),
      score: z.number(),
      reasoning: z.string()
    })),
    recommendations: z.array(z.string())
  }),
  execute: async ({ context: { tasks, criteria = [] } }) => {
    console.log(`正在对${tasks.length}个任务进行优先级排序`);
    
    // 计算优先级分数（影响 * 紧急程度 / 工作量）
    const prioritizedTasks = tasks.map(task => {
      const score = (task.impact * task.urgency) / task.effort;
      let priority: "critical" | "high" | "medium" | "low";
      
      if (score >= 8) priority = "critical";
      else if (score >= 6) priority = "high";
      else if (score >= 4) priority = "medium";
      else priority = "low";
      
      const reasoning = `影响程度${task.impact}/10，紧急程度${task.urgency}/10，工作量${task.effort}/10，综合评分${score.toFixed(2)}`;
      
      return {
        id: task.id,
        title: task.title,
        priority,
        score,
        reasoning
      };
    }).sort((a, b) => b.score - a.score);
    
    const recommendations = [
      "优先处理critical和high优先级的任务",
      "考虑将大型任务分解为更小的子任务",
      "定期重新评估任务优先级",
      "关注任务之间的依赖关系"
    ];
    
    if (criteria.length > 0) {
      recommendations.push(`已考虑额外标准：${criteria.join(', ')}`);
    }
    
    return {
      prioritizedTasks,
      recommendations
    };
  }
});

/**
 * 项目规划工具 - 生成完整的项目计划
 */
export const projectPlanningTool = createTool({
  id: "projectPlanningTool",
  description: "生成完整的项目计划，包括里程碑和时间线",
  inputSchema: z.object({
    projectName: z.string().describe("项目名称"),
    objectives: z.array(z.string()).describe("项目目标"),
    constraints: z.array(z.string()).describe("项目约束条件"),
    teamSize: z.number().describe("团队规模"),
    deadline: z.string().optional().describe("项目截止日期")
  }),
  outputSchema: z.object({
    projectPlan: z.object({
      phases: z.array(z.object({
        name: z.string(),
        description: z.string(),
        duration: z.string(),
        deliverables: z.array(z.string())
      })),
      milestones: z.array(z.object({
        name: z.string(),
        date: z.string(),
        criteria: z.array(z.string())
      })),
      riskAssessment: z.array(z.object({
        risk: z.string(),
        probability: z.string(),
        impact: z.string(),
        mitigation: z.string()
      }))
    }),
    recommendations: z.array(z.string())
  }),
  execute: async ({ context: { projectName, objectives, constraints, teamSize, deadline } }) => {
    console.log(`正在为项目 "${projectName}" 生成完整规划`);
    
    const LLM = openai("gpt-4o");
    
    try {
      const prompt = `
        请为以下项目生成详细的项目计划：
        项目名称：${projectName}
        项目目标：${objectives.join(', ')}
        约束条件：${constraints.join(', ')}
        团队规模：${teamSize}人
        ${deadline ? `截止日期：${deadline}` : ''}
        
        请包含项目阶段、里程碑和风险评估。
      `;
      
      const response = await generateText({
        model: LLM,
        prompt: prompt
      });
      
      // 模拟项目计划生成
      const projectPlan = {
        phases: [
          {
            name: "需求分析阶段",
            description: "收集和分析项目需求，制定详细规格说明",
            duration: "2-3周",
            deliverables: ["需求文档", "技术规格说明", "项目范围定义"]
          },
          {
            name: "设计阶段",
            description: "系统架构设计和详细设计",
            duration: "3-4周",
            deliverables: ["系统架构图", "数据库设计", "UI/UX设计"]
          },
          {
            name: "开发阶段",
            description: "核心功能开发和实现",
            duration: "6-8周",
            deliverables: ["核心模块", "API接口", "前端界面"]
          },
          {
            name: "测试阶段",
            description: "系统测试和质量保证",
            duration: "2-3周",
            deliverables: ["测试报告", "缺陷修复", "性能优化"]
          }
        ],
        milestones: [
          {
            name: "需求确认",
            date: "第3周",
            criteria: ["需求文档审批通过", "技术方案确定"]
          },
          {
            name: "设计完成",
            date: "第7周",
            criteria: ["架构设计审核通过", "原型验证完成"]
          },
          {
            name: "开发完成",
            date: "第15周",
            criteria: ["所有功能开发完成", "代码审查通过"]
          },
          {
            name: "项目交付",
            date: "第18周",
            criteria: ["测试通过", "用户验收完成", "文档交付"]
          }
        ],
        riskAssessment: [
          {
            risk: "需求变更",
            probability: "中等",
            impact: "高",
            mitigation: "建立变更控制流程，定期需求评审"
          },
          {
            risk: "技术难题",
            probability: "中等",
            impact: "中等",
            mitigation: "技术预研，专家咨询，备选方案"
          },
          {
            risk: "资源不足",
            probability: "低",
            impact: "高",
            mitigation: "资源预留，外部支持，优先级调整"
          }
        ]
      };
      
      const recommendations = [
        "建议采用敏捷开发方法，分阶段交付",
        "定期举行项目评审会议",
        "建立有效的沟通机制",
        "重视风险管理和质量控制",
        `根据${teamSize}人的团队规模，合理分配任务`
      ];
      
      if (deadline) {
        recommendations.push(`注意截止日期${deadline}，合理安排进度`);
      }
      
      return {
        projectPlan,
        recommendations
      };
    } catch (error) {
      console.error('项目规划生成失败:', error);
      return {
        projectPlan: {
          phases: [],
          milestones: [],
          riskAssessment: []
        },
        recommendations: ["项目规划生成失败，请稍后重试"]
      };
    }
  }
});

/**
 * 任务状态更新工具 - 更新任务完成状态
 */
export const taskStatusTool = createTool({
  id: "taskStatusTool",
  description: "更新任务的完成状态和进度",
  inputSchema: z.object({
    taskId: z.string().describe("任务ID"),
    status: z.enum(["pending", "in-progress", "completed", "blocked"]).describe("新的任务状态"),
    progress: z.number().min(0).max(100).optional().describe("完成进度百分比"),
    notes: z.string().optional().describe("状态更新说明")
  }),
  outputSchema: z.object({
    success: z.boolean(),
    message: z.string(),
    updatedTask: z.object({
      id: z.string(),
      status: z.string(),
      progress: z.number(),
      lastUpdated: z.string()
    })
  }),
  execute: async ({ context: { taskId, status, progress = 0, notes } }) => {
    console.log(`正在更新任务 ${taskId} 的状态为 ${status}`);
    
    // 模拟任务状态更新
    const updatedTask = {
      id: taskId,
      status,
      progress: status === "completed" ? 100 : progress,
      lastUpdated: new Date().toISOString()
    };
    
    const message = `任务 ${taskId} 状态已更新为 ${status}${notes ? `，备注：${notes}` : ''}`;
    
    return {
      success: true,
      message,
      updatedTask
    };
  }
});