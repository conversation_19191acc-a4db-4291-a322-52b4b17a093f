import axiosInstance from "./axiosInsance";

// Define response type for better type safety
interface ApiResponse<T = any> {
  status: number;
  data: T;
}

const handleCommonResponse = (response: ApiResponse) => {
  if (response.status === 200) {
    return response.data;
  }
  return response;
};

// Set default header for all requests
// axiosInstance.defaults.headers.common['xc-auth'] = "lYXRvcixzdXBlciIsInRva2VuX3ZlcnNpb24iOiI0ODcwYjk1MjUxNjM4NjVmYThiOGQyOWY1ZmMyZDgwZjgyY2Y4Yzc4ZTYxMzY2NWRkOWE5ZTRjNWZjMjA5MWRmMTg2ZjRmZTM5MDNlMjQ2ZiIsImlhdCI6MTczMzgzMTgzNCwiZXhwIjoxNzMzODY3ODM0fQ.n8RC5OKqDNaDai7A4c3Z7PmJzi_SjwxRZN-1zE4-XiE";

//获取元素库树状结构
export const getElementTreeApi = async (spaceId:string) => {
  const response = await axiosInstance.get(`/market/open/v1/element/elementTree?spaceId=${spaceId}`)
  return handleCommonResponse(response);
};

//编辑分组
export const editGroupApi = async (params: any) => {
  const response = await axiosInstance.post('/market/api/grouping/updateGrouping', params);
  return handleCommonResponse(response);
};
//编辑页面
export const editPageApi = async (params: any) => {
  const response = await axiosInstance.post('/market/api/page/updatePage', params);
  return handleCommonResponse(response);
};
//编辑元素库
export const editElementApi = async (params: any) => {
  const response = await axiosInstance.post('/market/api/element/editElement', params);
  return handleCommonResponse(response);
};
//删除分组
export const deleteGroupApi = async (params: any) => {
  const response = await axiosInstance.get('/market/api/grouping/deleteGrouping', {params});
  return handleCommonResponse(response);
};
//删除页面
export const deletePageApi = async (params: any) => {
  const response = await axiosInstance.get('/market/api/page/deletePage', {params});
  return handleCommonResponse(response);
};
//删除元素库
export const deleteElementApi = async (params: any) => {
  const response = await axiosInstance.get('/market/api/element/deleteElementById', {params});
  return handleCommonResponse(response);
};
// 新建分组
export const createGroupApi = async (params: any) => {
  const response = await axiosInstance.post('/market/api/grouping/addGrouping', params);
  return handleCommonResponse(response);
};
//新建元素库
export const createElementApi = async (params: any) => {
  const response = await axiosInstance.post('/market/api/v1/elementLibrary/add', params);
  return handleCommonResponse(response);
};


