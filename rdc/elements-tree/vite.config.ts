import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import federation from "@originjs/vite-plugin-federation";
import pkg from "./package.json";
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

// const PUBLIC_PATH = process.env.PUBLIC_PATH || '/';
// const isDevelopment = process.env.NODE_ENV === 'development';

// https://vitejs.dev/config/
export default defineConfig({
    base: `https://aie.waimai.st.sankuai.com/rdc_host/rdc/${pkg.name}/${pkg.version}/`,
    plugins: [
        react(),
        federation({
            name: "elements_tree",
            filename: "remoteEntry.js",
            exposes: {
                "./App": "./src/App.tsx",
            },
            shared: {
                react: {
                    // @ts-ignore
                    singleton: true,
                    eager: true,
                },
                "react-dom": {
                    // @ts-ignore
                    singleton: true,
                    eager: true,
                },
                lodash: {
                    // @ts-ignore
                    singleton: true,
                    eager: true,
                },
                "react-router-dom": {
                    // @ts-ignore
                    singleton: true,
                    eager: true,
                },
            }
        })
    ],
    css: {
        modules: {
            //
        },
        preprocessorOptions: {
            postcss: {
                plugins: [tailwindcss, autoprefixer],
            },
            less: {
                javascriptEnabled: true,
            },
        },
    },
    build: {
        outDir: `dist/rdc/${pkg.name}/${pkg.version}`,
        target: "esnext",
        rollupOptions: {
          output: {
            assetFileNames: (assetInfo) => {
              // @ts-ignore
              if (assetInfo.name?.endsWith('.css')) {
                return 'assets/index.css';
              }
              return 'assets/[name]-[hash][extname]';
            }
          }
        }
    },
    esbuild: {
        supported: {
            'top-level-await': true
        },
    },
});
