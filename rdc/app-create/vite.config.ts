import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import federation from "@originjs/vite-plugin-federation";
import pkg from "./package.json";
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

const PUBLIC_PATH = process.env.PUBLIC_PATH || '/';

// https://vitejs.dev/config/
export default defineConfig({
  base: PUBLIC_PATH,
  plugins: [
    react(),
    federation({
      name: "aie",
      filename: "remoteEntry.js",
      exposes: {
        "./CreatAppModal": "./src/App.tsx",
        "./Test": "./src/Test.tsx",
      },
      shared: {
        react: {
          // @ts-ignore
          singleton: true,
          eager: true,
        },
        "react-dom": {
          // @ts-ignore
          singleton: true,
          eager: true,
        },
        lodash: {
          // @ts-ignore
          singleton: true,
          eager: true,
        },
        "react-router-dom": {
          // @ts-ignore
          singleton: true,
          eager: true,
        },
      }
    }),
  ],
  css: {
    modules: {
      //
    },
    preprocessorOptions: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
      less: {
        javascriptEnabled: true,
      },
    },
  },
  build: {
    outDir: `dist/rdc/${pkg.name}/${pkg.version}`,
    target: "esnext",
  },
  esbuild: {
    supported: {
      'top-level-await': true
    },
  },
});