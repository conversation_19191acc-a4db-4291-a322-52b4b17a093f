import { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Upload,
  message,
  Input,
  Select,
  Checkbox,
  Spin,
} from "antd";
import type { GetProp, UploadFile, UploadProps } from "antd";
import {
  pageAddOrUpdateApi,
  applicationTag,
  pageAiUploadApi,
  getPersonSpaceList,
  pageDetaile<PERSON>pi,
} from "./api";
import { PlusOutlined } from "@ant-design/icons";
import { DebounceSelect, fetchUserList, UserAvatarNode } from "./newSelect";
type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];
const { TextArea } = Input;
import { fetchEventSource } from "@fortaine/fetch-event-source";
const baseUrl = import.meta.env.VITE_APP_BASE_URL; // 环境变量
const Action_URL = `${baseUrl}/market/api/provider/uploadFile`;
interface UserValue {
  label: any;
  key: string;
  value: string;
}
interface AppProps {
  creatAppOpen: boolean;
  setCreatAppOpen: (open: boolean) => void;
  applicationId?: string;
  ActionSuccess?: () => void;
  id?: string;
  spaceId?: string;
  onSpaceChange?: (spaceId: string) => void; // 添加空间切换回调
}

interface AppDetails {
  id: string;
  applicationId: string;
  name: string;
  describe: string;
  icon: string;
  labelIds: string[];
  functionary: string[];
  applyClientType: number;
  instructions: string; // 添加使用说明字段
  url: string; // 添加系统页面字段
  developer: string[]; // 添加人员字段
  testAccount: any[]; // 添加测试账号字段
  introduce: any[]; // 添加介绍字段
}

function App({
  creatAppOpen,
  setCreatAppOpen,
  applicationId,
  ActionSuccess,
  id,
  spaceId,
  onSpaceChange,
}: AppProps) {
  const [fileList, setFileList] = useState<UploadFile[]>([]); // 上传应用图标
  const [isClientOnly, setIsClientOnly] = useState(1);
  const [appTags, setAppTags] = useState([]); // 应用标签
  const [creatAILoading, setCreatAILoading] = useState(false);
  const [describeLoading, setdDescribeLoading] = useState(false); // AI应用loading
  const [form] = Form.useForm();
  const [value, setValue] = useState<UserValue[]>([]);
  const [appDetails, setAppDetails] = useState<AppDetails | null>(null);
  const [spaceList, setSpaceList] = useState<any[]>([]);

  //默认负责人
  const creatAppOk = async () => {
    try {
      const values = await form.validateFields();
      const newFunctionary = values.functionary?.map((item: any) => item.value);

      const payload = {
        ...appDetails,
        id,
        applicationId,
        spaceId: values.spaceId || spaceId,
        name: values.name,
        describe: values.describe,
        icon: values.icon,
        classify: 4,
        labelIds: values.labelIds,
        functionary: newFunctionary,
        applyClientType: values.applyClientType,
      };

      const res = await pageAddOrUpdateApi(payload);

      if (res.data.code === 0) {
        message.success(applicationId ? "更新应用成功" : "创建应用成功");

        // 只有新增时才跳转，编辑时不跳转
        if (!applicationId) {
          const targetSpaceId = values.spaceId || spaceId;

          // 通知父组件处理空间切换
          onSpaceChange?.(targetSpaceId);

          // 手动更新 localStorage 中的工作空间 ID
          const currentWorkspace = JSON.parse(
            localStorage.getItem("currentWorkspace") || "{}",
          );
          localStorage.setItem(
            "currentWorkspace",
            JSON.stringify({
              ...currentWorkspace,
              id: targetSpaceId,
            }),
          );

          // 给状态更新一点时间后再跳转
          setTimeout(() => {
            window.location.href = `/editor?from=rpa_local_device&id=${res.data.data.applicationId}&spaceId=${targetSpaceId}`;
          }, 100);
        }

        setCreatAppOpen(false);
        ActionSuccess?.();
      } else {
        message.error(res.data.msg || "操作失败，请重试");
      }
    } catch {
      message.error("创建/更新应用失败");
    }
  };
  const creatAppCancel = () => {
    setCreatAppOpen(false);
  };
  const fetchSpaceList = async () => {
    try {
      const response = await getPersonSpaceList({
        title: "",
        permissionCode: "aiwork_operaApp",
      });
      if (response.data.code === 0) {
        const list = response.data?.data?.list?.map((item: any) => ({
          value: item.id,
          label: item.title,
        }));

        // 找到个人空间（假设API返回的数据中title为"个人空间"的就是个人空间）
        const personalSpace = list.find(
          (item: { label: string }) => item.label === "个人空间",
        );

        setSpaceList(list);

        // 如果没有传入spaceId且找到了个人空间，则设置个人空间为默认值
        if (!spaceId && personalSpace) {
          form.setFieldsValue({ spaceId: personalSpace.value });
        }
      }
    } catch (error) {
      console.error("获取工作空间列表失败:", error);
    }
  };
  useEffect(() => {
    if (creatAppOpen) {
      const userInfo = JSON.parse(localStorage.getItem("userInfo") as string);
      const initialFunctionary = [
        {
          label: (
            <UserAvatarNode
              mis={userInfo?.email}
              name={userInfo?.display_name}
              avatar={userInfo?.avatar}
            />
          ),
          key: userInfo?.id,
          value: userInfo?.id,
        },
      ];

      setValue(initialFunctionary);

      // 获取标签和空间列表
      fetchTags();

      if (applicationId) {
        // 如果是编辑模式，获取应用详情
        fetchAppDetails();
      } else {
        // 如果是新建模式，重置表单
        form.resetFields();
        setFileList([]);
        setIsClientOnly(1);

        // 如果传入了 spaceId，优先使用传入的
        if (spaceId) {
          form.setFieldsValue({
            spaceId,
            functionary: initialFunctionary,
          });
        } else {
          // 否则使用当前工作空间
          const currentWorkspace = JSON.parse(
            localStorage.getItem("currentWorkspace") || "{}",
          );
          form.setFieldsValue({
            spaceId: currentWorkspace.id,
            functionary: initialFunctionary,
          });
        }
      }

      // 将 fetchSpaceList 移到最后执行
      fetchSpaceList();
    }
  }, [creatAppOpen]);

  const fetchAppDetails = async () => {
    try {
      const response = await pageDetaileApi({ applicationId });
      if (response.data && response.data.code === 0) {
        const appDetails = response.data.data;
        setAppDetails(appDetails);

        const newFunctionary = Array.isArray(appDetails.functionaryInfo)
          ? appDetails.functionaryInfo.map((item: any) => ({
              label: (
                <UserAvatarNode
                  mis={item.name}
                  name={item.display_name}
                  avatar={item.avatar}
                />
              ),
              key: item.id,
              value: item.id,
            }))
          : [];

        setValue(newFunctionary);

        form.setFieldsValue({
          ...appDetails,
          functionary: newFunctionary,
        });

        setIsClientOnly(appDetails.applyClientType);

        if (appDetails.icon) {
          setFileList([
            {
              uid: "-1",
              name: "image.png",
              status: "done",
              url: appDetails.icon,
            },
          ]);
        }
      }
    } catch (error) {
      console.error("Failed to fetch application details:", error);
      message.error("获取应用详情失败");
    }
  };
  const fetchTags = async () => {
    try {
      const resTag = await applicationTag({ limit: 100 });
      if (resTag.status === 200) {
        setAppTags(
          resTag.data.list.map((item: any) => ({
            value: item.Id,
            label: item.name,
          })),
        );
      }
    } catch (error) {
      console.error("Failed to fetch tags:", error);
      message.error("获取标签列表失败");
    }
  };
  const handleManageIcon: UploadProps["onChange"] = ({
    fileList: newFileList,
  }) => {
    const validFiles = newFileList.filter(
      (file: any) => file.size / 1024 / 1024 < 10,
    );
    setFileList(validFiles);
    const uploadedFiles = validFiles.filter(
      (file: any) => file.status === "done",
    );
    if (uploadedFiles.length > 0) {
      const file = uploadedFiles[0];
      let iconUrl = "";
      if (file.response && file.response.data) {
        iconUrl = file.response.data;
      } else if (file.url) {
        iconUrl = file.url;
      }
      form.setFieldsValue({ icon: iconUrl });
    } else {
      form.setFieldsValue({ icon: "" });
    }
  };

  const onPreview = async (file: UploadFile) => {
    let src = file.url as string;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj as FileType);
        reader.onload = () => resolve(reader.result as string);
      });
    }
    const image = new Image();
    image.src = src;
    const imgWindow = window.open(src);
    imgWindow?.document.write(image.outerHTML);
  };

  const beforeUploadIcon = (file: any) => {
    const isLt10M = file.size / 1024 / 1024 < 10; // 检查文件大小是否小于10MB
    if (!isLt10M) {
      message.error("文件大小不能超过 10MB!");
    }
    return isLt10M;
  };

  // 仅用户端
  const handleClientOnlyChange = (e: any) => {
    const value = e.target.checked ? 1 : 0;
    setIsClientOnly(value);
    form.setFieldsValue({ applyClientType: value });
  };
  const CustomUploadIcon = () => (
    <div className="flex flex-col items-center">
      <PlusOutlined className="text-2xl mb-1" />
      <span>上传图标</span>
    </div>
  );
  //AI生成图片
  const handleCreatAI = async () => {
    setCreatAILoading(true);
    try {
      const values = await form.validateFields(["name", "describe"]);
      const appName = values.name;
      const appDescribe = values.describe;
      const res = await pageAiUploadApi({
        prompt: appName + "," + appDescribe,
      });
      if (res.data) {
        const aiImageUrl = res.data.data[0].url;
        // 更新表单字段
        form.setFieldsValue({ icon: aiImageUrl });
        setFileList([
          {
            uid: "-10",
            name: "ai_image.png",
            status: "done",
            url: aiImageUrl,
          },
        ]);
      } else {
        message.error("AI 生成图片失败");
      }
    } catch (error) {
      console.error("AI 生成图片出错:", error);
    } finally {
      setCreatAILoading(false);
    }
  };

  //AI生成描述
  const handleDescribeAI = async () => {
    setdDescribeLoading(true);
    try {
      const values = await form.validateFields(["name"]);
      const appName = values.name;
      // 使用 fetchEventSource 处理流式响应
      await fetchEventSource(`${baseUrl}/xgpt/open/agentAi/ai`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Xc-Auth": localStorage.getItem("nc-token") || "",
        },
        body: JSON.stringify({
          name: appName,
          aiType: "aiwork_describe",
        }),
        onmessage(event) {
          const data = JSON.parse(event.data);
          console.log(data, "data");
          data.contents.map((item: any) => {
            if (item.type === "MESSAGE") {
              form.setFieldsValue({ describe: item.content });
            }
          });
        },
        onerror(err) {
          console.error("AI 生成描述出错:", err);
          message.error("AI 生成描述出错");
          setdDescribeLoading(false);
        },
        onclose() {
          message.success("AI 生成描述成功");
          setdDescribeLoading(false);
          console.log("Connection to the server closed.");
        },
      });
    } catch (error) {
      console.error("AI 生成描述出错:", error);
      message.error("AI 生成描述出错");
      setdDescribeLoading(false);
    }
  };

  // 1. 添加处理工作空间选择变化的函数
  const handleSpaceChange = (value: string) => {
    form.setFieldsValue({ spaceId: value });
  };

  return (
    <div>
      <Modal
        title={applicationId ? "编辑应用" : "创建应用"}
        open={creatAppOpen}
        onOk={creatAppOk}
        onCancel={creatAppCancel}
        okText="提交"
        cancelText="取消"
        width={640}
        styles={{ body: { maxHeight: "640px", overflowY: "auto" } }}
      >
        <Form form={form} layout="vertical" style={{ marginLeft: 10 }}>
          <Form.Item
            style={{ marginBottom: 10 }}
            name="name"
            label={
              <div className="flex">
                <div className="w-[520px]">应用名称</div>
              </div>
            }
            rules={[
              { max: 20 },
              {
                required: true,
                message: "请输入应用名称",
                validator: (_, value) =>
                  value && value.trim() !== ""
                    ? Promise.resolve()
                    : Promise.reject(new Error("请输入应用名称")),
              },
            ]}
          >
            <Input showCount maxLength={20} placeholder="请输入" />
          </Form.Item>
          <Form.Item
            style={{ marginBottom: 10 }}
            name="describe"
            label={
              <div style={{ display: "flex" }}>
                <div style={{ width: "500px" }}>应用描述</div>
                <Spin spinning={describeLoading}>
                  <button
                    onClick={handleDescribeAI}
                    style={{
                      width: "70px",
                      height: "25px",
                      borderRadius: "4px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "var(--ai-desc-bg)",
                    }}
                  >
                    <i
                      className="aieicon aieicon-sparkling-2-fill"
                      style={{
                        fontSize: "12px",
                        color: "#3c83F0",
                        marginRight: "4px",
                      }}
                    ></i>
                    <span
                      style={{
                        color: "#3c83F0",
                        fontSize: "13px",
                        fontWeight: "bold",
                      }}
                    >
                      AI生成
                    </span>
                  </button>
                </Spin>
              </div>
            }
            rules={[
              { max: 100 },
              // {
              //   validator: (_, value) =>
              //     value && value.trim() !== ""
              //       ? Promise.resolve()
              //       : Promise.reject(new Error("请输入应用描述")),
              // },
            ]}
          >
            <TextArea showCount maxLength={100} />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "请选择归属空间" }]}
            name="spaceId"
            label="归属空间"
            style={{ marginBottom: 10 }}
            initialValue={spaceId}
          >
            <Select
              showSearch
              allowClear
              options={spaceList}
              placeholder="请选择"
              onChange={handleSpaceChange}
              defaultValue={spaceId}
              filterOption={(input, option) =>
                typeof option?.label === "string"
                  ? option.label.toLowerCase().includes(input.toLowerCase())
                  : false
              }
              optionFilterProp="label"
            />
          </Form.Item>
          <Form.Item
            name="labelIds"
            label="应用标签"
            style={{ marginBottom: 10 }}
          >
            <Select
              mode="multiple"
              allowClear
              options={appTags}
              placeholder="请输入"
            />
          </Form.Item>
          <Form.Item
            name="functionary"
            label="开发者"
            style={{ marginBottom: 10 }}
          >
            <DebounceSelect
              mode="multiple"
              value={value}
              placeholder="请输入"
              maxTagCount="responsive"
              fetchOptions={fetchUserList}
              onChange={(newValue) => {
                setValue(newValue as UserValue[]);
                form.setFieldsValue({
                  functionary: newValue,
                });
              }}
              style={{ width: "100%" }}
              disabled
            />
          </Form.Item>
          <Form.Item
            name="applyClientType"
            valuePropName="checked"
            label="适用端"
            style={{ marginBottom: 10 }}
          >
            <Checkbox
              onChange={handleClientOnlyChange}
              value={isClientOnly}
              defaultChecked={true}
            >
              仅客户端
            </Checkbox>
          </Form.Item>
          <Form.Item
            style={{ marginBottom: 10 }}
            name="icon"
            label="应用图标"
            getValueFromEvent={(e) => {
              if (Array.isArray(e)) {
                return e;
              }
            }}
          >
            <div className="flex">
              <Upload
                action={Action_URL}
                listType="picture-circle"
                fileList={fileList}
                onChange={handleManageIcon}
                onPreview={onPreview}
                accept="image/*"
                beforeUpload={beforeUploadIcon}
              >
                {fileList.length < 1 && <CustomUploadIcon />}
              </Upload>
              <div
                style={{
                  height: "100px",
                  width: "400px",
                  backgroundColor: "#F9FAFC",
                  marginLeft: "16px",
                }}
              >
                <div
                  style={{
                    marginLeft: "24px",
                    marginTop: "12px",
                  }}
                  onClick={handleCreatAI}
                >
                  <Spin spinning={creatAILoading}>
                    <button
                      style={{
                        width: "80px",
                        height: "80px",
                        borderRadius: "50%",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        background:
                          "linear-gradient(225deg, #2137FF 0%, #6CBDFF 100%)",
                      }}
                    >
                      <i
                        className="aieicon aieicon-sparkling-2-fill"
                        style={{ 
                          fontSize: "28px",
                          color: "white",
                          height: "40px"
                        }}
                      ></i>
                      <span style={{
                          color: "white",
                          fontSize: "15px"
                        }}>AI生成</span>
                    </button>
                  </Spin>
                </div>
              </div>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
export default App;
