{"$schema": "https://turbo.build/schema.json", "ui": "tui", "envMode": "loose", "concurrency": "20", "tasks": {"generate": {"outputs": ["src/generated/**"]}, "build": {"dependsOn": ["^build", "generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "demo-viewer#build": {"dependsOn": ["^build", "generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"dependsOn": ["^build", "generate"], "cache": false, "persistent": true}, "demo-viewer#dev": {"dependsOn": ["^build", "generate"], "cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "outputs": []}, "link:global": {"cache": false}, "unlink:global": {"cache": false}}}