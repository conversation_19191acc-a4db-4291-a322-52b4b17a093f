[tool.poetry]
name = "langgraph_agui_dojo"
version = "0.1.0"
description = ""
readme = "README.md"

[project]
name = "agents"
version = "0.0.1"


[tool.poetry.dependencies]
python = ">=3.12,<3.13"
copilotkit = "0.1.46"
tavily-python = "^0.5.1"
uvicorn = "^0.34.0"
dotenv = "^0.9.9"
crewai = "0.118.0"
langchain = ">=0.1.0"
langchain-core = ">=0.1.5"
langchain-community = ">=0.0.1"
langchain-experimental = ">=0.0.11"
langchain-openai = ">=0.0.1"
langgraph = "^0.3.25"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
