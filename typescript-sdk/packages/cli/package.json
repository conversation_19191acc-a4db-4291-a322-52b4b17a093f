{"name": "create-ag-ui-app", "author": "<PERSON> <<EMAIL>>", "version": "0.0.4", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": "./dist/index.js", "sideEffects": false, "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@types/inquirer": "^9.0.8", "commander": "^12.1.0", "inquirer": "^12.6.3"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.11.19", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}}