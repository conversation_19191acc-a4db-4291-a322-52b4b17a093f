#!/usr/bin/python3
# coding: utf-8
"""
AG-UI客户端示例
演示如何连接和使用AG-UI兼容的LangGraph服务
"""

import asyncio
import json
import websockets
import requests
from typing import Dict, Any, List


class AGUIClient:
    """AG-UI客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        """
        初始化AG-UI客户端
        
        Args:
            base_url: 服务基础URL
        """
        self.base_url = base_url
        self.ws_url = base_url.replace("http", "ws") + "/ws/ag-ui"
        self.http_url = base_url + "/ag-ui/run"
    
    async def connect_websocket(self, messages: List[Dict[str, Any]], thread_id: str = None):
        """
        通过WebSocket连接AG-UI服务
        
        Args:
            messages: 消息列表
            thread_id: 线程ID（可选）
        """
        try:
            async with websockets.connect(self.ws_url) as websocket:
                print(f"已连接到AG-UI WebSocket: {self.ws_url}")
                
                # 发送运行请求
                request = {
                    "type": "run",
                    "messages": messages,
                    "thread_id": thread_id
                }
                
                await websocket.send(json.dumps(request))
                print(f"已发送请求: {request}")
                
                # 接收和处理响应
                async for message in websocket:
                    try:
                        event = json.loads(message)
                        await self._handle_event(event, websocket)
                        
                        # 如果收到运行结束事件，退出循环
                        if event.get("type") == "run_finished":
                            print("工作流执行完成")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {e}")
                    except Exception as e:
                        print(f"处理消息错误: {e}")
                        
        except Exception as e:
            print(f"WebSocket连接错误: {e}")
    
    async def _handle_event(self, event: Dict[str, Any], websocket):
        """
        处理AG-UI事件
        
        Args:
            event: AG-UI事件
            websocket: WebSocket连接
        """
        event_type = event.get("type")
        
        if event_type == "run_started":
            print("🚀 工作流开始执行")
            print(f"   线程ID: {event.get('thread_id')}")
            print(f"   运行ID: {event.get('run_id')}")
            
        elif event_type == "run_finished":
            print("✅ 工作流执行完成")
            
        elif event_type == "text_message_start":
            message_id = event.get("message_id")
            role = event.get("role")
            name = event.get("name")
            print(f"💬 开始新消息 [{role}]" + (f" ({name})" if name else ""))
            print(f"   消息ID: {message_id}")
            
        elif event_type == "text_message_delta":
            delta = event.get("delta", "")
            print(delta, end="", flush=True)
            
        elif event_type == "text_message_end":
            message_id = event.get("message_id")
            print(f"\n📝 消息结束 (ID: {message_id})")
            
        elif event_type == "tool_calls_start":
            tool_calls = event.get("tool_calls", [])
            print(f"🔧 开始工具调用: {len(tool_calls)} 个工具")
            for tool_call in tool_calls:
                function_name = tool_call.get("function", {}).get("name", "unknown")
                print(f"   - {function_name}")
                
        elif event_type == "tool_calls_end":
            tool_results = event.get("tool_call_results", [])
            print(f"✅ 工具调用完成: {len(tool_results)} 个结果")
            
        elif event_type == "state_snapshot":
            snapshot = event.get("snapshot", {})
            workflow_status = snapshot.get("workflow_status", "unknown")
            current_node = snapshot.get("current_node")
            active_nodes = snapshot.get("active_nodes", [])
            print(f"📊 状态快照: {workflow_status}")
            if current_node:
                print(f"   当前节点: {current_node}")
            if active_nodes:
                print(f"   活跃节点: {', '.join(active_nodes)}")
                
        elif event_type == "state_delta":
            delta = event.get("delta", [])
            print(f"🔄 状态更新: {len(delta)} 个变更")
            
        elif event_type == "messages_snapshot":
            messages = event.get("messages", [])
            print(f"📨 消息快照: {len(messages)} 条消息")
            
        elif event_type == "interrupt":
            message = event.get("message")
            interrupt_type = event.get("interrupt_type")
            print(f"⚠️  中断请求: {message}")
            print(f"   类型: {interrupt_type}")
            
            # 处理中断（这里简单地发送确认）
            response = {
                "type": "interrupt_response",
                "response": "continue"  # 或者根据需要发送其他响应
            }
            await websocket.send(json.dumps(response))
            
        elif event_type == "error":
            error = event.get("error")
            error_code = event.get("error_code")
            print(f"❌ 错误: {error}")
            if error_code:
                print(f"   错误代码: {error_code}")
                
        else:
            print(f"📦 未知事件类型: {event_type}")
            print(f"   事件内容: {json.dumps(event, indent=2, ensure_ascii=False)}")
    
    def send_http_request(self, messages: List[Dict[str, Any]], thread_id: str = None):
        """
        通过HTTP发送AG-UI请求
        
        Args:
            messages: 消息列表
            thread_id: 线程ID（可选）
            
        Returns:
            响应结果
        """
        try:
            request_data = {
                "messages": messages,
                "thread_id": thread_id
            }
            
            print(f"发送HTTP请求到: {self.http_url}")
            response = requests.post(self.http_url, json=request_data)
            response.raise_for_status()
            
            result = response.json()
            print(f"收到响应: {len(result.get('events', []))} 个事件")
            
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"HTTP请求错误: {e}")
            return None
    
    def check_health(self):
        """
        检查服务健康状态
        
        Returns:
            健康状态信息
        """
        try:
            health_url = self.base_url + "/ag-ui/health"
            response = requests.get(health_url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"健康检查失败: {e}")
            return None
    
    def get_service_info(self):
        """
        获取服务信息
        
        Returns:
            服务信息
        """
        try:
            info_url = self.base_url + "/ag-ui/info"
            response = requests.get(info_url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取服务信息失败: {e}")
            return None


async def main():
    """主函数 - 演示AG-UI客户端使用"""
    # 创建客户端
    client = AGUIClient("http://localhost:8080")
    
    print("=== AG-UI客户端示例 ===")
    
    # 检查服务健康状态
    print("\n1. 检查服务健康状态...")
    health = client.check_health()
    if health:
        print(f"服务状态: {health.get('status')}")
        print(f"服务名称: {health.get('service')}")
    else:
        print("服务不可用，请确保服务正在运行")
        return
    
    # 获取服务信息
    print("\n2. 获取服务信息...")
    info = client.get_service_info()
    if info:
        print(f"服务名称: {info.get('name')}")
        print(f"协议版本: {info.get('protocol_version')}")
        print(f"支持的功能: {', '.join(info.get('features', {}).keys())}")
    
    # 准备测试消息
    test_messages = [
        {
            "role": "user",
            "content": "请帮我生成一个简单的HTTP接口测试用例"
        }
    ]
    
    print("\n3. 测试WebSocket连接...")
    print("发送消息:", test_messages[0]["content"])
    print("\n--- WebSocket事件流 ---")
    
    try:
        await client.connect_websocket(test_messages)
    except Exception as e:
        print(f"WebSocket测试失败: {e}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())