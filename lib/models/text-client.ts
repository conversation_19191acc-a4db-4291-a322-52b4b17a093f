import {
  ClaudeIcon,
  GeminiIcon,
  GemmaIcon,
  GrokIcon,
  PerplexityIcon,
} from '../icons';
import { type TersaModel, type TersaProvider, providers } from '../providers';

export type PriceBracket = 'lowest' | 'low' | 'high' | 'highest';

const million = 1000000;
const thousand = 1000;

type TersaTextModelClient = TersaModel & {
  providers: (TersaProvider & {
    modelId: string;
    getCost: ({ input, output }: { input: number; output: number }) => number;
  })[];
};

// Client-safe text models configuration (no actual model instances)
export const textModels: Record<string, TersaTextModelClient> = {
  'gpt-3.5-turbo': {
    label: 'GPT-3.5 Turbo',
    chef: providers.openai,
    providers: [
      {
        ...providers.openai,
        modelId: 'gpt-3.5-turbo',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 0.5;
          const outputCost = (output / million) * 1.5;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'lowest',
  },
  'gpt-4': {
    label: 'GPT-4',
    chef: providers.openai,
    providers: [
      {
        ...providers.openai,
        modelId: 'gpt-4',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 30;
          const outputCost = (output / million) * 60;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'highest',
  },
  'gpt-4o': {
    label: 'GPT-4o',
    chef: providers.openai,
    providers: [
      {
        ...providers.openai,
        modelId: 'gpt-4o',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 2.5;
          const outputCost = (output / million) * 10;
          return inputCost + outputCost;
        },
      },
    ],
    default: true,
  },
  'gpt-4o-mini': {
    label: 'GPT-4o Mini',
    chef: providers.openai,
    providers: [
      {
        ...providers.openai,
        modelId: 'gpt-4o-mini',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 0.15;
          const outputCost = (output / million) * 0.6;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'lowest',
  },
  'gpt-4o-2024-11-20': {
    label: 'GPT-4o (2024-11-20)',
    chef: providers.openai,
    providers: [
      {
        ...providers.openai,
        modelId: 'gpt-4o-2024-11-20',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 2.5;
          const outputCost = (output / million) * 10;
          return inputCost + outputCost;
        },
      },
    ],
  },
  o1: {
    label: 'O1',
    chef: providers.openai,
    providers: [
      {
        ...providers.openai,
        modelId: 'o1',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 15;
          const outputCost = (output / million) * 60;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'highest',
  },
  'o1-mini': {
    label: 'O1 Mini',
    chef: providers.openai,
    providers: [
      {
        ...providers.openai,
        modelId: 'o1-mini',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 1.1;
          const outputCost = (output / million) * 4.4;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'low',
  },
  'claude-3-5-sonnet-latest': {
    icon: ClaudeIcon,
    label: 'Claude 3.5 Sonnet',
    chef: providers.anthropic,
    providers: [
      {
        ...providers.anthropic,
        modelId: 'claude-3-5-sonnet-latest',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 3;
          const outputCost = (output / million) * 15;
          return inputCost + outputCost;
        },
      },
    ],
  },
  'claude-3-5-haiku-latest': {
    icon: ClaudeIcon,
    label: 'Claude 3.5 Haiku',
    chef: providers.anthropic,
    providers: [
      {
        ...providers.anthropic,
        modelId: 'claude-3-5-haiku-latest',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 0.8;
          const outputCost = (output / million) * 4;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'low',
  },
  'gemini-2.0-flash': {
    icon: GeminiIcon,
    label: 'Gemini 2.0 Flash',
    chef: providers.google,
    providers: [
      {
        ...providers.google,
        modelId: 'gemini-2.0-flash-001',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 0.1;
          const outputCost = (output / million) * 0.4;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'lowest',
  },
  'gemini-1.5-pro': {
    icon: GeminiIcon,
    label: 'Gemini 1.5 Pro',
    chef: providers.google,
    providers: [
      {
        ...providers.google,
        modelId: 'gemini-1.5-pro',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 2.5;
          const outputCost = (output / million) * 10;
          return inputCost + outputCost;
        },
      },
    ],
  },
  'deepseek-v3': {
    label: 'DeepSeek V3 (Chat)',
    chef: providers.deepseek,
    providers: [
      {
        ...providers.deepseek,
        modelId: 'deepseek-chat',
        getCost: ({ input, output }: { input: number; output: number }) => {
          const inputCost = (input / million) * 0.27;
          const outputCost = (output / million) * 1.1;
          return inputCost + outputCost;
        },
      },
    ],
    priceIndicator: 'lowest',
  },
};