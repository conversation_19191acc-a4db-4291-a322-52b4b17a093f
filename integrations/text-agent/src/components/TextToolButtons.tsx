import { memo } from 'react';
import { File, Loader, Pencil } from 'lucide-react';

const getActionText = (type: 'createText' | 'updateText', tense: 'present' | 'past') => {
    switch (type) {
        case 'createText':
            return tense === 'present' ? '正在创建' : '已创建';
        case 'updateText':
            return tense === 'present' ? '正在更新' : '已更新';
        default:
            return null;
    }
};

interface TextToolResultProps {
    type: 'createText' | 'updateText';
    result: { id: string; title: string; kind: string; content: string };
    isReadonly: boolean;
}

function PureTextToolResult({ type, result, isReadonly }: TextToolResultProps) {
    // 这里需要在实际使用时从 apps/web 传入 useArtifact hook
    // 由于这是一个独立的包，我们暂时使用一个简单的实现
    const handleClick = (event: React.MouseEvent) => {
        if (isReadonly) {
            // 这里需要从 apps/web 传入 toast 函数
            console.error('在共享聊天中查看文件当前不受支持。');
            return;
        }

        const rect = event.currentTarget.getBoundingClientRect();
        const boundingBox = {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
        };

        // 这里需要调用 setArtifact，但由于这是独立包，需要通过 props 传入
        console.log('Opening artifact:', {
            documentId: result.id,
            kind: result.kind,
            content: result.content,
            title: result.title,
            isVisible: true,
            status: 'idle',
            boundingBox
        });
    };

    return (
        <button
            type='button'
            className='bg-background flex w-fit cursor-pointer flex-row items-start gap-3 rounded-xl border px-3 py-2'
            onClick={handleClick}>
            <div className='text-muted-foreground mt-1'>
                {type === 'createText' ? <File size={16} /> : type === 'updateText' ? <Pencil size={16} /> : null}
            </div>
            <div className='text-left'>{`${getActionText(type, 'past')} "${result.title}"`}</div>
        </button>
    );
}

export const TextToolResult = memo(PureTextToolResult, () => true);

interface TextToolCallProps {
    type: 'createText' | 'updateText';
    args: { title: string; description?: string };
    isReadonly: boolean;
}

function PureTextToolCall({ type, args, isReadonly }: TextToolCallProps) {
    const handleClick = (event: React.MouseEvent) => {
        if (isReadonly) {
            console.error('在共享聊天中查看文件当前不受支持。');
            return;
        }

        const rect = event.currentTarget.getBoundingClientRect();
        const boundingBox = {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
        };

        // 这里需要调用 setArtifact，但由于这是独立包，需要通过 props 传入
        console.log('Opening streaming artifact:', {
            isVisible: true,
            kind: 'text',
            title: args.title,
            content: '',
            status: 'streaming',
            boundingBox
        });
    };

    return (
        <button
            type='button'
            className='flex w-fit cursor-pointer flex-row items-start justify-between gap-3 rounded-xl border px-3 py-2'
            onClick={handleClick}>
            <div className='flex flex-row items-start gap-3'>
                <div className='mt-1 text-zinc-500'>
                    {type === 'createText' ? <File size={16} /> : type === 'updateText' ? <Pencil size={16} /> : null}
                </div>

                <div className='text-left'>
                    {`${getActionText(type, 'present')} ${args.title ? `"${args.title}"` : ''}`}
                </div>
            </div>

            <div className='mt-1 animate-spin'>
                <Loader size={16} />
            </div>
        </button>
    );
}

export const TextToolCall = memo(PureTextToolCall, () => true);
