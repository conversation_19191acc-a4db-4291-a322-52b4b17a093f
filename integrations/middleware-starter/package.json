{"name": "@ag-ui/middleware-starter", "author": "<PERSON> <<EMAIL>>", "version": "0.0.1", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@ag-ui/client": "workspace:*", "openai": "^5.5.1"}, "peerDependencies": {"rxjs": "7.8.1"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^24.0.3", "jest": "^30.0.1", "ts-jest": "^29.4.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}}