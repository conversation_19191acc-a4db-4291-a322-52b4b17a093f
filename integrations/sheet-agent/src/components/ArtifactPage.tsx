'use client';

import React from 'react';
import { SheetEditor } from './SheetEditor.js';

// 定义具体的类型
interface SheetMetadata {
    headers?: string[];
    data?: (string | number)[][];
    rowCount?: number;
    columnCount?: number;
    [key: string]: unknown;
}

interface SheetSuggestion {
    id: string;
    text: string;
    type?: string;
}

export interface ArtifactPageProps {
    title: string;
    content: string;
    onSaveContent?: (content: string, debounce: boolean) => void;
    metadata?: SheetMetadata;
    setMetadata?: (metadata: SheetMetadata) => void;
    status?: 'streaming' | 'idle';
    isCurrentVersion?: boolean;
    currentVersionIndex?: number;
    suggestions?: SheetSuggestion[];
    mode?: 'edit' | 'diff';
    isInline?: boolean;
    isLoading?: boolean;
}

export function SheetArtifactPage({
    content,
    onSaveContent,
    status = 'idle',
    isCurrentVersion = true,
    currentVersionIndex = 0,
    suggestions = [],
    ...props
}: ArtifactPageProps) {
    return (
        <div className='h-full w-full overflow-hidden'>
            <SheetEditor
                content={content}
                onChange={(newContent: string) => onSaveContent?.(newContent, true)}
                status={status}
                isCurrentVersion={isCurrentVersion}
                currentVersionIndex={currentVersionIndex}
                suggestions={suggestions}
                {...props}
            />
        </div>
    );
}
