import { EditorAgent } from './agent.js';
import { formatContent, extractMetadata } from './utils.js';
import { SupportedFormat } from './types.js';

// 定义上下文类型
interface ProcessContext {
    userId?: string;
    sessionId?: string;
    metadata?: Record<string, unknown>;
}

export class EditorAgentServer {
    private agent: EditorAgent;

    constructor() {
        this.agent = new EditorAgent();
    }

    async formatDocument(content: string, format: SupportedFormat) {
        try {
            const formattedContent = formatContent(content, format);
            const metadata = extractMetadata(formattedContent, format);

            return {
                success: true,
                data: {
                    content: formattedContent,
                    metadata
                }
            };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return {
                success: false,
                error: errorMessage
            };
        }
    }

    async processMessage(message: string, context?: ProcessContext) {
        try {
            const result = await this.agent.processMessage(message);
            return {
                success: true,
                data: result
            };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return {
                success: false,
                error: errorMessage
            };
        }
    }

    async generateArtifact(prompt: string, type: string) {
        try {
            const result = await this.agent.generateArtifact(prompt, type);
            return {
                success: true,
                data: result
            };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return {
                success: false,
                error: errorMessage
            };
        }
    }

    getMetadata() {
        return this.agent.metadata;
    }

    getCapabilities() {
        return this.agent.capabilities;
    }
}

export default new EditorAgentServer();
