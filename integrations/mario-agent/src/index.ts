'use client';

// Agent Integration 统一导出
// 使用命名空间模式避免命名冲突

import type { IntegrationPackage } from '@workspace/shared';
import { createAgent } from './agent.js';
import { DEFAULT_CONFIG } from './config.js';
import { Workspace } from './workspace.js';

// 导出命名空间对象，避免命名冲突
export const MarioIntegration = {
  // 基础信息
  id: 'mario',
  name: 'Agent Integration',
  description: 'Mario框架用例生成',
  version: '0.0.1',

  // 核心功能
  createAgent,
  Agent: createAgent, // 别名

  // UI组件
  Workspace,

  // 配置
  defaultConfig: DEFAULT_CONFIG,

  // 工具
  tools: {},

  // 工具数组（用于注册）
  toolsArray: []
};

// 创建 Integration Package 用于注册
export const marioIntegrationPackage: IntegrationPackage = {
  id: MarioIntegration.id,
  name: MarioIntegration.name,
  description: MarioIntegration.description,
  version: MarioIntegration.version,
  createAgent: MarioIntegration.createAgent,
  workspace: MarioIntegration.Workspace as any,
  defaultConfig: MarioIntegration.defaultConfig,
  tools: MarioIntegration.toolsArray
};

// 默认导出命名空间
export default MarioIntegration;

// 导出类型
export type { AgentConfig, AgentState, WorkspaceProps, Item, GenerateParams, GenerateResult } from './types.js';

// 核心组件
export { Workspace } from './workspace.js';
export { MarioChatContainer } from './components/MarioChatContainer.js';
export { WebCodeFrame } from './components/WebCodeFrame.js';
export { WebTerminalFrame } from './components/WebTerminalFrame.js';
export { MarioMessageList } from './components/MarioMessageList.js';

// Hooks
export { useMarioWebSocket } from './hooks/useMarioWebSocket.js';

// 服务 - 使用客户端安全的 Docker 服务
export { dockerClientService } from './services/docker-client.js';

// 类型
export type {
  MarioMessage,
  MarioCase,
  ContainerInfo,
  ContainerConfig,
  DockerServiceResponse,
  MarioInput,
  MarioSettings,
  WebSocketState,
  ChatState,
  HealthCheckResult,
  MarioEvent,
  MarioChatContainerProps,
  WebCodeFrameProps,
  WebTerminalFrameProps,
  MarioMessageListProps,
  ApiResponse,
  MarioError,
  ContainerError,
  WebSocketError,
  MarioConfig
} from './types/mario.js';

// 工具函数
export {
  generateId,
  formatTimestamp,
  formatDateTime,
  createMarioMessage,
  createSystemMessage,
  createErrorMessage,
  isValidPort,
  isValidContainerId,
  truncateContainerId,
  formatFileSize,
  delay,
  retry,
  withTimeout,
  debounce,
  throttle,
  deepClone,
  mergeDeep,
  safeJsonParse,
  safeJsonStringify,
  isValidUrl,
  buildWebSocketUrl,
  buildHttpUrl,
  extractErrorMessage,
  generateRandomPort,
  isPortAvailable,
  stripHtmlTags,
  escapeHtml,
  copyToClipboard,
  downloadTextFile,
  getFileExtension,
  getMimeType,
  formatContainerStatus,
  formatHealthStatus,
  getRelativeTime,
  validateMarioCase,
  generateContainerName,
  parseDockerImage,
  formatDockerImage
} from './utils/mario-utils.js';

// 配置
export {
  CONFIG,
  MARIO_CONFIG,
  ENV_CONFIG,
  DOCKER_IMAGES,
  PORT_CONFIG,
  WEBSOCKET_CONFIG,
  TERMINAL_CONFIG,
  FILE_CONFIG,
  HEALTH_CHECK_CONFIG,
  LOG_CONFIG,
  CACHE_CONFIG,
  UI_CONFIG,
  SECURITY_CONFIG,
  PERFORMANCE_CONFIG,
  FEATURE_FLAGS,
  validateConfig,
  getRuntimeConfig
} from './config/mario-config.js';

// 原有的Agent相关导出（保持兼容性）
export { createAgent } from './agent.js';
