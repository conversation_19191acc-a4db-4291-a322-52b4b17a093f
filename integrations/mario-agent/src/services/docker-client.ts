import axios from 'axios';

interface ContainerInfo {
  id: string;
  name: string;
  port: number;
  status: string;
}

interface CreateContainerOptions {
  image: string;
  name: string;
  port: number;
  env?: string[];
  volumes?: string[];
}

interface DockerApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

class DockerClientService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/docker') {
    this.baseUrl = baseUrl;
  }

  /**
   * 测试容器连接
   */
  async testConnection(port: number): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/test-connection`, {
        params: { port },
        timeout: 5000,
      });
      return response.data.success;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查容器健康状态
   */
  async checkContainerHealth(containerId: string): Promise<{
    isHealthy: boolean;
    status: string;
    port?: number;
  }> {
    try {
      const response = await axios.get(`${this.baseUrl}/containers/${containerId}/health`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error checking container health:', error);
      return { isHealthy: false, status: 'error' };
    }
  }

  /**
   * 等待容器服务就绪
   */
  async waitForContainerReady(containerId: string, maxWaitTime = 60000): Promise<{
    success: boolean;
    port?: number;
    error?: string;
  }> {
    try {
      const response = await axios.post(`${this.baseUrl}/containers/${containerId}/wait-ready`, {
        maxWaitTime
      });
      return response.data.data;
    } catch (error: any) {
      console.error('Error waiting for container ready:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建新的Docker容器
   */
  async createContainer(options: CreateContainerOptions): Promise<{
    success: boolean;
    containerId?: string;
    port?: number;
    error?: string;
  }> {
    try {
      // 将参数转换为现有 API 期望的格式
      const apiPayload = {
        username: options.name.replace(/[^a-zA-Z0-9-]/g, ''), // 清理用户名
        image: options.image,
        env: options.env ? options.env.reduce((acc, envVar) => {
          const [key, value] = envVar.split('=');
          if (key && value) {
            acc[key] = value;
          }
          return acc;
        }, {} as Record<string, string>) : {},
        volumes: options.volumes || []
      };

      const response = await axios.post(this.baseUrl, apiPayload);
      
      if (response.data.success) {
        return {
          success: true,
          containerId: response.data.containerId,
          port: response.data.webPort
        };
      } else {
        return {
          success: false,
          error: response.data.error
        };
      }
    } catch (error: any) {
      console.error('Error creating container:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message
      };
    }
  }

  /**
   * 停止容器
   */
  async stopContainer(containerId: string): Promise<boolean> {
    try {
      const response = await axios.post(`${this.baseUrl}/containers/${containerId}/stop`);
      return response.data.success;
    } catch (error: any) {
      console.error('Error stopping container:', error);
      return false;
    }
  }

  /**
   * 删除容器
   */
  async removeContainer(containerId: string): Promise<boolean> {
    try {
      const response = await axios.delete(`${this.baseUrl}/containers/${containerId}`);
      return response.data.success;
    } catch (error: any) {
      console.error('Error removing container:', error);
      return false;
    }
  }

  /**
   * 列出容器
   */
  async listContainers(): Promise<ContainerInfo[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/containers`);
      return response.data.data || [];
    } catch (error: any) {
      console.error('Error listing containers:', error);
      return [];
    }
  }
}

// 创建单例实例
export const dockerClientService = new DockerClientService();
export default dockerClientService; 