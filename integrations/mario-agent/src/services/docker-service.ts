import Dockerode from 'dockerode';
import axios from 'axios';

interface ContainerInfo {
  id: string;
  name: string;
  port: number;
  status: string;
}

interface CreateContainerOptions {
  image: string;
  name: string;
  port: number;
  env?: string[];
  volumes?: string[];
}

class DockerService {
  private docker: Dockerode;

  constructor() {
    this.docker = new Dockerode();
  }

  /**
   * 测试容器连接
   */
  async testConnection(port: number): Promise<boolean> {
    try {
      const response = await axios.get(`http://localhost:${port}/health`, {
        timeout: 5000,
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查容器健康状态
   */
  async checkContainerHealth(containerId: string): Promise<{
    isHealthy: boolean;
    status: string;
    port?: number;
  }> {
    try {
      const container = this.docker.getContainer(containerId);
      const info = await container.inspect();
      
      const isRunning = info.State.Running;
      const port = this.extractPortFromContainer(info);
      
      if (!isRunning) {
        return { isHealthy: false, status: 'stopped' };
      }
      
      if (port && await this.testConnection(port)) {
        return { isHealthy: true, status: 'healthy', port };
      }
      
      return { isHealthy: false, status: 'unhealthy', port };
    } catch (error) {
      console.error('Error checking container health:', error);
      return { isHealthy: false, status: 'error' };
    }
  }

  /**
   * 等待容器服务就绪
   */
  async waitForContainerReady(containerId: string, maxWaitTime = 60000): Promise<{
    success: boolean;
    port?: number;
    error?: string;
  }> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const health = await this.checkContainerHealth(containerId);
      
      if (health.isHealthy && health.port) {
        return { success: true, port: health.port };
      }
      
      if (health.status === 'stopped' || health.status === 'error') {
        return { success: false, error: `Container ${health.status}` };
      }
      
      // 等待2秒后重试
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    return { success: false, error: 'Timeout waiting for container to be ready' };
  }

  /**
   * 创建新的Docker容器
   */
  async createContainer(options: CreateContainerOptions): Promise<{
    success: boolean;
    containerId?: string;
    port?: number;
    error?: string;
  }> {
    try {
      // 检查镜像是否存在
      try {
        await this.docker.getImage(options.image).inspect();
      } catch (error) {
        // 如果镜像不存在，尝试拉取
        console.log(`Pulling image ${options.image}...`);
        await this.pullImage(options.image);
      }

      // 停止并删除同名容器（如果存在）
      await this.removeContainerByName(options.name);

      // 创建容器配置
      const containerConfig: Dockerode.ContainerCreateOptions = {
        Image: options.image,
        name: options.name,
        Env: options.env || [],
        ExposedPorts: {
          [`${options.port}/tcp`]: {}
        },
        HostConfig: {
          PortBindings: {
            [`${options.port}/tcp`]: [{ HostPort: options.port.toString() }]
          },
          Binds: options.volumes || [],
          AutoRemove: true
        },
        WorkingDir: '/workspace'
      };

      // 创建容器
      const container = await this.docker.createContainer(containerConfig);
      
      // 启动容器
      await container.start();
      
      const containerId = container.id;
      
      // 等待容器就绪
      const readyResult = await this.waitForContainerReady(containerId);
      
      if (readyResult.success) {
        return {
          success: true,
          containerId,
          port: readyResult.port
        };
      } else {
        return {
          success: false,
          error: readyResult.error
        };
      }
    } catch (error) {
      console.error('Error creating container:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 停止容器
   */
  async stopContainer(containerId: string): Promise<boolean> {
    try {
      const container = this.docker.getContainer(containerId);
      await container.stop();
      return true;
    } catch (error) {
      console.error('Error stopping container:', error);
      return false;
    }
  }

  /**
   * 删除容器
   */
  async removeContainer(containerId: string): Promise<boolean> {
    try {
      const container = this.docker.getContainer(containerId);
      await container.remove({ force: true });
      return true;
    } catch (error) {
      console.error('Error removing container:', error);
      return false;
    }
  }

  /**
   * 根据名称删除容器
   */
  private async removeContainerByName(name: string): Promise<void> {
    try {
      const containers = await this.docker.listContainers({ all: true });
      const existingContainer = containers.find((c: any) => 
        c.Names.some((n: string) => n === `/${name}` || n === name)
      );
      
      if (existingContainer) {
        const container = this.docker.getContainer(existingContainer.Id);
        try {
          await container.stop();
        } catch (error) {
          // 容器可能已经停止
        }
        await container.remove({ force: true });
      }
    } catch (error) {
      console.error('Error removing existing container:', error);
    }
  }

  /**
   * 拉取Docker镜像
   */
  private async pullImage(imageName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.docker.pull(imageName, (err: any, stream: any) => {
        if (err) {
          reject(err);
          return;
        }
        
        this.docker.modem.followProgress(stream, (err: any, res: any) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });
    });
  }

  /**
   * 从容器信息中提取端口
   */
  private extractPortFromContainer(containerInfo: any): number | undefined {
    try {
      const ports = containerInfo.NetworkSettings?.Ports;
      if (!ports) return undefined;
      
      for (const [containerPort, hostPorts] of Object.entries(ports)) {
        if (Array.isArray(hostPorts) && hostPorts.length > 0) {
          const hostPort = (hostPorts as any)[0]?.HostPort;
          if (hostPort) {
            return parseInt(hostPort, 10);
          }
        }
      }
    } catch (error) {
      console.error('Error extracting port:', error);
    }
    return undefined;
  }

  /**
   * 列出所有容器
   */
  async listContainers(): Promise<ContainerInfo[]> {
    try {
      const containers = await this.docker.listContainers({ all: true });
      return containers.map((container: any) => ({
        id: container.Id,
        name: container.Names[0]?.replace('/', '') || 'unknown',
        port: this.extractPortFromContainer(container) || 0,
        status: container.State
      }));
    } catch (error) {
      console.error('Error listing containers:', error);
      return [];
    }
  }
}

export const dockerService = new DockerService();
export type { ContainerInfo, CreateContainerOptions };