import type { BaseWorkspaceProps, BaseAgentConfig } from '@workspace/shared';

// 通用 Item 接口
export interface Item {
  primary: string; // 主要内容（俳句）
  secondary: string; // 次要内容（翻译或解释）
  extras: string[]; // 额外内容（主题、情感等）
  metadata?: any; // 元数据
}

// Agent 状态接口
export interface AgentState {
  items: Item[];
  currentItem?: Item;
  selectedIndex: number;
  topic: string;
  isGenerating: boolean;
  history: Array<{
    topic: string;
    item: Item;
    timestamp: number;
  }>;
  preferences: {
    style: string;
    outputFormat: string;
    includeExtras: boolean;
  };
  lastError?: string;
}

// Agent 配置接口
export interface AgentConfig extends BaseAgentConfig {
  // Haiku特定配置
  style?: string;
  outputFormat?: string;
  includeExtras?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  initialState?: AgentState;

  // 扩展模型配置（重新声明以确保兼容）
  model?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

// Tool 参数和结果类型
export interface GenerateParams {
  topic: string;
  style?: string;
  outputFormat?: string;
  includeExtras?: boolean;
}

export interface GenerateResult {
  primary: string;
  secondary: string;
  extras: string[];
  metadata?: any;
}

export interface EditParams {
  item: Item;
  instruction: string;
}

export interface EditResult {
  primary: string;
  secondary: string;
}

export interface SelectParams {
  items: Item[];
  criteria: string;
}

export interface SelectResult {
  selectedIndex: number;
  reason: string;
}

// Workspace Props - 继承基础属性并添加特定属性
export interface WorkspaceProps extends BaseWorkspaceProps {
  // 可以添加特定的 props
}
