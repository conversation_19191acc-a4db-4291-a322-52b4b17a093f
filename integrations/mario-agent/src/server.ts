/**
 * Mario Agent - 服务器端入口
 * 只包含服务器端需要的功能：数据、配置、agent 创建函数
 * 不包含 React 组件
 */

import type { IntegrationPackage } from '@workspace/shared';
import { mastraAgent } from './mastra-agent.js'; // 使用Mastra Agent
import { DEFAULT_CONFIG } from './config.js';

// 创建Agent的函数，返回Mastra Agent实例
export const createAgent = (config?: any) => {
  // 对于CopilotKit集成，我们需要返回Mastra Agent实例
  return mastraAgent;
};

// 服务器端 Integration 对象 - 不包含 Workspace 组件
export const MarioServerIntegration = {
  // 基础信息
  id: 'mario',
  name: 'Mario Integration',
  description: 'Mairo测试框架，能够自动生成EC测试用例和Thrift泛化测试用例',
  version: '0.0.1',

  // 核心功能
  createAgent,
  Agent: createAgent, // 别名

  // 配置
  defaultConfig: DEFAULT_CONFIG,

  // 工具
  tools: {},

  // 工具数组（用于注册）
  toolsArray: []
};

// 创建服务器端 Integration Package - 不包含 workspace
export const marioServerIntegrationPackage: IntegrationPackage = {
  id: MarioServerIntegration.id,
  name: MarioServerIntegration.name,
  description: MarioServerIntegration.description,
  version: MarioServerIntegration.version,
  createAgent: MarioServerIntegration.createAgent,
  workspace: undefined as any, // 服务器端不需要 workspace 组件
  defaultConfig: MarioServerIntegration.defaultConfig,
  tools: MarioServerIntegration.toolsArray
};

// 默认导出服务器端集成
export default MarioServerIntegration;

// 导出核心组件（服务器端）
export { DEFAULT_CONFIG } from './config.js';
export { mastraAgent } from './mastra-agent.js';

// 导出类型
export type { AgentConfig, AgentState, Item, GenerateParams, GenerateResult } from './types.js';
