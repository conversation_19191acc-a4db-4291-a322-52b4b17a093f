/**
 * 服务端专用导出
 * 仅包含核心 Agent 类和服务端工具
 */

// 核心 Agent 类（服务端可用）
export { MarioAgent } from './lib/mario-agent';

// 类型定义
export type {
  MarioAgentConfig,
  MarioMessage,
  MarioWebSocketMessage,
  ContainerInfo,
  MarioAgentEvent,
  MarioAgentState,
  StreamConfig,
  MarioTool,
  MarioAgentContext
} from './types';

// 重新导出 AG-UI 核心类型
export type {
  Message,
  Tool,
  AgentConfig,
  RunAgentInput,
  BaseEvent
} from '@ag-ui/client';

// 导入类型用于类定义
import { MarioAgent } from './lib/mario-agent';
import { MarioAgentConfig } from './types';

/**
 * Next.js API 路由助手
 */
export class MarioAgentHandler {
  private agent: MarioAgent;

  constructor(config: MarioAgentConfig) {
    this.agent = new MarioAgent(config);
  }

  /**
   * 处理 Next.js API 路由请求
   */
  async handleRequest(req: any, res: any) {
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
      const { input, threadId, runId } = req.body;
      
      const runInput = {
        threadId: threadId || `thread-${Date.now()}`,
        runId: runId || `run-${Date.now()}`,
        state: {},
        messages: [{ id: `msg-${Date.now()}`, role: 'user' as const, content: input }],
        tools: [],
        context: [],
        forwardedProps: {}
      };

      // 设置 SSE 响应头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // 订阅 agent 事件并流式响应
      const subscription = this.agent.runAgent(runInput).subscribe({
        next: (event: any) => {
          res.write(`data: ${JSON.stringify(event)}\n\n`);
        },
        error: (error: any) => {
          res.write(`data: ${JSON.stringify({ type: 'error', error: error.message })}\n\n`);
          res.end();
        },
        complete: () => {
          res.write(`data: ${JSON.stringify({ type: 'complete' })}\n\n`);
          res.end();
        }
      });

      // 处理客户端断开连接
      req.on('close', () => {
        subscription.unsubscribe();
      });

    } catch (error) {
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Internal server error' 
      });
    }
  }
}

/**
 * Express.js 中间件
 */
export function createMarioAgentMiddleware(config: MarioAgentConfig) {
  const handler = new MarioAgentHandler(config);
  
  return async (req: any, res: any, next: any) => {
    if (req.path === '/api/mario' && req.method === 'POST') {
      return handler.handleRequest(req, res);
    }
    next();
  };
}
