/**
 * AG-UI 错误处理工具
 * 提供统一的错误处理和用户友好的错误信息
 */

export interface AGUIError {
  type: 'connection' | 'validation' | 'ai_processing' | 'unknown';
  message: string;
  originalError?: Error;
  timestamp: Date;
}

/**
 * 创建标准化的 AG-UI 错误对象
 */
export function createAGUIError(
  type: AGUIError['type'],
  message: string,
  originalError?: Error
): AGUIError {
  return {
    type,
    message,
    originalError,
    timestamp: new Date(),
  };
}

/**
 * 验证消息输入
 * @param message 用户输入的消息
 * @returns 验证结果和错误信息
 */
export function validateMessageInput(message: string): {
  isValid: boolean;
  error?: AGUIError;
} {
  if (!message) {
    return {
      isValid: false,
      error: createAGUIError('validation', '消息不能为空'),
    };
  }

  const trimmedMessage = message.trim();
  if (trimmedMessage.length === 0) {
    return {
      isValid: false,
      error: createAGUIError('validation', '消息不能只包含空格'),
    };
  }

  if (trimmedMessage.length > 10000) {
    return {
      isValid: false,
      error: createAGUIError('validation', '消息长度不能超过 10000 个字符'),
    };
  }

  return { isValid: true };
}

/**
 * 验证 WebSocket 连接状态
 */
export function validateWebSocketConnection(ws: WebSocket | null): {
  isValid: boolean;
  error?: AGUIError;
} {
  if (!ws) {
    return {
      isValid: false,
      error: createAGUIError('connection', 'WebSocket 连接未建立'),
    };
  }

  if (ws.readyState !== WebSocket.OPEN) {
    const stateMessages: Record<number, string> = {
      [WebSocket.CONNECTING]: 'WebSocket 正在连接中',
      [WebSocket.CLOSING]: 'WebSocket 正在关闭',
      [WebSocket.CLOSED]: 'WebSocket 连接已关闭',
    };
    
    return {
      isValid: false,
      error: createAGUIError(
        'connection',
        stateMessages[ws.readyState] || 'WebSocket 连接状态异常'
      ),
    };
  }

  return { isValid: true };
}

/**
 * 处理 AI 处理错误
 */
export function handleAIProcessingError(error: unknown): AGUIError {
  if (error instanceof Error) {
    // 检查是否是 extract 函数相关错误
    if (error.message.includes('extract() failed') || 
        error.message.includes('No function call occurred')) {
      return createAGUIError(
        'ai_processing',
        'AI 处理失败：无法提取函数调用。请检查输入格式或重试。',
        error
      );
    }
    
    // 检查是否是生成失败错误
    if (error.message.includes('No generations found')) {
      return createAGUIError(
        'ai_processing',
        'AI 生成失败：未找到生成内容。请重试或检查模型配置。',
        error
      );
    }
    
    return createAGUIError('ai_processing', `AI 处理错误：${error.message}`, error);
  }
  
  return createAGUIError('unknown', '未知的 AI 处理错误');
}

/**
 * 获取用户友好的错误消息
 */
export function getUserFriendlyErrorMessage(error: AGUIError): string {
  const baseMessages = {
    connection: '连接问题',
    validation: '输入验证失败',
    ai_processing: 'AI 处理失败',
    unknown: '未知错误',
  };
  
  return `${baseMessages[error.type]}：${error.message}`;
}

/**
 * 记录错误到控制台（开发环境）或发送到监控服务（生产环境）
 */
export function logError(error: AGUIError, context?: string): void {
  const logMessage = `[AG-UI Error] ${context ? `[${context}] ` : ''}${error.type}: ${error.message}`;
  
  if (process.env.NODE_ENV === 'development') {
    console.error(logMessage, error.originalError);
  } else {
    // 在生产环境中，可以发送到监控服务
    console.error(logMessage);
    // TODO: 发送到监控服务（如 Sentry, LogRocket 等）
  }
}