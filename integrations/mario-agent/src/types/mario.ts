// Mario 相关的类型定义

// 消息类型
export interface MarioMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'error' | 'system' | 'interrupt';
  metadata?: {
    containerId?: string;
    port?: number;
    caseId?: string;
    [key: string]: any;
  };
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: 'message' | 'error' | 'status' | 'container_created' | 'case_generated';
  data: any;
  timestamp?: number;
}

// 容器配置
export interface ContainerConfig {
  image: string;
  name: string;
  ports: number[];
  environment?: Record<string, string>;
  workingDir?: string;
  command?: string[];
  volumes?: Array<{
    host: string;
    container: string;
    mode?: 'ro' | 'rw';
  }>;
}

// 容器信息
export interface ContainerInfo {
  id: string;
  name: string;
  status: 'creating' | 'running' | 'stopped' | 'error';
  ports: number[];
  image: string;
  createdAt: Date;
  healthStatus?: 'healthy' | 'unhealthy' | 'starting' | 'unknown';
}

// Docker 服务响应
export interface DockerServiceResponse {
  success: boolean;
  containerId: string;
  ports: number[];
  message?: string;
  error?: string;
}

// Mario 用例
export interface MarioCase {
  id: string;
  title: string;
  description: string;
  steps: string[];
  expectedResult: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  createdAt: Date;
  updatedAt: Date;
  containerId?: string;
  port?: number;
}

// Mario 输入
export interface MarioInput {
  message: string;
  mario_case?: MarioCase;
  port?: number;
  containerId?: string;
  model?: string;
  mcpServers?: string[];
}

// Mario 设置
export interface MarioSettings {
  model: string;
  mcpServers: string[];
  containerConfig: ContainerConfig;
  autoCreateContainer: boolean;
  autoSwitchToPreview: boolean;
}

// WebSocket 连接状态
export interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastConnectedAt: Date | null;
  reconnectAttempts: number;
}

// WebSocket 连接状态（兼容旧版本）
export interface WebSocketConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

// useMarioWebSocket Hook 返回类型
export interface UseMarioWebSocketReturn {
  messages: MarioMessage[];
  sendMessage: (input: MarioInput) => Promise<void>;
  clearMessages: () => void;
  connectionState: WebSocketConnectionState;
  isLoading: boolean;
  loadingMessage: string;
  currentStreamingMessage: string;
  connect: () => void;
  disconnect: () => void;
}

// 聊天状态
export interface ChatState {
  messages: MarioMessage[];
  isLoading: boolean;
  error: string | null;
  currentCase: MarioCase | null;
  container: ContainerInfo | null;
}

// 文件操作类型
export interface FileOperation {
  type: 'create' | 'update' | 'delete' | 'copy';
  path: string;
  content?: string;
  destination?: string;
}

// 终端消息类型
export interface TerminalMessage {
  type: 'input' | 'output' | 'resize' | 'init';
  data?: string;
  cols?: number;
  rows?: number;
}

// 健康检查结果
export interface HealthCheckResult {
  isHealthy: boolean;
  status: string;
  details?: {
    port: number;
    responseTime: number;
    statusCode?: number;
    error?: string;
  };
}

// 事件类型
export type MarioEvent =
  | { type: 'container_created'; data: ContainerInfo }
  | { type: 'container_stopped'; data: { id: string } }
  | { type: 'container_error'; data: { id: string; error: string } }
  | { type: 'case_generated'; data: MarioCase }
  | { type: 'case_completed'; data: { id: string; result: string } }
  | { type: 'message_received'; data: MarioMessage }
  | { type: 'websocket_connected'; data: { url: string } }
  | { type: 'websocket_disconnected'; data: { reason: string } };

// 组件 Props 类型
export interface MarioChatContainerProps {
  onContainerCreated?: (container: { id: string; port: number }) => void;
  className?: string;
}

export interface WebCodeFrameProps {
  port: number;
  containerId?: string;
  title?: string;
  className?: string;
  onFullscreenChange?: (isFullscreen: boolean) => void;
}

export interface WebTerminalFrameProps {
  port: number;
  containerId?: string;
  title?: string;
  className?: string;
  onFullscreenChange?: (isFullscreen: boolean) => void;
}

export interface MarioMessageListProps {
  messages: MarioMessage[];
  isLoading?: boolean;
  onMessageEdit?: (id: string, content: string) => void;
  onMessageDelete?: (id: string) => void;
  onMessageCopy?: (content: string) => void;
  className?: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 错误类型
export class MarioError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'MarioError';
  }
}

export class ContainerError extends MarioError {
  constructor(
    message: string,
    public containerId?: string,
    details?: any
  ) {
    super(message, 'CONTAINER_ERROR', details);
    this.name = 'ContainerError';
  }
}

export class WebSocketError extends MarioError {
  constructor(
    message: string,
    public url?: string,
    details?: any
  ) {
    super(message, 'WEBSOCKET_ERROR', details);
    this.name = 'WebSocketError';
  }
}

// 工具函数类型
export type MessageFormatter = (message: MarioMessage) => string;
export type EventHandler<T = any> = (event: T) => void | Promise<void>;
export type AsyncRetryFunction<T> = () => Promise<T>;

// 常量
export const MARIO_CONSTANTS = {
  DEFAULT_PORTS: [3000, 8080, 5000, 8000],
  DEFAULT_IMAGE: 'node:18-alpine',
  WEBSOCKET_RECONNECT_DELAY: 3000,
  CONTAINER_HEALTH_CHECK_TIMEOUT: 30000,
  MESSAGE_QUEUE_MAX_SIZE: 1000,
  TERMINAL_BUFFER_SIZE: 1000
} as const;

// 配置类型
export interface MarioConfig {
  apiUrl: string;
  websocketUrl: string;
  dockerApiUrl: string;
  defaultModel: string;
  defaultMcpServers: string[];
  containerDefaults: ContainerConfig;
  retryAttempts: number;
  timeouts: {
    websocket: number;
    container: number;
    healthCheck: number;
  };
}
