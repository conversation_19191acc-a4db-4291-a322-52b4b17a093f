import { useEffect, useRef, useState, useCallback } from 'react';
import type {
  MarioMessage,
  MarioInput,
  WebSocketConnectionState,
  UseMarioWebSocketReturn,
} from '../types/mario.js';
import { generateId, createMarioMessage } from '../utils/mario-utils.js';
import {
  validateMessageInput,
  validateWebSocketConnection,
  handleAIProcessingError,
  logError,
  getUserFriendlyErrorMessage,
} from '../utils/error-handler.js';

// AG-UI 事件类型
interface AGUIEvent {
  type: string;
  timestamp: string;
  thread_id?: string;
  run_id?: string;
  message_id?: string;
  role?: string;
  delta?: string;
  content?: string;
  tool_calls?: any[];
  tool_call_results?: any[];
  snapshot?: any;
  messages?: any[];
  error?: string;
}

interface UseAGUIWebSocketOptions {
  serverUrl?: string;
  onContainerCreated?: (containerId: string, port: number) => void;
  onError?: (error: string) => void;
}

interface ConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

interface LoadingState {
  isLoading: boolean;
  loadingMessage: string;
}

/**
 * AG-UI协议兼容的WebSocket钩子
 * 支持AG-UI标准事件格式
 */
export function useAGUIWebSocket(options: UseAGUIWebSocketOptions = {}) {
  const {
    serverUrl = process.env.NEXT_PUBLIC_MARIO_WS_URL || 'ws://localhost:8080/ws/ag-ui',
    onContainerCreated,
    onError
  } = options;

  // 状态管理
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isConnected: false,
    isConnecting: false,
    error: null
  });

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    loadingMessage: ''
  });

  const [messages, setMessages] = useState<MarioMessage[]>([]);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<string>('');

  // Refs
  const wsRef = useRef<WebSocket | null>(null);
  const currentMessageIdRef = useRef<string | null>(null);
  const currentStreamingMessageRef = useRef<string>('');
  const threadIdRef = useRef<string>(generateId());
  const runIdRef = useRef<string | null>(null);

  /**
   * 连接到AG-UI WebSocket服务
   */
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionState((prev) => ({ ...prev, isConnecting: true, error: null }));

    try {
      const ws = new WebSocket(serverUrl);

      ws.onopen = () => {
        console.log('AG-UI WebSocket connected');
        setConnectionState({
          isConnected: true,
          isConnecting: false,
          error: null
        });
      };

      ws.onclose = (event) => {
        console.log('AG-UI WebSocket disconnected:', event.reason);
        setConnectionState((prev) => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }));
      };

      ws.onerror = (error) => {
        console.error('AG-UI WebSocket error:', error);
        const errorMessage = 'WebSocket connection failed';
        setConnectionState({
          isConnected: false,
          isConnecting: false,
          error: errorMessage
        });
        onError?.(errorMessage);
      };

      ws.onmessage = (event) => {
        try {
          const aguiEvent: AGUIEvent = JSON.parse(event.data);
          handleAGUIEvent(aguiEvent);
        } catch (error) {
          console.error('Failed to parse AG-UI event:', error);
        }
      };

      wsRef.current = ws;
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionState({
        isConnected: false,
        isConnecting: false,
        error: 'Failed to create connection'
      });
    }
  }, [serverUrl, onError]);

  /**
   * 处理AG-UI事件
   */
  const handleAGUIEvent = useCallback((event: AGUIEvent) => {
    console.log('Received AG-UI event:', event);

    switch (event.type) {
      case 'run_started':
        runIdRef.current = event.run_id || null;
        setLoadingState({ isLoading: true, loadingMessage: '正在处理...' });
        break;

      case 'run_finished':
        setLoadingState({ isLoading: false, loadingMessage: '' });
        setCurrentStreamingMessage('');
        currentStreamingMessageRef.current = '';
        currentMessageIdRef.current = null;
        break;

      case 'text_message_start':
        currentMessageIdRef.current = event.message_id || generateId();
        currentStreamingMessageRef.current = '';
        setCurrentStreamingMessage('');
        break;

      case 'text_message_delta':
        if (event.message_id === currentMessageIdRef.current && event.delta) {
          currentStreamingMessageRef.current += event.delta;
          setCurrentStreamingMessage(currentStreamingMessageRef.current);
        }
        break;

      case 'text_message_end':
        if (event.message_id === currentMessageIdRef.current) {
          const finalContent = currentStreamingMessageRef.current;
          const newMessage: MarioMessage = {
            id: event.message_id || generateId(),
            role: 'assistant',
            content: finalContent,
            timestamp: new Date(),
            type: 'text'
          };

          setMessages((prev) => [...prev, newMessage]);
          setCurrentStreamingMessage('');
          currentStreamingMessageRef.current = '';
          currentMessageIdRef.current = null;
        }
        break;

      case 'tool_calls_start':
        if (event.tool_calls && event.tool_calls.length > 0) {
          const toolMessage: MarioMessage = {
            id: generateId(),
            role: 'assistant',
            content: `正在调用工具: ${event.tool_calls.map(tc => tc.function?.name || tc.name).join(', ')}`,
            timestamp: new Date(),
            type: 'system',
            metadata: { tool_calls: event.tool_calls }
          };
          setMessages((prev) => [...prev, toolMessage]);
        }
        break;

      case 'tool_calls_end':
        if (event.tool_call_results && event.tool_call_results.length > 0) {
          const resultMessage: MarioMessage = {
            id: generateId(),
            role: 'assistant',
            content: '工具调用完成',
            timestamp: new Date(),
            type: 'system',
            metadata: { tool_results: event.tool_call_results }
          };
          setMessages((prev) => [...prev, resultMessage]);
        }
        break;

      case 'state_snapshot':
        // 处理状态快照，可以用于显示工作流状态
        console.log('State snapshot:', event.snapshot);
        break;

      case 'messages_snapshot':
        // 处理消息快照
        if (event.messages) {
          const convertedMessages: MarioMessage[] = event.messages.map((msg: any) => ({
            id: msg.id || generateId(),
            role: msg.role || 'assistant',
            content: msg.content || '',
            timestamp: new Date(msg.timestamp || Date.now()),
            type: 'text'
          }));
          setMessages(convertedMessages);
        }
        break;

      case 'error':
        const errorMessage: MarioMessage = {
          id: generateId(),
          role: 'assistant',
          content: `错误: ${event.error}`,
          timestamp: new Date(),
          type: 'error'
        };
        setMessages((prev) => [...prev, errorMessage]);
        setLoadingState({ isLoading: false, loadingMessage: '' });
        onError?.(event.error || '未知错误');
        break;

      case 'interrupt':
        const interruptMessage: MarioMessage = {
          id: generateId(),
          role: 'assistant',
          content: `中断: ${event.error || '工作流被中断'}`,
          timestamp: new Date(),
          type: 'interrupt'
        };
        setMessages((prev) => [...prev, interruptMessage]);
        break;

      default:
        console.log('Unhandled AG-UI event type:', event.type);
    }
  }, [onError, onContainerCreated]);

  /**
   * 断开连接
   */
  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setConnectionState({
      isConnected: false,
      isConnecting: false,
      error: null
    });
  }, []);

  /**
   * 发送消息到 AG-UI WebSocket
   * 使用统一的错误处理和验证
   */
  const sendMessage = useCallback(
    async (input: MarioInput) => {
      // 验证消息输入
      const messageValidation = validateMessageInput(input.message);
      if (!messageValidation.isValid && messageValidation.error) {
        const errorMessage = getUserFriendlyErrorMessage(messageValidation.error);
        logError(messageValidation.error, 'sendMessage');
        onError?.(errorMessage);
        return;
      }

      // 验证 WebSocket 连接状态
      const connectionValidation = validateWebSocketConnection(wsRef.current);
      if (!connectionValidation.isValid && connectionValidation.error) {
        const errorMessage = getUserFriendlyErrorMessage(connectionValidation.error);
        logError(connectionValidation.error, 'sendMessage');
        onError?.(errorMessage);
        return;
      }

      // 添加用户消息到列表
      const userMessage: MarioMessage = {
        id: generateId(),
        role: 'user',
        content: input.message,
        timestamp: new Date(),
        type: 'text',
        metadata: {
          mario_case: input.mario_case,
          port: input.port,
          model: input.model,
          mcpServers: input.mcpServers
        }
      };

      setMessages((prev) => [...prev, userMessage]);
      setLoadingState({ isLoading: true, loadingMessage: '正在处理...' });

      // 生成新的运行ID
      runIdRef.current = generateId();

      try {
        // 构建AG-UI格式的请求
        const aguiRequest = {
          type: 'run',
          messages: [
            {
              role: 'user',
              content: input.message.trim() // 确保内容已清理
            }
          ],
          thread_id: threadIdRef.current,
          run_id: runIdRef.current,
          tools: [],
          state: {
            mario_case: input.mario_case,
            port: input.port || 8080,
            model: input.model,
            mcpServers: input.mcpServers
          }
        };

        // 验证构建的请求对象
         if (!aguiRequest.messages[0]?.content) {
           console.error('AG-UI request content is empty after processing');
           return;
         }
 
         // 发送到AG-UI服务
         if (wsRef.current) {
           wsRef.current.send(JSON.stringify(aguiRequest));
         }
         console.log('Message sent to AG-UI:', aguiRequest);
       } catch (error) {
         const aguiError = handleAIProcessingError(error);
         const errorMessage = getUserFriendlyErrorMessage(aguiError);
         logError(aguiError, 'sendMessage');
         onError?.(errorMessage);
       }
    },
    [onError]
  );

  /**
   * 停止当前操作
   */
  const stopCurrentOperation = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'interrupt_response',
        response: 'stop'
      }));
      setLoadingState({ isLoading: false, loadingMessage: '' });
      setCurrentStreamingMessage('');
      currentStreamingMessageRef.current = '';
      currentMessageIdRef.current = null;
    }
  }, []);

  /**
   * 清空消息
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentStreamingMessage('');
    currentStreamingMessageRef.current = '';
    currentMessageIdRef.current = null;
    // 重置线程ID
    threadIdRef.current = generateId();
  }, []);

  /**
   * 重新连接
   */
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 1000);
  }, [disconnect, connect]);

  // 组件挂载时自动连接
  const hasConnectedRef = useRef(false);
  useEffect(() => {
    if (!hasConnectedRef.current) {
      hasConnectedRef.current = true;
      connect();
    }

    return () => {
      disconnect();
    };
  }, []);

  return {
    // 状态
    isConnected: connectionState.isConnected,
    isConnecting: connectionState.isConnecting,
    connectionError: connectionState.error,
    isLoading: loadingState.isLoading,
    loadingMessage: loadingState.loadingMessage,

    // 消息
    messages,
    currentStreamingMessage,

    // 方法
    sendMessage,
    stopCurrentOperation,
    clearMessages,
    connect,
    disconnect,
    reconnect
  };
}

export type { UseAGUIWebSocketOptions };