import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';
import type {
  MarioMessage,
  MarioInput,
  WebSocketMessage,
  WebSocketConnectionState,
  UseMarioWebSocketReturn,
  MarioSettings
} from '../types/mario.js';
import { MARIO_CONFIG } from '../config/mario-config.js';
import { generateId, createMarioMessage } from '../utils/mario-utils.js';

// 导出类型供其他组件使用
export type { MarioMessage, MarioInput } from '../types/mario.js';

interface UseMarioWebSocketOptions {
  serverUrl?: string;
  onContainerCreated?: (containerId: string, port: number) => void;
  onError?: (error: string) => void;
}

interface ConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

interface LoadingState {
  isLoading: boolean;
  loadingMessage: string;
}

export function useMarioWebSocket(options: UseMarioWebSocketOptions = {}) {
  const {
    serverUrl = process.env.NEXT_PUBLIC_MARIO_WS_URL || 'http://localhost:3001',
    onContainerCreated,
    onError
  } = options;

  // 状态管理
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isConnected: false,
    isConnecting: false,
    error: null
  });

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    loadingMessage: ''
  });

  const [messages, setMessages] = useState<MarioMessage[]>([]);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<string>('');

  // Refs - 使用 ref 来存储流式消息状态，避免依赖循环
  const socketRef = useRef<Socket | null>(null);
  const messageQueueRef = useRef<string[]>([]);
  const isProcessingRef = useRef(false);
  const currentMessageIdRef = useRef<string | null>(null);
  const currentStreamingMessageRef = useRef<string>('');

  // 连接WebSocket - 移除 currentStreamingMessage 依赖，使用 ref 代替
  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return;
    }

    setConnectionState((prev) => ({ ...prev, isConnecting: true, error: null }));

    const socket = io(serverUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    socket.on('connect', () => {
      console.log('Mario WebSocket connected');
      setConnectionState({
        isConnected: true,
        isConnecting: false,
        error: null
      });
    });

    socket.on('disconnect', (reason: any) => {
      console.log('Mario WebSocket disconnected:', reason);
      setConnectionState((prev) => ({
        ...prev,
        isConnected: false,
        isConnecting: false
      }));
    });

    socket.on('connect_error', (error: any) => {
      console.error('Mario WebSocket connection error:', error);
      const errorMessage = error.message || 'Connection failed';
      setConnectionState({
        isConnected: false,
        isConnecting: false,
        error: errorMessage
      });
      onError?.(errorMessage);
    });

    // 处理消息流 - 使用 ref 来避免依赖循环
    socket.on('message_stream', (data: { content: string; isComplete: boolean; messageId: string }) => {
      const { content, isComplete, messageId } = data;

      if (messageId !== currentMessageIdRef.current) {
        // 新消息开始
        currentMessageIdRef.current = messageId;
        currentStreamingMessageRef.current = content;
        setCurrentStreamingMessage(content);
      } else {
        // 继续流式输出
        currentStreamingMessageRef.current = currentStreamingMessageRef.current + content;
        setCurrentStreamingMessage(currentStreamingMessageRef.current);
      }

      if (isComplete) {
        // 流式输出完成，添加到消息列表
        const finalContent = currentStreamingMessageRef.current;
        const newMessage: MarioMessage = {
          id: messageId,
          role: 'assistant',
          content: finalContent,
          timestamp: new Date(),
          type: 'text'
        };

        setMessages((prev) => [...prev, newMessage]);
        setCurrentStreamingMessage('');
        currentStreamingMessageRef.current = '';
        currentMessageIdRef.current = null;
        setLoadingState({ isLoading: false, loadingMessage: '' });
      }
    });

    // 处理系统消息
    socket.on('system_message', (data: { content: string; type: string; metadata?: any }) => {
      const systemMessage: MarioMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: data.content,
        timestamp: new Date(),
        type: data.type as any,
        metadata: data.metadata
      };

      setMessages((prev) => [...prev, systemMessage]);
    });

    // 处理容器创建事件
    socket.on('container_created', (data: { containerId: string; port: number }) => {
      console.log('Container created:', data);
      onContainerCreated?.(data.containerId, data.port);

      const systemMessage: MarioMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: `容器已创建，端口: ${data.port}`,
        timestamp: new Date(),
        type: 'system',
        metadata: {
          containerId: data.containerId,
          port: data.port
        }
      };

      setMessages((prev) => [...prev, systemMessage]);
    });

    // 处理错误
    socket.on('error', (data: { message: string; details?: any }) => {
      console.error('Mario WebSocket error:', data);
      const errorMessage: MarioMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: `错误: ${data.message}`,
        timestamp: new Date(),
        type: 'error',
        metadata: data.details
      };

      setMessages((prev) => [...prev, errorMessage]);
      setLoadingState({ isLoading: false, loadingMessage: '' });
      onError?.(data.message);
    });

    // 处理加载状态
    socket.on('loading_status', (data: { isLoading: boolean; message: string }) => {
      setLoadingState({
        isLoading: data.isLoading,
        loadingMessage: data.message
      });
    });

    socketRef.current = socket;
  }, [serverUrl, onContainerCreated, onError]); // 移除 currentStreamingMessage 依赖

  // 断开连接
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    setConnectionState({
      isConnected: false,
      isConnecting: false,
      error: null
    });
  }, []);

  // 发送消息
  const sendMessage = useCallback(
    async (input: MarioInput) => {
      if (!socketRef.current?.connected) {
        const error = 'WebSocket not connected';
        onError?.(error);
        return;
      }

      // 添加用户消息到列表
      const userMessage: MarioMessage = {
        id: uuidv4(),
        role: 'user',
        content: input.message,
        timestamp: new Date(),
        type: 'text',
        metadata: {
          mario_case: input.mario_case,
          port: input.port,
          model: input.model,
          mcpServers: input.mcpServers
        }
      };

      setMessages((prev) => [...prev, userMessage]);
      setLoadingState({ isLoading: true, loadingMessage: '正在处理...' });

      // 发送到服务器
      socketRef.current.emit('mario_message', {
        message: input.message,
        mario_case: input.mario_case,
        port: input.port,
        model: input.model,
        mcpServers: input.mcpServers,
        timestamp: new Date().toISOString()
      });
    },
    [onError]
  );

  // 停止当前操作
  const stopCurrentOperation = useCallback(() => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('stop_operation');
      setLoadingState({ isLoading: false, loadingMessage: '' });
      setCurrentStreamingMessage('');
      currentStreamingMessageRef.current = '';
      currentMessageIdRef.current = null;
    }
  }, []);

  // 清空消息
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentStreamingMessage('');
    currentStreamingMessageRef.current = '';
    currentMessageIdRef.current = null;
  }, []);

  // 重新连接
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 1000);
  }, [disconnect, connect]);

  // 组件挂载时自动连接 - 使用 useRef 来避免重复连接
  const hasConnectedRef = useRef(false);
  useEffect(() => {
    if (!hasConnectedRef.current) {
      hasConnectedRef.current = true;
      connect();
    }

    return () => {
      disconnect();
    };
  }, []); // 移除 connect, disconnect 依赖，使用 ref 控制

  // 处理消息队列 - 优化触发条件，避免不必要的处理
  useEffect(() => {
    const processQueue = async () => {
      if (isProcessingRef.current || messageQueueRef.current.length === 0) {
        return;
      }

      isProcessingRef.current = true;

      while (messageQueueRef.current.length > 0) {
        const message = messageQueueRef.current.shift();
        if (message) {
          // 处理消息
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      isProcessingRef.current = false;
    };

    // 只在消息队列有内容时处理
    if (messageQueueRef.current.length > 0) {
      processQueue();
    }
  }, [messages.length]); // 只依赖消息数量，而不是整个消息数组

  return {
    // 状态
    isConnected: connectionState.isConnected,
    isConnecting: connectionState.isConnecting,
    connectionError: connectionState.error,
    isLoading: loadingState.isLoading,
    loadingMessage: loadingState.loadingMessage,

    // 消息
    messages,
    currentStreamingMessage,

    // 方法
    sendMessage,
    stopCurrentOperation,
    clearMessages,
    connect,
    disconnect,
    reconnect
  };
}

export type { UseMarioWebSocketOptions };
