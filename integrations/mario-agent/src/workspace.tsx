import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@workspace/ui/components/ui/button';
import { Card, CardContent } from '@workspace/ui/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/ui/tabs';
import { Badge } from '@workspace/ui/components/ui/badge';
import { Separator } from '@workspace/ui/components/ui/separator';
import { 
  Maximize2, 
  Minimize2, 
  MessageSquare, 
  Globe, 
  Terminal, 
  Settings,
  Play,
  Square,
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { MarioChatContainer } from './components/MarioChatContainer.js';
import { WebCodeFrame } from './components/WebCodeFrame.js';
import { WebTerminalFrame } from './components/WebTerminalFrame.js';
import { dockerService } from './services/docker-service.js';

interface WorkspaceProps {
  className?: string;
}

interface ContainerInfo {
  id: string;
  port: number;
  status: 'creating' | 'running' | 'stopped' | 'error';
  healthStatus: 'healthy' | 'unhealthy' | 'starting' | 'unknown';
  createdAt: Date;
}

export function Workspace({ className }: WorkspaceProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');
  const [container, setContainer] = useState<ContainerInfo | null>(null);
  const [isCreatingContainer, setIsCreatingContainer] = useState(false);

  // 处理全屏切换
  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 监听ESC键退出全屏
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullscreen]);

  // 创建容器
  const handleCreateContainer = useCallback(async () => {
    if (isCreatingContainer) return;
    
    setIsCreatingContainer(true);
    
    try {
      const result = await dockerService.createContainer({
        image: 'node:18-alpine',
        name: `mario-workspace-${Date.now()}`,
        ports: [3000, 8080],
        environment: {
          NODE_ENV: 'development'
        },
        workingDir: '/workspace',
        command: ['sh', '-c', 'while true; do sleep 30; done']
      });
      
      const newContainer: ContainerInfo = {
        id: result.containerId,
        port: result.ports[0],
        status: 'creating',
        healthStatus: 'starting',
        createdAt: new Date()
      };
      
      setContainer(newContainer);
      toast.success('容器创建成功');
      
      // 等待容器启动并检查健康状态
      setTimeout(async () => {
        try {
          const isHealthy = await dockerService.waitForContainerReady(result.containerId, 30000);
          setContainer(prev => prev ? {
            ...prev,
            status: 'running',
            healthStatus: isHealthy ? 'healthy' : 'unhealthy'
          } : null);
        } catch (error) {
          console.error('Container health check failed:', error);
          setContainer(prev => prev ? {
            ...prev,
            status: 'error',
            healthStatus: 'unhealthy'
          } : null);
        }
      }, 2000);
      
    } catch (error: any) {
      console.error('Failed to create container:', error);
      toast.error('容器创建失败');
    } finally {
      setIsCreatingContainer(false);
    }
  }, [isCreatingContainer]);

  // 停止容器
  const handleStopContainer = useCallback(async () => {
    if (!container) return;
    
    try {
      await dockerService.stopContainer(container.id);
      setContainer(prev => prev ? { ...prev, status: 'stopped' } : null);
      toast.success('容器已停止');
    } catch (error: any) {
      console.error('Failed to stop container:', error);
      toast.error('停止容器失败');
    }
  }, [container]);

  // 删除容器
  const handleRemoveContainer = useCallback(async () => {
    if (!container) return;
    
    try {
      await dockerService.removeContainer(container.id);
      setContainer(null);
      toast.success('容器已删除');
    } catch (error: any) {
      console.error('Failed to remove container:', error);
      toast.error('删除容器失败');
    }
  }, [container]);

  // 容器状态指示器
  const ContainerStatus = () => {
    if (!container) {
      return (
        <Badge variant="secondary" className="gap-1">
          <AlertCircle className="h-3 w-3" />
          无容器
        </Badge>
      );
    }
    
    const statusConfig = {
      creating: { variant: 'outline' as const, icon: RefreshCw, text: '创建中', spin: true },
      running: { variant: 'default' as const, icon: CheckCircle, text: '运行中', spin: false },
      stopped: { variant: 'secondary' as const, icon: Square, text: '已停止', spin: false },
      error: { variant: 'destructive' as const, icon: AlertCircle, text: '错误', spin: false }
    };
    
    const config = statusConfig[container.status];
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="gap-1">
        <Icon className={`h-3 w-3 ${config.spin ? 'animate-spin' : ''}`} />
        {config.text}
      </Badge>
    );
  };

  // 处理容器创建回调（从聊天组件传递）
  const handleContainerCreated = useCallback((containerInfo: { id: string; port: number }) => {
    const newContainer: ContainerInfo = {
      id: containerInfo.id,
      port: containerInfo.port,
      status: 'running',
      healthStatus: 'healthy',
      createdAt: new Date()
    };
    
    setContainer(newContainer);
    
    // 自动切换到相应的tab
    if (activeTab === 'chat') {
      setActiveTab('web');
    }
  }, [activeTab]);

  return (
    <div className={`${className} ${isFullscreen ? 'fixed inset-0 z-50 bg-background' : 'h-full'} flex flex-col`}>
      {/* Canvas Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-semibold">Mario Agent Workspace</h1>
          
          <ContainerStatus />
          
          {container && (
            <Badge variant="outline" className="text-xs">
              端口: {container.port}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {/* 容器控制按钮 */}
          {!container ? (
            <Button
              variant="outline"
              size="sm"
              onClick={handleCreateContainer}
              disabled={isCreatingContainer}
              className="gap-2"
            >
              {isCreatingContainer ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  创建中...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  创建容器
                </>
              )}
            </Button>
          ) : (
            <>
              {container.status === 'running' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStopContainer}
                  className="gap-2"
                >
                  <Square className="h-4 w-4" />
                  停止
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleRemoveContainer}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                删除
              </Button>
            </>
          )}
          
          <Separator orientation="vertical" className="h-6" />
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleFullscreen}
            className="gap-2"
          >
            {isFullscreen ? (
              <>
                <Minimize2 className="h-4 w-4" />
                退出全屏
              </>
            ) : (
              <>
                <Maximize2 className="h-4 w-4" />
                全屏
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Canvas Content */}
      <div className="flex-1 p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="chat" className="gap-2">
              <MessageSquare className="h-4 w-4" />
              聊天
            </TabsTrigger>
            <TabsTrigger value="web" className="gap-2" disabled={!container || container.status !== 'running'}>
              <Globe className="h-4 w-4" />
              网页预览
            </TabsTrigger>
            <TabsTrigger value="terminal" className="gap-2" disabled={!container || container.status !== 'running'}>
              <Terminal className="h-4 w-4" />
              终端
            </TabsTrigger>
            <TabsTrigger value="settings" className="gap-2">
              <Settings className="h-4 w-4" />
              设置
            </TabsTrigger>
          </TabsList>
          
          <div className="flex-1 mt-4">
            <TabsContent value="chat" className="h-full m-0">
              <MarioChatContainer
                onContainerCreated={handleContainerCreated}
                className="h-full"
              />
            </TabsContent>
            
            <TabsContent value="web" className="h-full m-0">
              {container && container.status === 'running' ? (
                <WebCodeFrame
                  port={container.port}
                  containerId={container.id}
                  title="容器网页预览"
                  className="h-full"
                  onFullscreenChange={(fullscreen: boolean) => {
                    if (fullscreen) setIsFullscreen(true);
                  }}
                />
              ) : (
                <Card className="h-full">
                  <CardContent className="flex items-center justify-center h-full">
                    <div className="text-center space-y-4">
                      <Globe className="h-12 w-12 text-muted-foreground mx-auto" />
                      <div>
                        <h3 className="text-lg font-medium">网页预览不可用</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          请先创建并启动容器
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="terminal" className="h-full m-0">
              {container && container.status === 'running' ? (
                <WebTerminalFrame
                  port={container.port}
                  containerId={container.id}
                  title="容器终端"
                  className="h-full"
                  onFullscreenChange={(fullscreen: boolean) => {
                    if (fullscreen) setIsFullscreen(true);
                  }}
                />
              ) : (
                <Card className="h-full">
                  <CardContent className="flex items-center justify-center h-full">
                    <div className="text-center space-y-4">
                      <Terminal className="h-12 w-12 text-muted-foreground mx-auto" />
                      <div>
                        <h3 className="text-lg font-medium">终端不可用</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          请先创建并启动容器
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="settings" className="h-full m-0">
              <Card className="h-full">
                <CardContent className="p-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-4">容器设置</h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium">默认镜像</label>
                            <p className="text-sm text-muted-foreground">node:18-alpine</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium">默认端口</label>
                            <p className="text-sm text-muted-foreground">3000, 8080</p>
                          </div>
                        </div>
                        
                        {container && (
                          <div className="space-y-2">
                            <label className="text-sm font-medium">当前容器信息</label>
                            <div className="bg-muted p-3 rounded text-sm space-y-1">
                              <div>ID: {container.id}</div>
                              <div>端口: {container.port}</div>
                              <div>状态: {container.status}</div>
                              <div>健康状态: {container.healthStatus}</div>
                              <div>创建时间: {container.createdAt.toLocaleString()}</div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="text-lg font-medium mb-4">Mario Agent 设置</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">工作模式</label>
                          <p className="text-sm text-muted-foreground">智能代码生成与容器管理</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium">支持功能</label>
                          <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
                            <li>AI 驱动的代码生成</li>
                            <li>Docker 容器管理</li>
                            <li>实时网页预览</li>
                            <li>集成终端访问</li>
                            <li>WebSocket 通信</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
