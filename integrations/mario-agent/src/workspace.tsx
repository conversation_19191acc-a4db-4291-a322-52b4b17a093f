'use client';

import { Badge } from '@workspace/ui/components/badge';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Lightbulb, Maximize, Minimize, TestTube } from 'lucide-react';
import { AnimatedGradientText } from '@workspace/ui/components/magicui/animated-gradient-text';
import { BaseWorkspaceProps } from '@workspace/shared';
import { createAgent } from './agent.js';
import { useRef, useState, useEffect } from 'react';
import { useAGUIWebSocket } from './hooks/useAGUIWebSocket.js';
import type { MarioMessage } from './types/mario.js';
import { validateMessageInput, getUserFriendlyErrorMessage } from './utils/error-handler.js';

/**
 * Agent 工作区组件
 * 提供 Mario 测试用例生成界面，集成 AG-UI WebSocket 功能
 */
export function Workspace({ setIsAgentActive, isAgentActive }: BaseWorkspaceProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showAGUIPanel, setShowAGUIPanel] = useState(false);
  const [inputMessage, setInputMessage] = useState('');
  // 使用 useAGUIWebSocket 返回的 messages 作为消息历史

  // 创建 Agent 实例
  const agent = useRef(createAgent());

  // AG-UI WebSocket 连接
  const {
    isConnected,
    isConnecting,
    connectionError,
    isLoading,
    loadingMessage,
    messages,
    currentStreamingMessage,
    sendMessage,
    stopCurrentOperation,
    clearMessages,
    connect,
    disconnect,
    reconnect
  } = useAGUIWebSocket({
    serverUrl: 'ws://localhost:8080/ws/ag-ui',
    onContainerCreated: (containerId: string, port: number) => {
      console.log('Container created:', containerId, port);
    },
    onError: (error) => {
      console.error('AG-UI WebSocket error:', error);
    }
  });

  /**
   * 切换全屏模式
   */
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  /**
   * 切换 AG-UI 面板显示
   */
  const toggleAGUIPanel = () => {
    setShowAGUIPanel(!showAGUIPanel);
    if (!showAGUIPanel && !isConnected) {
      connect();
    }
  };

  /**
   * 处理发送消息的函数
   * 使用统一的错误处理和验证
   */
  const handleSendMessage = async () => {
    // 使用统一的消息验证
    const validation = validateMessageInput(inputMessage);
    if (!validation.isValid && validation.error) {
      const errorMessage = getUserFriendlyErrorMessage(validation.error);
      console.warn(errorMessage);
      // 可以在这里显示用户友好的错误提示
      return;
    }
    
    // 确保连接状态正常
    if (!isConnected) {
      console.warn('WebSocket 未连接，无法发送消息');
      return;
    }
    
    try {
      await sendMessage({
        message: inputMessage.trim(),
        mario_case: undefined,
        port: 8080,
        model: 'gpt-4o-2024-11-20',
        mcpServers: []
      });
      setInputMessage('');
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  /**
   * 处理键盘事件
   */
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  /**
   * 监听ESC键退出全屏
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullscreen]);

  return (
    <div className={`flex h-full flex-col ${isFullscreen ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900' : ''}`}>
      {/* Canvas Header */}
      <div className='h-12 w-full border-b border-gray-200 dark:border-gray-700'>
        <div className='flex h-full items-center justify-between px-6'>
          <div className='flex items-center gap-4'>
            <Button
              variant='outline'
              size='sm'
              onClick={toggleFullscreen}
              className='h-8 w-8 border-gray-300 p-0 transition-colors hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-800'
              title={isFullscreen ? '退出全屏' : '全屏'}>
              {isFullscreen ? <Minimize className='h-4 w-4' /> : <Maximize className='h-4 w-4' />}
            </Button>

            <Button
              variant={showAGUIPanel ? 'default' : 'outline'}
              size='sm'
              onClick={toggleAGUIPanel}
              className='h-8 gap-2 px-3'
              title={showAGUIPanel ? '关闭 AG-UI 面板' : '打开 AG-UI 面板'}>
              <TestTube className='h-4 w-4' />
              <span className='text-xs'>AG-UI</span>
            </Button>

            {isAgentActive && (
              <Badge variant='default' className='h-8 gap-2'>
                <Lightbulb className='h-3 w-3' />
                <AnimatedGradientText>Agent Contributing</AnimatedGradientText>
              </Badge>
            )}

            {isConnected && (
              <Badge variant='secondary' className='h-8 gap-2'>
                <div className='h-2 w-2 rounded-full bg-green-500'></div>
                <span className='text-xs'>AG-UI Connected</span>
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Canvas Content */}
      <div className='flex-1'>
        {showAGUIPanel ? (
          <div className='flex h-full'>
            {/* 左侧：原始 Canvas */}
            <div className='flex w-1/2 items-center justify-center border-r border-gray-200 dark:border-gray-700'>
              <div className='text-center'>
                <h2 className='mb-4 text-2xl font-bold text-gray-800 dark:text-gray-200'>Mario Agent Canvas</h2>
                <p className='text-gray-600 dark:text-gray-400'>Canvas content area for mario creation and editing</p>
              </div>
            </div>
            
            {/* 右侧：AG-UI 面板 */}
            <div className='flex w-1/2 flex-col'>
              {/* AG-UI 头部 */}
              <div className='border-b border-gray-200 p-4 dark:border-gray-700'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-semibold text-gray-800 dark:text-gray-200'>AG-UI WebSocket</h3>
                  <div className='flex items-center gap-2'>
                    <div className={`h-2 w-2 rounded-full ${
                      isConnected ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      {isConnecting ? '连接中...' : isConnected ? '已连接' : '未连接'}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* 消息历史 */}
              <div className='flex-1 overflow-y-auto p-4'>
                <div className='space-y-3'>
                  {messages.length === 0 ? (
                    <div className='text-center text-gray-500 dark:text-gray-400'>
                      <p>暂无消息</p>
                      <p className='text-sm'>发送消息开始与 Mario Agent 对话</p>
                    </div>
                  ) : (
                    messages.map((message, index) => (
                      <div key={index} className='rounded-lg border border-gray-200 p-3 dark:border-gray-700'>
                        <div className='mb-2 flex items-center justify-between'>
                          <span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                            {message.type}
                          </span>
                          <span className='text-xs text-gray-500 dark:text-gray-400'>
                            {new Date().toLocaleTimeString()}
                          </span>
                        </div>
                        <div className='text-sm text-gray-600 dark:text-gray-400'>
                          {JSON.stringify(message, null, 2)}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
              
              {/* 输入区域 */}
              <div className='border-t border-gray-200 p-4 dark:border-gray-700'>
                <div className='flex gap-2'>
                  <input
                    type='text'
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder='输入消息...'
                    className='flex-1 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200'
                    disabled={!isConnected}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!isConnected || !inputMessage.trim()}
                    size='sm'
                    className='px-4'>
                    发送
                  </Button>
                </div>
                
                {connectionError && (
                  <div className='mt-2 text-sm text-red-600 dark:text-red-400'>
                    错误: {connectionError}
                  </div>
                )}
                
                <div className='mt-2 flex gap-2'>
                  <Button
                    onClick={connect}
                    disabled={isConnected}
                    variant='outline'
                    size='sm'>
                    连接
                  </Button>
                  <Button
                    onClick={disconnect}
                    disabled={!isConnected}
                    variant='outline'
                    size='sm'>
                    断开
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className='flex h-full w-full items-center justify-center'>
            <div className='text-center'>
              <h2 className='mb-4 text-2xl font-bold text-gray-800 dark:text-gray-200'>Mario Agent Canvas</h2>
              <p className='text-gray-600 dark:text-gray-400'>Canvas content area for mario creation and editing</p>
              <div className='mt-6'>
                <Button onClick={toggleAGUIPanel} className='gap-2'>
                  <TestTube className='h-4 w-4' />
                  打开 AG-UI 面板
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
