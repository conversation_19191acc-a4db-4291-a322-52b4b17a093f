/**
 * <PERSON> 聊天组件
 * 基于原有聊天界面实现，集成 useMarioAgent Hook
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useMarioAgent } from '../hooks/useMarioAgent';
import type { MarioMessage, ContainerInfo, UseMarioAgentConfig } from '../types';

/**
 * <PERSON> 组件属性
 */
export interface MarioChatProps extends Omit<UseMarioAgentConfig, 'enabled'> {
  /** 组件类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否显示头部 */
  showHeader?: boolean;
  /** 标题 */
  title?: string;
  /** 输入框占位符 */
  placeholder?: string;
  /** 是否显示清空按钮 */
  showClearButton?: boolean;
  /** 是否显示重连按钮 */
  showReconnectButton?: boolean;
  /** 高度 */
  height?: string | number;
  /** 是否启用 */
  enabled?: boolean;
}

/**
 * <PERSON> 聊天组件
 */
export function MarioChat({
  url,
  username,
  debug = false,
  timeout,
  onCreateContainer,
  onMuseInput,
  onConnectionChange,
  onError,
  className = '',
  style,
  showHeader = true,
  title = 'Mario 助手',
  placeholder = '输入你的问题...',
  showClearButton = true,
  showReconnectButton = true,
  height = '600px',
  enabled = true
}: MarioChatProps) {
  
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 使用 Mario Agent Hook
  const {
    isConnected,
    isLoading,
    error,
    messages,
    sendMessage,
    clearMessages,
    reconnect
  } = useMarioAgent({
    url,
    username,
    enabled,
    debug,
    timeout,
    onCreateContainer,
    onMuseInput,
    onConnectionChange,
    onError
  });

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 聚焦输入框
  useEffect(() => {
    if (isConnected && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isConnected]);

  /**
   * 处理发送消息
   */
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputValue.trim() || isLoading || !isConnected) {
      return;
    }

    const content = inputValue.trim();
    setInputValue('');
    
    try {
      await sendMessage(content);
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  /**
   * 处理键盘事件
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  /**
   * 处理清空消息
   */
  const handleClearMessages = () => {
    clearMessages();
    setInputValue('');
  };

  return (
    <div 
      className={`mario-chat ${className}`}
      style={{
        display: 'flex',
        flexDirection: 'column',
        height,
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        backgroundColor: '#fff',
        overflow: 'hidden',
        ...style
      }}
    >
      {/* 头部 */}
      {showHeader && (
        <div 
          style={{
            padding: '16px',
            borderBottom: '1px solid #e0e0e0',
            backgroundColor: '#f8f9fa',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>
              {title}
            </h3>
            <div 
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: isConnected ? '#10b981' : '#ef4444'
              }}
              title={isConnected ? '已连接' : '未连接'}
            />
          </div>
          
          <div style={{ display: 'flex', gap: '8px' }}>
            {showClearButton && (
              <button
                onClick={handleClearMessages}
                style={{
                  padding: '4px 8px',
                  fontSize: '12px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  backgroundColor: '#fff',
                  cursor: 'pointer'
                }}
                title="清空消息"
              >
                清空
              </button>
            )}
            
            {showReconnectButton && !isConnected && (
              <button
                onClick={reconnect}
                style={{
                  padding: '4px 8px',
                  fontSize: '12px',
                  border: '1px solid #007acc',
                  borderRadius: '4px',
                  backgroundColor: '#007acc',
                  color: 'white',
                  cursor: 'pointer'
                }}
                title="重新连接"
              >
                重连
              </button>
            )}
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div 
          style={{
            padding: '12px 16px',
            backgroundColor: '#fef2f2',
            borderBottom: '1px solid #fecaca',
            color: '#dc2626',
            fontSize: '14px'
          }}
        >
          ❌ {error}
        </div>
      )}

      {/* 消息列表 */}
      <div 
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '16px',
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        }}
      >
        {messages.length === 0 ? (
          <div 
            style={{
              textAlign: 'center',
              color: '#666',
              padding: '40px 20px'
            }}
          >
            <p>👋 你好！我是 {title}</p>
            <p>请输入你的问题，我会帮助你生成测试用例。</p>
          </div>
        ) : (
          messages.map((message) => (
            <MessageItem key={message.id} message={message} />
          ))
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <form 
        onSubmit={handleSendMessage}
        style={{
          padding: '16px',
          borderTop: '1px solid #e0e0e0',
          backgroundColor: '#f8f9fa'
        }}
      >
        <div style={{ display: 'flex', gap: '8px' }}>
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={isConnected ? placeholder : "请先连接到Mario服务"}
            disabled={!isConnected || isLoading}
            style={{
              flex: 1,
              padding: '12px',
              border: '1px solid #ccc',
              borderRadius: '6px',
              fontSize: '14px',
              outline: 'none'
            }}
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || !isConnected || isLoading}
            style={{
              padding: '12px 24px',
              backgroundColor: isConnected && inputValue.trim() ? '#007acc' : '#ccc',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: isConnected && inputValue.trim() ? 'pointer' : 'not-allowed',
              fontSize: '14px'
            }}
          >
            {isLoading ? '发送中...' : '发送'}
          </button>
        </div>
      </form>
    </div>
  );
}

/**
 * 消息项组件
 */
interface MessageItemProps {
  message: MarioMessage;
}

function MessageItem({ message }: MessageItemProps) {
  const isUser = message.role === 'user';
  const isWaiting = message.isWaiting;
  
  return (
    <div 
      style={{
        display: 'flex',
        justifyContent: isUser ? 'flex-end' : 'flex-start',
        gap: '8px'
      }}
    >
      {!isUser && (
        <div 
          style={{
            width: '32px',
            height: '32px',
            borderRadius: '50%',
            backgroundColor: '#f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0
          }}
        >
          {message.sender?.avatar || '🤖'}
        </div>
      )}
      
      <div 
        style={{
          maxWidth: '70%',
          padding: '12px 16px',
          borderRadius: '12px',
          backgroundColor: isUser ? '#007acc' : '#f1f3f4',
          color: isUser ? 'white' : '#333',
          fontSize: '14px',
          lineHeight: '1.4',
          opacity: isWaiting ? 0.7 : 1,
          animation: isWaiting ? 'pulse 1.5s infinite' : 'none'
        }}
      >
        {message.content}
        
        {message.sender?.name && !isUser && (
          <div 
            style={{
              fontSize: '12px',
              color: '#666',
              marginTop: '4px'
            }}
          >
            {message.sender.name}
          </div>
        )}
      </div>
      
      {isUser && (
        <div 
          style={{
            width: '32px',
            height: '32px',
            borderRadius: '50%',
            backgroundColor: '#007acc',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0,
            color: 'white',
            fontSize: '16px'
          }}
        >
          👤
        </div>
      )}
    </div>
  );
}
