'use client';

import React, { useEffect, useRef, useState, useCallback, memo } from 'react';

// 动态导入xterm相关模块，确保只在客户端加载
let Terminal: any;
let FitAddon: any;
let SearchAddon: any;
let WebLinksAddon: any;
let WebglAddon: any;

// 检查是否在客户端环境
const isClient = typeof window !== 'undefined';

// 动态加载xterm模块
const loadXtermModules = async () => {
  if (!isClient || Terminal) return;
  
  try {
    const [terminalModule, fitModule, searchModule, webLinksModule, webglModule] = await Promise.all([
      import('@xterm/xterm'),
      import('@xterm/addon-fit'),
      import('@xterm/addon-search'),
      import('@xterm/addon-web-links'),
      import('@xterm/addon-webgl')
    ]);
    
    Terminal = terminalModule.Terminal;
    FitAddon = fitModule.FitAddon;
    SearchAddon = searchModule.SearchAddon;
    WebLinksAddon = webLinksModule.WebLinksAddon;
    WebglAddon = webglModule.WebglAddon;
  } catch (error) {
    console.error('Failed to load xterm modules:', error);
  }
};
import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Separator } from '@workspace/ui/components/separator';
import { Input } from '@workspace/ui/components/input';
import { 
  Terminal as TerminalIcon, 
  Search, 
  Copy, 
  Trash2, 
  Settings,
  Maximize2,
  Minimize2,
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@workspace/ui/lib/utils';
import { toast } from 'sonner';

// 动态导入CSS的函数
const loadXtermCSS = async () => {
  if (isClient) {
    try {
      await import('@xterm/xterm/css/xterm.css');
    } catch (error) {
      console.warn('Failed to load xterm CSS:', error);
    }
  }
};

interface WebTerminalFrameProps {
  port: number;
  containerId?: string;
  title?: string;
  className?: string;
  onFullscreenChange?: (isFullscreen: boolean) => void;
}

interface TerminalState {
  isConnected: boolean;
  isConnecting: boolean;
  hasError: boolean;
  errorMessage: string;
}

interface TerminalSettings {
  fontSize: number;
  fontFamily: string;
  theme: 'dark' | 'light';
  cursorBlink: boolean;
}

const DEFAULT_SETTINGS: TerminalSettings = {
  fontSize: 14,
  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
  theme: 'dark',
  cursorBlink: true
};

const TERMINAL_THEMES = {
  dark: {
    background: '#1e1e1e',
    foreground: '#d4d4d4',
    cursor: '#d4d4d4',
    selection: '#264f78',
    black: '#000000',
    red: '#cd3131',
    green: '#0dbc79',
    yellow: '#e5e510',
    blue: '#2472c8',
    magenta: '#bc3fbc',
    cyan: '#11a8cd',
    white: '#e5e5e5',
    brightBlack: '#666666',
    brightRed: '#f14c4c',
    brightGreen: '#23d18b',
    brightYellow: '#f5f543',
    brightBlue: '#3b8eea',
    brightMagenta: '#d670d6',
    brightCyan: '#29b8db',
    brightWhite: '#e5e5e5'
  },
  light: {
    background: '#ffffff',
    foreground: '#333333',
    cursor: '#333333',
    selection: '#add6ff',
    black: '#000000',
    red: '#cd3131',
    green: '#00bc00',
    yellow: '#949800',
    blue: '#0451a5',
    magenta: '#bc05bc',
    cyan: '#0598bc',
    white: '#555555',
    brightBlack: '#666666',
    brightRed: '#cd3131',
    brightGreen: '#14ce14',
    brightYellow: '#b5ba00',
    brightBlue: '#0451a5',
    brightMagenta: '#bc05bc',
    brightCyan: '#0598bc',
    brightWhite: '#a5a5a5'
  }
};

export const WebTerminalFrame = memo<WebTerminalFrameProps>(({ 
  port, 
  containerId, 
  title = 'Web 终端', 
  className,
  onFullscreenChange
}) => {
  // 状态管理
  const [terminalState, setTerminalState] = useState<TerminalState>({
    isConnected: false,
    isConnecting: false,
    hasError: false,
    errorMessage: ''
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [settings, setSettings] = useState<TerminalSettings>(DEFAULT_SETTINGS);
  
  // Refs
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstanceRef = useRef<any | null>(null);
  const fitAddonRef = useRef<any | null>(null);
  const searchAddonRef = useRef<any | null>(null);
  const websocketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 终端URL
  const terminalUrl = `ws://localhost:${port}/terminal`;
  
  // 初始化终端
  const initializeTerminal = useCallback(async () => {
    if (!terminalRef.current || terminalInstanceRef.current || !isClient) {
      return;
    }
    
    // 确保xterm模块已加载
    await loadXtermModules();
    
    if (!Terminal || !FitAddon || !SearchAddon || !WebLinksAddon) {
      console.error('Failed to load required xterm modules');
      return;
    }
    
    // 创建终端实例
    const terminal = new Terminal({
      fontSize: settings.fontSize,
      fontFamily: settings.fontFamily,
      theme: TERMINAL_THEMES[settings.theme],
      cursorBlink: settings.cursorBlink,
      allowTransparency: true,
      convertEol: true,
      scrollback: 1000,
      tabStopWidth: 4
    });
    
    // 添加插件
    const fitAddon = new FitAddon();
    const searchAddon = new SearchAddon();
    const webLinksAddon = new WebLinksAddon();
    
    terminal.loadAddon(fitAddon);
    terminal.loadAddon(searchAddon);
    terminal.loadAddon(webLinksAddon);
    
    // 尝试加载WebGL插件（可选）
    try {
      const webglAddon = new WebglAddon();
      terminal.loadAddon(webglAddon);
    } catch (error) {
      console.warn('WebGL addon not available:', error);
    }
    
    // 打开终端
    terminal.open(terminalRef.current);
    
    // 适配大小
    fitAddon.fit();
    
    // 保存引用
    terminalInstanceRef.current = terminal;
    fitAddonRef.current = fitAddon;
    searchAddonRef.current = searchAddon;
    
    // 处理用户输入
    terminal.onData((data: string) => {
      if (websocketRef.current?.readyState === WebSocket.OPEN) {
        websocketRef.current.send(JSON.stringify({
          type: 'input',
          data: data
        }));
      }
    });
    
    // 处理终端大小变化
    terminal.onResize(({ cols, rows }: { cols: number; rows: number }) => {
      if (websocketRef.current?.readyState === WebSocket.OPEN) {
        websocketRef.current.send(JSON.stringify({
          type: 'resize',
          cols,
          rows
        }));
      }
    });
    
    // 窗口大小变化时重新适配
    const handleResize = (): void => {
      fitAddon.fit();
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [settings, port]);
  
  // 连接WebSocket
  const connectWebSocket = useCallback(() => {
    if (websocketRef.current?.readyState === WebSocket.OPEN) {
      return;
    }
    
    setTerminalState(prev => ({ ...prev, isConnecting: true, hasError: false }));
    
    try {
      const ws = new WebSocket(terminalUrl);
      
      ws.onopen = () => {
        console.log('Terminal WebSocket connected');
        setTerminalState({
          isConnected: true,
          isConnecting: false,
          hasError: false,
          errorMessage: ''
        });
        
        // 发送初始化信息
        if (terminalInstanceRef.current) {
          const { cols, rows } = terminalInstanceRef.current;
          ws.send(JSON.stringify({
            type: 'init',
            cols,
            rows
          }));
        }
      };
      
      ws.onmessage = (event: MessageEvent) => {
        try {
          const message = JSON.parse(event.data);
          
          if (message.type === 'output' && terminalInstanceRef.current) {
            terminalInstanceRef.current.write(message.data);
          }
        } catch (error: any) {
          // 如果不是JSON，直接写入终端
          if (terminalInstanceRef.current) {
            terminalInstanceRef.current.write(event.data);
          }
        }
      };
      
      ws.onclose = (event: CloseEvent) => {
        console.log('Terminal WebSocket closed:', event.code, event.reason);
        setTerminalState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }));
        
        // 自动重连
        if (event.code !== 1000) { // 非正常关闭
          reconnectTimeoutRef.current = setTimeout(() => {
            connectWebSocket();
          }, 3000);
        }
      };
      
      ws.onerror = (error: Event) => {
        console.error('Terminal WebSocket error:', error);
        setTerminalState({
          isConnected: false,
          isConnecting: false,
          hasError: true,
          errorMessage: '连接失败'
        });
      };
      
      websocketRef.current = ws;
    } catch (error: any) {
      console.error('Failed to create WebSocket:', error);
      setTerminalState({
        isConnected: false,
        isConnecting: false,
        hasError: true,
        errorMessage: '无法创建连接'
      });
    }
  }, [terminalUrl]);
  
  // 断开连接
  const disconnectWebSocket = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (websocketRef.current) {
      websocketRef.current.close(1000, 'User disconnected');
      websocketRef.current = null;
    }
    
    setTerminalState({
      isConnected: false,
      isConnecting: false,
      hasError: false,
      errorMessage: ''
    });
  }, []);
  
  // 重新连接
  const reconnect = useCallback(() => {
    disconnectWebSocket();
    setTimeout(connectWebSocket, 1000);
  }, [disconnectWebSocket, connectWebSocket]);
  
  // 清空终端
  const clearTerminal = useCallback(() => {
    if (terminalInstanceRef.current) {
      terminalInstanceRef.current.clear();
    }
  }, []);
  
  // 复制选中内容
  const copySelection = useCallback(async () => {
    if (terminalInstanceRef.current) {
      const selection = terminalInstanceRef.current.getSelection();
      if (selection) {
        try {
          await navigator.clipboard.writeText(selection);
          toast.success('已复制到剪贴板');
        } catch (error: any) {
          toast.error('复制失败');
        }
      } else {
        toast.info('没有选中内容');
      }
    }
  }, []);
  
  // 搜索
  const handleSearch = useCallback((term: string, direction: 'next' | 'previous' = 'next') => {
    if (searchAddonRef.current && term) {
      if (direction === 'next') {
        searchAddonRef.current.findNext(term);
      } else {
        searchAddonRef.current.findPrevious(term);
      }
    }
  }, []);
  
  // 切换全屏
  const handleToggleFullscreen = useCallback(() => {
    const newFullscreen = !isFullscreen;
    setIsFullscreen(newFullscreen);
    onFullscreenChange?.(newFullscreen);
    
    // 延迟重新适配大小
    setTimeout(() => {
      fitAddonRef.current?.fit();
    }, 100);
  }, [isFullscreen, onFullscreenChange]);
  
  // 更新设置
  const handleSettingsUpdate = useCallback((newSettings: Partial<TerminalSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
    
    // 应用设置到终端
    if (terminalInstanceRef.current) {
      const terminal = terminalInstanceRef.current;
      
      if (newSettings.fontSize) {
        terminal.options.fontSize = newSettings.fontSize;
      }
      if (newSettings.fontFamily) {
        terminal.options.fontFamily = newSettings.fontFamily;
      }
      if (newSettings.theme) {
        terminal.options.theme = TERMINAL_THEMES[newSettings.theme];
      }
      if (newSettings.cursorBlink !== undefined) {
        terminal.options.cursorBlink = newSettings.cursorBlink;
      }
      
      // 刷新终端
      terminal.refresh(0, terminal.rows - 1);
      fitAddonRef.current?.fit();
    }
  }, []);
  
  // 组件挂载时初始化
  useEffect(() => {
    if (!isClient) return;
    
    const init = async () => {
      await initializeTerminal();
      connectWebSocket();
    };
    
    init();
    
    return () => {
      disconnectWebSocket();
      if (terminalInstanceRef.current) {
        terminalInstanceRef.current.dispose();
        terminalInstanceRef.current = null;
      }
    };
  }, [initializeTerminal, connectWebSocket, disconnectWebSocket]);
  
  // 全屏状态变化时重新适配大小
  useEffect(() => {
    if (fitAddonRef.current) {
      setTimeout(() => {
        fitAddonRef.current?.fit();
      }, 100);
    }
  }, [isFullscreen]);
  
  // 连接状态指示器
  const ConnectionStatus = () => {
    if (terminalState.isConnecting) {
      return (
        <Badge variant="outline" className="gap-1">
          <RefreshCw className="h-3 w-3 animate-spin" />
          连接中
        </Badge>
      );
    }
    
    if (terminalState.hasError) {
      return (
        <Badge variant="destructive" className="gap-1">
          <AlertCircle className="h-3 w-3" />
          连接失败
        </Badge>
      );
    }
    
    if (terminalState.isConnected) {
      return (
        <Badge variant="default" className="gap-1">
          <CheckCircle className="h-3 w-3" />
          已连接
        </Badge>
      );
    }
    
    return (
      <Badge variant="secondary" className="gap-1">
        <AlertCircle className="h-3 w-3" />
        未连接
      </Badge>
    );
  };
  
  // 服务器端渲染时显示占位符
  if (!isClient) {
    return (
      <Card className={cn(
        "flex flex-col h-full",
        className
      )}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <TerminalIcon className="h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="flex-1 p-0 relative">
          <div className="flex items-center justify-center h-full bg-muted">
            <div className="text-center space-y-4">
              <TerminalIcon className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-medium">终端加载中...</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  正在初始化终端组件
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className={cn(
      "flex flex-col h-full",
      isFullscreen && "fixed inset-0 z-50 rounded-none",
      className
    )}>
      {/* 头部工具栏 */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="flex items-center gap-2">
              <TerminalIcon className="h-5 w-5" />
              {title}
            </CardTitle>
            
            <ConnectionStatus />
            
            {containerId && (
              <Badge variant="outline" className="text-xs">
                {containerId.slice(0, 12)}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSearch(!showSearch)}
              className="gap-1"
            >
              <Search className="h-4 w-4" />
              搜索
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={copySelection}
              className="gap-1"
            >
              <Copy className="h-4 w-4" />
              复制
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={clearTerminal}
              className="gap-1"
            >
              <Trash2 className="h-4 w-4" />
              清空
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="gap-1"
            >
              <Settings className="h-4 w-4" />
              设置
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleFullscreen}
              className="gap-1"
            >
              {isFullscreen ? (
                <>
                  <Minimize2 className="h-4 w-4" />
                  退出全屏
                </>
              ) : (
                <>
                  <Maximize2 className="h-4 w-4" />
                  全屏
                </>
              )}
            </Button>
            
            {!terminalState.isConnected && (
              <Button
                variant="outline"
                size="sm"
                onClick={reconnect}
                className="gap-1"
              >
                <RefreshCw className="h-4 w-4" />
                重连
              </Button>
            )}
          </div>
        </div>
        
        {/* 搜索栏 */}
        {showSearch && (
          <div className="flex items-center gap-2 mt-2">
            <Input
              placeholder="搜索..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch(searchTerm, e.shiftKey ? 'previous' : 'next');
                }
              }}
              className="flex-1"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSearch(searchTerm, 'previous')}
              disabled={!searchTerm}
            >
              上一个
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSearch(searchTerm, 'next')}
              disabled={!searchTerm}
            >
              下一个
            </Button>
          </div>
        )}
        
        {/* 设置面板 */}
        {showSettings && (
          <Card className="mt-3">
            <CardContent className="p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">字体大小</label>
                  <Input
                    type="number"
                    min={8}
                    max={24}
                    value={settings.fontSize}
                    onChange={(e) => handleSettingsUpdate({ fontSize: parseInt(e.target.value) })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">主题</label>
                  <select
                    value={settings.theme}
                    onChange={(e) => handleSettingsUpdate({ theme: e.target.value as 'dark' | 'light' })}
                    className="w-full p-2 border rounded"
                  >
                    <option value="dark">深色</option>
                    <option value="light">浅色</option>
                  </select>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">字体</label>
                <Input
                  value={settings.fontFamily}
                  onChange={(e) => handleSettingsUpdate({ fontFamily: e.target.value })}
                  placeholder="字体族"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="cursor-blink"
                  checked={settings.cursorBlink}
                  onChange={(e) => handleSettingsUpdate({ cursorBlink: e.target.checked })}
                />
                <label htmlFor="cursor-blink" className="text-sm font-medium">
                  光标闪烁
                </label>
              </div>
            </CardContent>
          </Card>
        )}
      </CardHeader>
      
      <Separator />
      
      {/* 终端区域 */}
      <CardContent className="flex-1 p-0 relative">
        {terminalState.hasError ? (
          <div className="flex items-center justify-center h-full bg-muted">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-medium">终端连接失败</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {terminalState.errorMessage}
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  请确保容器服务正在运行在端口 {port}
                </p>
              </div>
              <Button onClick={reconnect} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-1" />
                重新连接
              </Button>
            </div>
          </div>
        ) : (
          <div
            ref={terminalRef}
            className="w-full h-full p-2"
            style={{
              backgroundColor: TERMINAL_THEMES[settings.theme].background
            }}
          />
        )}
      </CardContent>
    </Card>
  );
});

WebTerminalFrame.displayName = 'WebTerminalFrame';

export default WebTerminalFrame;