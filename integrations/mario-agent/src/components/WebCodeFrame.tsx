import React, { useState, useEffect, useRef, memo, useCallback } from 'react';
import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Separator } from '@workspace/ui/components/separator';
import { Input } from '@workspace/ui/components/input';
import {
  RefreshCw,
  ExternalLink,
  Home,
  ArrowLeft,
  ArrowRight,
  Globe,
  AlertCircle,
  Loader2,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { cn } from '@workspace/ui/lib/utils';
import { toast } from 'sonner';

interface WebCodeFrameProps {
  port: number;
  containerId?: string;
  title?: string;
  className?: string;
  onFullscreenChange?: (isFullscreen: boolean) => void;
}

interface FrameState {
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string;
  currentUrl: string;
  canGoBack: boolean;
  canGoForward: boolean;
}

const DEFAULT_STATE: FrameState = {
  isLoading: true,
  hasError: false,
  errorMessage: '',
  currentUrl: '',
  canGoBack: false,
  canGoForward: false
};

export const WebCodeFrame = memo<WebCodeFrameProps>(
  ({ port, containerId, title = 'Web 预览', className, onFullscreenChange }) => {
    // 状态管理
    const [frameState, setFrameState] = useState<FrameState>(DEFAULT_STATE);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [customUrl, setCustomUrl] = useState('');
    const [isHealthy, setIsHealthy] = useState(false);

    // Refs
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const healthCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

    // 基础URL
    const baseUrl = `http://localhost:${port}`;

    // 健康检查
    const checkHealth = useCallback(async () => {
      try {
        const response = await fetch(`${baseUrl}/health`, {
          method: 'GET',
          mode: 'no-cors',
          cache: 'no-cache'
        });

        // 由于 no-cors 模式，我们无法检查响应状态
        // 如果没有抛出错误，就认为服务是健康的
        setIsHealthy(true);
        setFrameState((prev) => ({
          ...prev,
          hasError: false,
          errorMessage: ''
        }));
      } catch (error) {
        setIsHealthy(false);
        setFrameState((prev) => ({
          ...prev,
          hasError: true,
          errorMessage: '服务不可用'
        }));
      }
    }, [baseUrl]);

    // 开始健康检查
    useEffect(() => {
      checkHealth();

      healthCheckIntervalRef.current = setInterval(checkHealth, 5000);

      return () => {
        if (healthCheckIntervalRef.current) {
          clearInterval(healthCheckIntervalRef.current);
        }
      };
    }, [checkHealth]);

    // 处理iframe加载
    const handleIframeLoad = useCallback(() => {
      setFrameState((prev) => ({ ...prev, isLoading: false }));

      try {
        const iframe = iframeRef.current;
        if (iframe && iframe.contentWindow) {
          const currentUrl = iframe.contentWindow.location.href;
          setFrameState((prev) => ({
            ...prev,
            currentUrl,
            canGoBack: (iframe.contentWindow?.history?.length || 0) > 1,
            canGoForward: false // 无法直接检测
          }));
        }
      } catch (error: any) {
        // 跨域限制，无法访问iframe内容
        console.warn('Cannot access iframe content due to CORS:', error);
      }
    }, []);

    // 处理iframe错误
    const handleIframeError = useCallback(() => {
      setFrameState((prev) => ({
        ...prev,
        isLoading: false,
        hasError: true,
        errorMessage: '页面加载失败'
      }));
    }, []);

    // 刷新页面
    const handleRefresh = useCallback(() => {
      if (iframeRef.current) {
        setFrameState((prev) => ({ ...prev, isLoading: true, hasError: false }));
        iframeRef.current.src = iframeRef.current.src;
      }
    }, []);

    // 导航到首页
    const handleGoHome = useCallback(() => {
      if (iframeRef.current) {
        setFrameState((prev) => ({ ...prev, isLoading: true, hasError: false }));
        iframeRef.current.src = baseUrl;
        setCustomUrl('');
      }
    }, [baseUrl]);

    // 后退
    const handleGoBack = useCallback(() => {
      try {
        iframeRef.current?.contentWindow?.history.back();
      } catch (error: any) {
        toast.error('无法后退：跨域限制');
      }
    }, []);

    // 前进
    const handleGoForward = useCallback(() => {
      try {
        iframeRef.current?.contentWindow?.history.forward();
      } catch (error: any) {
        toast.error('无法前进：跨域限制');
      }
    }, []);

    // 在新窗口打开
    const handleOpenExternal = useCallback(() => {
      const url = customUrl || baseUrl;
      window.open(url, '_blank', 'noopener,noreferrer');
    }, [customUrl, baseUrl]);

    // 导航到自定义URL
    const handleNavigateToCustomUrl = useCallback(
      (e?: React.FormEvent) => {
        if (e) {
          e.preventDefault();
        }
        if (customUrl && iframeRef.current) {
          setFrameState((prev) => ({ ...prev, isLoading: true, hasError: false }));

          // 如果是相对路径，添加基础URL
          const fullUrl = customUrl.startsWith('http')
            ? customUrl
            : `${baseUrl}${customUrl.startsWith('/') ? '' : '/'}${customUrl}`;

          iframeRef.current.src = fullUrl;
        }
      },
      [customUrl, baseUrl]
    );

    // 处理自定义URL输入
    const handleCustomUrlKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
          handleNavigateToCustomUrl();
        }
      },
      [handleNavigateToCustomUrl]
    );

    // 切换全屏
    const handleToggleFullscreen = useCallback(() => {
      const newFullscreen = !isFullscreen;
      setIsFullscreen(newFullscreen);
      onFullscreenChange?.(newFullscreen);
    }, [isFullscreen, onFullscreenChange]);

    // 当前显示的URL
    const displayUrl = customUrl || baseUrl;

    return (
      <Card className={cn('flex h-full flex-col', isFullscreen && 'fixed inset-0 z-50 rounded-none', className)}>
        {/* 头部工具栏 */}
        <CardHeader className='pb-3'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <CardTitle className='flex items-center gap-2'>
                <Globe className='h-5 w-5' />
                {title}
              </CardTitle>

              <Badge variant={isHealthy ? 'default' : 'destructive'} className='gap-1'>
                {isHealthy ? (
                  <>
                    <div className='h-2 w-2 rounded-full bg-green-500' />
                    在线
                  </>
                ) : (
                  <>
                    <AlertCircle className='h-3 w-3' />
                    离线
                  </>
                )}
              </Badge>

              {containerId && (
                <Badge variant='outline' className='text-xs'>
                  {containerId.slice(0, 12)}
                </Badge>
              )}
            </div>

            <div className='flex items-center gap-2'>
              <Button variant='outline' size='sm' onClick={handleToggleFullscreen} className='gap-1'>
                {isFullscreen ? (
                  <>
                    <Minimize2 className='h-4 w-4' />
                    退出全屏
                  </>
                ) : (
                  <>
                    <Maximize2 className='h-4 w-4' />
                    全屏
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* 导航栏 */}
          <div className='flex items-center gap-2'>
            <div className='flex items-center gap-1'>
              <Button
                variant='outline'
                size='sm'
                onClick={handleGoBack}
                disabled={!frameState.canGoBack}
                className='p-2'>
                <ArrowLeft className='h-4 w-4' />
              </Button>

              <Button
                variant='outline'
                size='sm'
                onClick={handleGoForward}
                disabled={!frameState.canGoForward}
                className='p-2'>
                <ArrowRight className='h-4 w-4' />
              </Button>

              <Button
                variant='outline'
                size='sm'
                onClick={handleRefresh}
                disabled={frameState.isLoading}
                className='p-2'>
                {frameState.isLoading ? (
                  <Loader2 className='h-4 w-4 animate-spin' />
                ) : (
                  <RefreshCw className='h-4 w-4' />
                )}
              </Button>

              <Button variant='outline' size='sm' onClick={handleGoHome} className='p-2'>
                <Home className='h-4 w-4' />
              </Button>
            </div>

            <div className='flex flex-1 items-center gap-2'>
              <Input
                placeholder={`路径或完整URL (默认: ${baseUrl})`}
                value={customUrl}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCustomUrl(e.target.value)}
                onKeyDown={handleCustomUrlKeyDown}
                className='text-sm'
              />

              <Button variant='outline' size='sm' onClick={handleNavigateToCustomUrl} disabled={!customUrl.trim()}>
                前往
              </Button>
            </div>

            <Button variant='outline' size='sm' onClick={handleOpenExternal} className='gap-1'>
              <ExternalLink className='h-4 w-4' />
              新窗口
            </Button>
          </div>
        </CardHeader>

        <Separator />

        {/* 内容区域 */}
        <CardContent className='relative flex-1 p-0'>
          {frameState.hasError ? (
            <div className='bg-muted flex h-full items-center justify-center'>
              <div className='space-y-4 text-center'>
                <AlertCircle className='text-muted-foreground mx-auto h-12 w-12' />
                <div>
                  <h3 className='text-lg font-medium'>页面加载失败</h3>
                  <p className='text-muted-foreground mt-1 text-sm'>{frameState.errorMessage}</p>
                  <p className='text-muted-foreground mt-2 text-xs'>请确保容器服务正在运行在端口 {port}</p>
                </div>
                <div className='flex justify-center gap-2'>
                  <Button onClick={handleRefresh} variant='outline' size='sm'>
                    <RefreshCw className='mr-1 h-4 w-4' />
                    重试
                  </Button>
                  <Button onClick={checkHealth} variant='outline' size='sm'>
                    检查服务
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <>
              {frameState.isLoading && (
                <div className='bg-background/80 absolute inset-0 z-10 flex items-center justify-center backdrop-blur-sm'>
                  <div className='flex items-center gap-2'>
                    <Loader2 className='h-5 w-5 animate-spin' />
                    <span className='text-sm'>加载中...</span>
                  </div>
                </div>
              )}

              <iframe
                ref={iframeRef}
                src={displayUrl}
                className='h-full w-full border-0'
                title={title}
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                sandbox='allow-same-origin allow-scripts allow-forms allow-popups allow-modals'
              />
            </>
          )}
        </CardContent>
      </Card>
    );
  }
);

WebCodeFrame.displayName = 'WebCodeFrame';

export default WebCodeFrame;
