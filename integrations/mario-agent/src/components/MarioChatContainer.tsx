import React, { useState, useCallback, useRef, memo } from 'react';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Input } from '@workspace/ui/components/input';
import { Label } from '@workspace/ui/components/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select';
import { Textarea } from '@workspace/ui/components/textarea';
import { Badge } from '@workspace/ui/components/badge';
import { Separator } from '@workspace/ui/components/separator';
import { Switch } from '@workspace/ui/components/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs';
import { 
  Send, 
  Square, 
  Trash2, 
  Settings, 
  Play, 
  Container, 
  Globe, 
  Terminal,
  Code,
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@workspace/ui/lib/utils';
import { useMarioWebSocket, MarioInput } from '../hooks/useMarioWebSocket.js';
import { MarioMessageList } from './MarioMessageList.js';
import { toast } from 'sonner';

interface MarioChatContainerProps {
  onContainerCreated?: (containerId: string, port: number) => void;
  onCanvasOpen?: (port: number) => void;
  onTerminalOpen?: (port: number) => void;
  className?: string;
}

interface ChatSettings {
  model: string;
  mcp: string[];
  autoCreateContainer: boolean;
  defaultPort: number;
}

const DEFAULT_SETTINGS: ChatSettings = {
  model: 'gpt-4',
  mcp: [],
  autoCreateContainer: true,
  defaultPort: 3000
};

const AVAILABLE_MODELS = [
  { value: 'gpt-4', label: 'GPT-4' },
  { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
  { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
  { value: 'claude-3-haiku', label: 'Claude 3 Haiku' }
];

const AVAILABLE_MCP = [
  { value: 'filesystem', label: '文件系统' },
  { value: 'web-search', label: '网络搜索' },
  { value: 'code-execution', label: '代码执行' },
  { value: 'database', label: '数据库' }
];

export const MarioChatContainer = memo<MarioChatContainerProps>(({ 
  onContainerCreated, 
  onCanvasOpen, 
  onTerminalOpen, 
  className 
}) => {
  // 状态管理
  const [input, setInput] = useState('');
  const [marioCase, setMarioCase] = useState('');
  const [settings, setSettings] = useState<ChatSettings>(DEFAULT_SETTINGS);
  const [showSettings, setShowSettings] = useState(false);
  const [currentPort, setCurrentPort] = useState<number | null>(null);
  const [currentContainerId, setCurrentContainerId] = useState<string | null>(null);
  
  // Refs
  const inputRef = useRef<HTMLTextAreaElement>(null);
  
  // WebSocket Hook
  const {
    isConnected,
    isConnecting,
    connectionError,
    isLoading,
    loadingMessage,
    messages,
    currentStreamingMessage,
    sendMessage,
    stopCurrentOperation,
    clearMessages,
    reconnect
  } = useMarioWebSocket({
    onContainerCreated: (containerId, port) => {
      setCurrentContainerId(containerId);
      setCurrentPort(port);
      onContainerCreated?.(containerId, port);
      toast.success(`容器已创建，端口: ${port}`);
    },
    onError: (error) => {
      toast.error(`Mario 错误: ${error}`);
    }
  });
  
  // 处理消息发送
  const handleSendMessage = useCallback(async () => {
    if (!input.trim() || isLoading) return;
    
    const marioInput: MarioInput = {
      message: input.trim(),
      port: currentPort || settings.defaultPort,
      model: settings.model,
      mcpServers: settings.mcp
    };
    
    await sendMessage(marioInput);
    setInput('');
    setMarioCase('');
    
    // 聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  }, [input, marioCase, currentPort, settings, isLoading, sendMessage]);
  
  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);
  
  // 处理停止操作
  const handleStop = useCallback(() => {
    stopCurrentOperation();
    toast.info('操作已停止');
  }, [stopCurrentOperation]);
  
  // 处理清空消息
  const handleClearMessages = useCallback(() => {
    clearMessages();
    toast.info('消息已清空');
  }, [clearMessages]);
  
  // 处理设置更新
  const handleSettingsUpdate = useCallback((newSettings: Partial<ChatSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);
  
  // 处理MCP选择
  const handleMcpToggle = useCallback((mcpValue: string) => {
    setSettings(prev => ({
      ...prev,
      mcp: prev.mcp.includes(mcpValue)
        ? prev.mcp.filter(m => m !== mcpValue)
        : [...prev.mcp, mcpValue]
    }));
  }, []);
  
  // 处理消息操作
  const handleCopyMessage = useCallback((content: string) => {
    toast.success('消息已复制到剪贴板');
  }, []);
  
  const handleEditMessage = useCallback((messageId: string, content: string) => {
    setInput(content);
    inputRef.current?.focus();
  }, []);
  
  const handleDeleteMessage = useCallback((messageId: string) => {
    // TODO: 实现消息删除逻辑
    toast.info('消息删除功能待实现');
  }, []);
  
  // 连接状态指示器
  const ConnectionStatus = () => {
    if (isConnecting) {
      return (
        <Badge variant="outline" className="gap-1">
          <RefreshCw className="h-3 w-3 animate-spin" />
          连接中
        </Badge>
      );
    }
    
    if (connectionError) {
      return (
        <Badge variant="destructive" className="gap-1">
          <AlertCircle className="h-3 w-3" />
          连接失败
        </Badge>
      );
    }
    
    if (isConnected) {
      return (
        <Badge variant="default" className="gap-1">
          <CheckCircle className="h-3 w-3" />
          已连接
        </Badge>
      );
    }
    
    return (
      <Badge variant="secondary" className="gap-1">
        <AlertCircle className="h-3 w-3" />
        未连接
      </Badge>
    );
  };
  
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* 头部 */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="flex items-center gap-2">
              🎮 Mario Chat
            </CardTitle>
            <ConnectionStatus />
            {currentPort && (
              <Badge variant="outline" className="gap-1">
                <Container className="h-3 w-3" />
                端口: {currentPort}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {currentPort && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onCanvasOpen?.(currentPort)}
                  className="gap-1"
                >
                  <Globe className="h-4 w-4" />
                  画布
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onTerminalOpen?.(currentPort)}
                  className="gap-1"
                >
                  <Terminal className="h-4 w-4" />
                  终端
                </Button>
              </>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="gap-1"
            >
              <Settings className="h-4 w-4" />
              设置
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearMessages}
              className="gap-1"
            >
              <Trash2 className="h-4 w-4" />
              清空
            </Button>
            
            {!isConnected && (
              <Button
                variant="outline"
                size="sm"
                onClick={reconnect}
                className="gap-1"
              >
                <RefreshCw className="h-4 w-4" />
                重连
              </Button>
            )}
          </div>
        </div>
        
        {/* 设置面板 */}
        {showSettings && (
          <Card className="mt-3">
            <CardContent className="p-4">
              <Tabs defaultValue="model" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="model">模型</TabsTrigger>
                  <TabsTrigger value="mcp">MCP</TabsTrigger>
                  <TabsTrigger value="container">容器</TabsTrigger>
                </TabsList>
                
                <TabsContent value="model" className="space-y-4">
                  <div className="space-y-2">
                    <Label>AI 模型</Label>
                    <Select
                      value={settings.model}
                      onValueChange={(value: string) => handleSettingsUpdate({ model: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {AVAILABLE_MODELS.map((model) => (
                          <SelectItem key={model.value} value={model.value}>
                            {model.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>
                
                <TabsContent value="mcp" className="space-y-4">
                  <div className="space-y-2">
                    <Label>MCP 服务</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {AVAILABLE_MCP.map((mcp) => (
                        <div key={mcp.value} className="flex items-center space-x-2">
                          <Switch
                            id={mcp.value}
                            checked={settings.mcp.includes(mcp.value)}
                            onCheckedChange={() => handleMcpToggle(mcp.value)}
                          />
                          <Label htmlFor={mcp.value} className="text-sm">
                            {mcp.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="container" className="space-y-4">
                  <div className="space-y-2">
                    <Label>默认端口</Label>
                    <Input
                      type="number"
                      value={settings.defaultPort}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSettingsUpdate({ defaultPort: parseInt(e.target.value) || 3000 })}
                      min={1000}
                      max={65535}
                    />
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="auto-container"
                      checked={settings.autoCreateContainer}
                      onCheckedChange={(checked: boolean) => handleSettingsUpdate({ autoCreateContainer: checked })}
                    />
                    <Label htmlFor="auto-container" className="text-sm">
                      自动创建容器
                    </Label>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        )}
      </CardHeader>
      
      <Separator />
      
      {/* 消息列表 */}
      <CardContent className="flex-1 p-0">
        <MarioMessageList
          messages={messages}
          currentStreamingMessage={currentStreamingMessage}
          isLoading={isLoading}
          loadingMessage={loadingMessage}
          onCopyMessage={handleCopyMessage}
          onEditMessage={handleEditMessage}
          onDeleteMessage={handleDeleteMessage}
          className="h-full"
        />
      </CardContent>
      
      <Separator />
      
      {/* 输入区域 */}
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Mario Case 输入 */}
          <div className="space-y-2">
            <Label htmlFor="mario-case" className="text-sm font-medium">
              用例描述 (可选)
            </Label>
            <Input
              id="mario-case"
              placeholder="描述您想要实现的功能或用例..."
              value={marioCase}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setMarioCase(e.target.value)}
              disabled={isLoading}
            />
          </div>
          
          {/* 消息输入 */}
          <div className="space-y-2">
            <Label htmlFor="message-input" className="text-sm font-medium">
              消息
            </Label>
            <div className="flex gap-2">
              <Textarea
                ref={inputRef}
                id="message-input"
                placeholder="输入您的消息... (Shift+Enter 换行，Enter 发送)"
                value={input}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isLoading || !isConnected}
                className="min-h-[60px] max-h-[200px] resize-none"
                rows={2}
              />
              
              <div className="flex flex-col gap-2">
                {isLoading ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleStop}
                    className="gap-1 h-[60px] w-[80px]"
                  >
                    <Square className="h-4 w-4" />
                    停止
                  </Button>
                ) : (
                  <Button
                    onClick={handleSendMessage}
                    disabled={!input.trim() || !isConnected}
                    className="gap-1 h-[60px] w-[80px]"
                  >
                    <Send className="h-4 w-4" />
                    发送
                  </Button>
                )}
              </div>
            </div>
          </div>
          
          {/* 状态信息 */}
          {connectionError && (
            <div className="text-sm text-red-500 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              连接错误: {connectionError}
            </div>
          )}
        </div>
      </CardContent>
    </div>
  );
});

MarioChatContainer.displayName = 'MarioChatContainer';

export default MarioChatContainer;