import React, { memo, useEffect, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Card, CardContent } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Separator } from '@workspace/ui/components/separator';
import { Copy, Edit, Trash2, AlertCircle, CheckCircle, XCircle, Clock } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@workspace/ui/lib/utils';
import { MarioMessage } from '../hooks/useMarioWebSocket.js';

interface MarioMessageListProps {
  messages: MarioMessage[];
  currentStreamingMessage?: string;
  isLoading?: boolean;
  loadingMessage?: string;
  onCopyMessage?: (content: string) => void;
  onEditMessage?: (messageId: string, content: string) => void;
  onDeleteMessage?: (messageId: string) => void;
  className?: string;
}

interface MessageBubbleProps {
  message: MarioMessage;
  isStreaming?: boolean;
  onCopy?: (content: string) => void;
  onEdit?: (messageId: string, content: string) => void;
  onDelete?: (messageId: string) => void;
}

const MessageBubble = memo<MessageBubbleProps>(({ message, isStreaming = false, onCopy, onEdit, onDelete }) => {
  const isUser = message.role === 'user';
  const isSystem = message.type === 'system';
  const isError = message.type === 'error';
  const isInterrupt = message.type === 'interrupt';

  const getMessageIcon = () => {
    if (isError) return <XCircle className='h-4 w-4 text-red-500' />;
    if (isSystem) return <CheckCircle className='h-4 w-4 text-green-500' />;
    if (isInterrupt) return <AlertCircle className='h-4 w-4 text-yellow-500' />;
    return null;
  };

  const getMessageBadge = () => {
    if (isError) return <Badge variant='destructive'>错误</Badge>;
    if (isSystem) return <Badge variant='secondary'>系统</Badge>;
    if (isInterrupt) return <Badge variant='outline'>中断</Badge>;
    if (message.metadata?.containerId) {
      return <Badge variant='default'>容器: {message.metadata.port}</Badge>;
    }
    return null;
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(timestamp);
  };

  return (
    <div className={cn('flex gap-3 p-4', isUser ? 'justify-end' : 'justify-start')}>
      {!isUser && (
        <Avatar className='h-8 w-8 shrink-0'>
          <AvatarImage src='/mario-avatar.png' alt='Mario' />
          <AvatarFallback className='bg-blue-500 text-xs text-white'>M</AvatarFallback>
        </Avatar>
      )}

      <div className={cn('flex max-w-[80%] flex-col gap-2', isUser ? 'items-end' : 'items-start')}>
        {/* 消息头部 */}
        <div
          className={cn(
            'text-muted-foreground flex items-center gap-2 text-xs',
            isUser ? 'flex-row-reverse' : 'flex-row'
          )}>
          <span className='font-medium'>{isUser ? '您' : 'Mario'}</span>
          <span>{formatTimestamp(message.timestamp)}</span>
          {getMessageIcon()}
          {getMessageBadge()}
        </div>

        {/* 消息内容 */}
        <Card
          className={cn(
            'relative',
            isUser
              ? 'bg-primary text-primary-foreground'
              : isError
                ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'
                : isSystem
                  ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950'
                  : 'bg-muted',
            isStreaming && 'animate-pulse'
          )}>
          <CardContent className='p-3'>
            {isUser ? (
              <p className='whitespace-pre-wrap text-sm'>{message.content}</p>
            ) : (
              <div className='prose prose-sm dark:prose-invert max-w-none'>
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    code({
                      node,
                      inline,
                      className,
                      children,
                      ...props
                    }: {
                      node?: any;
                      inline?: boolean;
                      className?: string;
                      children?: React.ReactNode;
                      [key: string]: any;
                    }) {
                      const match = /language-(\w+)/.exec(className || '');
                      return !inline && match ? (
                        <SyntaxHighlighter
                          style={oneDark}
                          language={match[1]}
                          PreTag='div'
                          className='rounded-md'
                          {...props}>
                          {String(children).replace(/\n$/, '')}
                        </SyntaxHighlighter>
                      ) : (
                        <code className={className} {...props}>
                          {children}
                        </code>
                      );
                    }
                  }}>
                  {message.content}
                </ReactMarkdown>
              </div>
            )}

            {/* 特殊提示 */}
            {isInterrupt && (
              <div className='mt-2 rounded bg-yellow-100 p-2 text-xs dark:bg-yellow-900'>
                <AlertCircle className='mr-1 inline h-3 w-3' />
                操作已被中断
              </div>
            )}
          </CardContent>

          {/* 操作按钮 */}
          <div
            className={cn(
              'absolute top-2 opacity-0 transition-opacity group-hover:opacity-100',
              isUser ? 'left-2' : 'right-2'
            )}>
            <div className='flex gap-1'>
              <Button variant='ghost' size='sm' className='h-6 w-6 p-0' onClick={() => onCopy?.(message.content)}>
                <Copy className='h-3 w-3' />
              </Button>
              {isUser && (
                <>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-6 w-6 p-0'
                    onClick={() => onEdit?.(message.id, message.content)}>
                    <Edit className='h-3 w-3' />
                  </Button>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-6 w-6 p-0 text-red-500 hover:text-red-700'
                    onClick={() => onDelete?.(message.id)}>
                    <Trash2 className='h-3 w-3' />
                  </Button>
                </>
              )}
            </div>
          </div>
        </Card>
      </div>

      {isUser && (
        <Avatar className='h-8 w-8 shrink-0'>
          <AvatarImage src='/user-avatar.png' alt='User' />
          <AvatarFallback className='bg-primary text-primary-foreground text-xs'>U</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
});

MessageBubble.displayName = 'MessageBubble';

const LoadingMessage = memo<{ message: string }>(({ message }) => {
  return (
    <div className='flex justify-start gap-3 p-4'>
      <Avatar className='h-8 w-8 shrink-0'>
        <AvatarImage src='/mario-avatar.png' alt='Mario' />
        <AvatarFallback className='bg-blue-500 text-xs text-white'>M</AvatarFallback>
      </Avatar>

      <div className='flex max-w-[80%] flex-col items-start gap-2'>
        <div className='text-muted-foreground flex items-center gap-2 text-xs'>
          <span className='font-medium'>Mario</span>
          <Clock className='h-3 w-3 animate-spin' />
        </div>

        <Card className='bg-muted'>
          <CardContent className='p-3'>
            <div className='flex items-center gap-2'>
              <div className='flex gap-1'>
                <div className='bg-primary h-2 w-2 animate-bounce rounded-full' />
                <div className='bg-primary h-2 w-2 animate-bounce rounded-full' style={{ animationDelay: '0.1s' }} />
                <div className='bg-primary h-2 w-2 animate-bounce rounded-full' style={{ animationDelay: '0.2s' }} />
              </div>
              <span className='text-muted-foreground text-sm'>{message}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
});

LoadingMessage.displayName = 'LoadingMessage';

const StreamingMessage = memo<{ content: string }>(({ content }) => {
  return (
    <div className='flex justify-start gap-3 p-4'>
      <Avatar className='h-8 w-8 shrink-0'>
        <AvatarImage src='/mario-avatar.png' alt='Mario' />
        <AvatarFallback className='bg-blue-500 text-xs text-white'>M</AvatarFallback>
      </Avatar>

      <div className='flex max-w-[80%] flex-col items-start gap-2'>
        <div className='text-muted-foreground flex items-center gap-2 text-xs'>
          <span className='font-medium'>Mario</span>
          <div className='h-2 w-2 animate-pulse rounded-full bg-green-500' />
        </div>

        <Card className='bg-muted'>
          <CardContent className='p-3'>
            <div className='prose prose-sm dark:prose-invert max-w-none'>
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{content}</ReactMarkdown>
              <span className='bg-primary ml-1 inline-block h-4 w-2 animate-pulse' />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
});

StreamingMessage.displayName = 'StreamingMessage';

export const MarioMessageList = memo<MarioMessageListProps>(
  ({
    messages,
    currentStreamingMessage,
    isLoading,
    loadingMessage = '正在处理...',
    onCopyMessage,
    onEditMessage,
    onDeleteMessage,
    className
  }) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);

    // 自动滚动到底部
    useEffect(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages, currentStreamingMessage, isLoading]);

    const handleCopy = async (content: string) => {
      try {
        await navigator.clipboard.writeText(content);
        onCopyMessage?.(content);
      } catch (error: any) {
        console.error('Failed to copy message:', error);
      }
    };

    return (
      <div className={cn('flex h-full flex-col', className)}>
        <div className='flex-1 overflow-y-auto'>
          {messages.length === 0 && !isLoading && !currentStreamingMessage && (
            <div className='text-muted-foreground flex h-full items-center justify-center'>
              <div className='text-center'>
                <div className='mb-2 text-4xl'>🎮</div>
                <p className='text-lg font-medium'>欢迎使用 Mario</p>
                <p className='text-sm'>开始对话来体验智能编程助手</p>
              </div>
            </div>
          )}

          <div className='group'>
            {messages.map((message, index) => (
              <div key={message.id}>
                <MessageBubble
                  message={message}
                  onCopy={handleCopy}
                  onEdit={onEditMessage}
                  onDelete={onDeleteMessage}
                />
                {index < messages.length - 1 && <Separator className='mx-4' />}
              </div>
            ))}

            {currentStreamingMessage && (
              <>
                {messages.length > 0 && <Separator className='mx-4' />}
                <StreamingMessage content={currentStreamingMessage} />
              </>
            )}

            {isLoading && !currentStreamingMessage && (
              <>
                {messages.length > 0 && <Separator className='mx-4' />}
                <LoadingMessage message={loadingMessage} />
              </>
            )}
          </div>

          <div ref={messagesEndRef} />
        </div>
      </div>
    );
  }
);

MarioMessageList.displayName = 'MarioMessageList';

export default MarioMessageList;
