# Mario Agent 错误处理改进

## 概述

本次修改主要解决了 `intercept-console-error.js:50` 错误和 `extract() failed: No function call occurred` 问题，通过添加严格的输入验证、统一的错误处理和更好的用户反馈来提高系统的稳定性。

## 主要修改

### 1. 新增错误处理工具 (`src/utils/error-handler.ts`)

创建了统一的错误处理工具，包含：

- **错误类型定义**：`connection`、`validation`、`ai_processing`、`unknown`
- **输入验证函数**：`validateMessageInput()` - 验证消息不为空、长度合理
- **连接验证函数**：`validateWebSocketConnection()` - 验证 WebSocket 连接状态
- **AI 错误处理**：`handleAIProcessingError()` - 专门处理 AI SDK 相关错误
- **用户友好消息**：`getUserFriendlyErrorMessage()` - 转换技术错误为用户可理解的消息
- **错误日志**：`logError()` - 统一的错误记录机制

### 2. 改进 WebSocket 钩子 (`src/hooks/useAGUIWebSocket.ts`)

**主要改进：**

```typescript
// 添加导入
import {
  validateMessageInput,
  validateWebSocketConnection,
  handleAIProcessingError,
  logError,
  getUserFriendlyErrorMessage,
} from '../utils/error-handler';

// 改进的 sendMessage 函数
const sendMessage = useCallback(async (input: MarioInput) => {
  // 1. 验证消息输入
  const messageValidation = validateMessageInput(input.message);
  if (!messageValidation.isValid && messageValidation.error) {
    const errorMessage = getUserFriendlyErrorMessage(messageValidation.error);
    logError(messageValidation.error, 'sendMessage');
    onError?.(errorMessage);
    return;
  }

  // 2. 验证 WebSocket 连接状态
  const connectionValidation = validateWebSocketConnection(wsRef.current);
  if (!connectionValidation.isValid && connectionValidation.error) {
    const errorMessage = getUserFriendlyErrorMessage(connectionValidation.error);
    logError(connectionValidation.error, 'sendMessage');
    onError?.(errorMessage);
    return;
  }

  // 3. 改进的错误处理
  try {
    // ... 发送逻辑
  } catch (error) {
    const aguiError = handleAIProcessingError(error);
    const errorMessage = getUserFriendlyErrorMessage(aguiError);
    logError(aguiError, 'sendMessage');
    onError?.(errorMessage);
  }
}, [onError]);
```

### 3. 改进工作区组件 (`src/workspace.tsx`)

**主要改进：**

```typescript
// 添加导入
import { validateMessageInput, getUserFriendlyErrorMessage } from './utils/error-handler';

// 改进的 handleSendMessage 函数
const handleSendMessage = async () => {
  // 使用统一的消息验证
  const validation = validateMessageInput(inputMessage);
  if (!validation.isValid && validation.error) {
    const errorMessage = getUserFriendlyErrorMessage(validation.error);
    console.warn(errorMessage);
    return;
  }
  
  // ... 其他逻辑
};
```

### 4. 改进主题提取工具 (`integrations/haiku-agent/src/tools/topic-tool.ts`)

**主要改进：**

```typescript
async execute(input: string): Promise<string> {
  // 验证输入不为空
  if (!input || input.trim().length === 0) {
    console.warn('TopicExtractorTool: Empty input provided, returning default topic');
    return 'general';
  }

  try {
    const result = await generateText({
      model: openai('gpt-4o-2024-11-20'),
      prompt: `Extract the main topic from this user input. Return only the topic, nothing else.\n\nUser input: ${input.trim()}`,
      maxTokens: 50,
    });

    // 验证 AI 返回结果
    const extractedTopic = result.text?.trim();
    if (!extractedTopic) {
      console.warn('TopicExtractorTool: AI returned empty result, using fallback');
      return input.trim();
    }

    return extractedTopic;
  } catch (error) {
    console.error('TopicExtractorTool: AI topic extraction failed, using fallback:', error);
    return input.trim() || 'general';
  }
}
```

## 解决的问题

### 1. `extract() failed: No function call occurred`

**原因：** AI SDK 的 `extract` 函数在处理空消息或格式不正确的输入时失败。

**解决方案：**
- 添加严格的输入验证，确保消息不为空
- 在 AI 处理前清理输入内容
- 添加专门的 AI 错误处理逻辑

### 2. `Judge node failed with error: No generations found in stream`

**原因：** 后端判断节点接收到空消息或无效请求。

**解决方案：**
- 验证 AG-UI 请求的 `content` 字段不为空
- 确保 WebSocket 连接状态正常
- 添加请求构建验证

### 3. 前端错误处理不统一

**原因：** 各个组件使用不同的错误处理方式。

**解决方案：**
- 创建统一的错误处理工具
- 标准化错误消息格式
- 提供用户友好的错误反馈

## 使用方法

### 1. 错误验证

```typescript
import { validateMessageInput } from './utils/error-handler';

const validation = validateMessageInput(userInput);
if (!validation.isValid) {
  console.error(validation.error?.message);
  return;
}
```

### 2. WebSocket 连接验证

```typescript
import { validateWebSocketConnection } from './utils/error-handler';

const connectionValidation = validateWebSocketConnection(websocket);
if (!connectionValidation.isValid) {
  console.error(connectionValidation.error?.message);
  return;
}
```

### 3. AI 错误处理

```typescript
import { handleAIProcessingError, getUserFriendlyErrorMessage } from './utils/error-handler';

try {
  // AI 处理逻辑
} catch (error) {
  const aguiError = handleAIProcessingError(error);
  const userMessage = getUserFriendlyErrorMessage(aguiError);
  showErrorToUser(userMessage);
}
```

## 测试建议

1. **空消息测试**：尝试发送空消息或只包含空格的消息
2. **连接状态测试**：在 WebSocket 断开时尝试发送消息
3. **长消息测试**：发送超过 10000 字符的消息
4. **AI 错误测试**：模拟 AI 处理失败的情况

## 监控和日志

- 所有错误都会通过 `logError()` 函数记录
- 开发环境：错误详情输出到控制台
- 生产环境：可扩展到监控服务（如 Sentry）

## 版本兼容性

- 确保 AI SDK 版本一致性（当前使用 `ai@^4.3.16`）
- 兼容现有的 AG-UI 协议
- 向后兼容现有的错误处理逻辑