'use client';

import React, { useState, useEffect } from 'react';
import { useAGUIWebSocket } from '../src/hooks/useAGUIWebSocket.js';
import type { AGUIMessage } from '../src/types/mario.js';

/**
 * AG-UI WebSocket 使用示例
 * 展示如何使用新的AG-UI协议与Mario服务通信
 */
export function AGUIExample() {
  const [inputMessage, setInputMessage] = useState('');
  const [marioCase, setMarioCase] = useState('web_development');
  const [port, setPort] = useState(3000);

  // 使用AG-UI WebSocket钩子
  const {
    isConnected,
    isConnecting,
    connectionError,
    isLoading,
    loadingMessage,
    messages,
    currentStreamingMessage,
    sendMessage,
    stopCurrentOperation,
    clearMessages,
    reconnect
  } = useAGUIWebSocket({
    serverUrl: 'ws://localhost:8080/ws/ag-ui',
    onContainerCreated: (containerId, port) => {
      console.log(`Container created: ${containerId} on port ${port}`);
    },
    onError: (error) => {
      console.error('AG-UI WebSocket error:', error);
    }
  });

  // 发送消息处理
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !isConnected) return;

    const marioInput = {
      message: inputMessage,
      mario_case: marioCase,
      port: port,
      model: 'gpt-4',
      mcpServers: []
    };

    try {
      await sendMessage(marioInput);
      setInputMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  // 连接状态指示器
  const getConnectionStatus = () => {
    if (isConnecting) return { text: '连接中...', color: 'text-yellow-600' };
    if (isConnected) return { text: '已连接', color: 'text-green-600' };
    if (connectionError) return { text: `连接错误: ${connectionError}`, color: 'text-red-600' };
    return { text: '未连接', color: 'text-gray-600' };
  };

  const connectionStatus = getConnectionStatus();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* 标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          AG-UI WebSocket 示例
        </h1>
        <p className="text-gray-600">
          使用AG-UI协议与Mario服务进行实时通信
        </p>
      </div>

      {/* 连接状态 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              isConnected ? 'bg-green-500' : 
              isConnecting ? 'bg-yellow-500' : 
              'bg-red-500'
            }`} />
            <span className={`font-medium ${connectionStatus.color}`}>
              {connectionStatus.text}
            </span>
          </div>
          
          <div className="flex space-x-2">
            {!isConnected && (
              <button
                onClick={reconnect}
                className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                重新连接
              </button>
            )}
            
            <button
              onClick={clearMessages}
              className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              清空消息
            </button>
          </div>
        </div>
      </div>

      {/* 配置面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="text-lg font-semibold mb-4">配置</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mario 用例
            </label>
            <select
              value={marioCase}
              onChange={(e) => setMarioCase(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="web_development">Web 开发</option>
              <option value="data_analysis">数据分析</option>
              <option value="api_testing">API 测试</option>
              <option value="automation">自动化</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              端口
            </label>
            <input
              type="number"
              value={port}
              onChange={(e) => setPort(parseInt(e.target.value) || 3000)}
              min="1000"
              max="65535"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200 p-4">
          <h3 className="text-lg font-semibold">对话历史</h3>
        </div>
        
        <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
          {messages.length === 0 && !currentStreamingMessage && (
            <div className="text-center text-gray-500 py-8">
              <p>还没有消息</p>
              <p className="text-sm mt-1">发送一条消息开始对话</p>
            </div>
          )}
          
          {messages.map((message: any, index: number) => (
            <div
              key={message.id}
              className={`flex ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-blue-500 text-white'
                    : message.type === 'error'
                    ? 'bg-red-100 text-red-800 border border-red-200'
                    : message.type === 'system'
                    ? 'bg-gray-100 text-gray-800 border border-gray-200'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                <div className="whitespace-pre-wrap">{message.content}</div>
                <div className="text-xs mt-1 opacity-70">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))}
          
          {/* 当前流式消息 */}
          {currentStreamingMessage && (
            <div className="flex justify-start">
              <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 text-gray-800 border-2 border-blue-200">
                <div className="whitespace-pre-wrap">{currentStreamingMessage}</div>
                <div className="text-xs mt-1 opacity-70 flex items-center">
                  <div className="animate-pulse">正在输入...</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 输入区域 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="输入您的消息..."
            disabled={!isConnected || isLoading}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
          />
          
          {isLoading ? (
            <button
              onClick={stopCurrentOperation}
              className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              停止
            </button>
          ) : (
            <button
              onClick={handleSendMessage}
              disabled={!isConnected || !inputMessage.trim()}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              发送
            </button>
          )}
        </div>
        
        {isLoading && loadingMessage && (
          <div className="mt-2 text-sm text-blue-600 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            {loadingMessage}
          </div>
        )}
      </div>

      {/* 调试信息 */}
      <details className="bg-gray-50 rounded-lg border border-gray-200">
        <summary className="p-4 cursor-pointer font-medium text-gray-700">
          调试信息
        </summary>
        <div className="p-4 pt-0 space-y-2 text-sm">
          <div><strong>连接状态:</strong> {isConnected ? '已连接' : '未连接'}</div>
          <div><strong>正在连接:</strong> {isConnecting ? '是' : '否'}</div>
          <div><strong>正在加载:</strong> {isLoading ? '是' : '否'}</div>
          <div><strong>消息数量:</strong> {messages.length}</div>
          <div><strong>当前流式消息长度:</strong> {currentStreamingMessage.length}</div>
          {connectionError && (
            <div className="text-red-600">
              <strong>连接错误:</strong> {connectionError}
            </div>
          )}
        </div>
      </details>
    </div>
  );
}

export default AGUIExample;