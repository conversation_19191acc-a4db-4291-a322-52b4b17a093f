import { Subject } from 'rxjs';
import { RunAgentInput, BaseEvent } from '@ag-ui/core';
import {
    BaseAgent,
    ExtendedAgentMetadata,
    AgentCapabilities,
    ArtifactContent,
    ChatMessage,
    ArtifactPageProps,
    createArtifactContent,
    createChatMessage
} from '@workspace/base-agent';
// 条件导入 React 和组件（仅在客户端环境）
let React: any = null;
let ImageArtifactPage: any = null;

try {
    // 只在客户端环境导入 React 组件
    if (typeof window !== 'undefined') {
        React = require('react');
        ImageArtifactPage = require('./components/ArtifactPage.js').ImageArtifactPage;
    }
} catch (error) {
    // 在服务器端环境中忽略导入错误
    console.log('Running in server environment, skipping React component imports');
}

export interface ImageAgentConfig {
    model?: string;
    temperature?: number;
    maxTokens?: number;
}

export class ImageAgent extends BaseAgent {
    private agentConfig: ImageAgentConfig;

    constructor(config: ImageAgentConfig = {}) {
        // 定义 agent 元数据
        const metadata: ExtendedAgentMetadata = {
            id: 'image-agent',
            name: 'Image Agent',
            description: 'AI agent specialized in generating and editing images',
            version: '0.1.0',
            capabilities: ['image-generation', 'image-editing', 'visual-content'],
            tags: ['image', 'visual', 'generation'],
            author: 'Muse Studio',
            icon: '🎨'
        };

        // 定义 agent 能力
        const capabilities: AgentCapabilities = {
            canGenerateArtifacts: true,
            supportedArtifactTypes: ['image-content'],
            canExecuteCode: false,
            canAccessFiles: false,
            canUseTools: true
        };

        // 基础配置
        const agentConfig = {
            name: metadata.name,
            description: metadata.description,
            version: metadata.version,
            capabilities: metadata.capabilities
        };

        super({
            config: agentConfig,
            tools: [],
            metadata,
            capabilities
        });

        this.agentConfig = {
            model: 'gpt-4o-2024-11-20',
            temperature: 0.7,
            maxTokens: 4000,
            ...config
        };
    }

    // 实现抽象方法：处理请求
    protected async processRequest(
        input: RunAgentInput,
        subject: Subject<BaseEvent>,
        messageId: string
    ): Promise<void> {
        const lastMessage = input.messages[input.messages.length - 1];
        const userPrompt = lastMessage?.content || '';

        try {
            // 模拟图像生成过程
            this.sendTextContent(subject, messageId, '正在生成图像...');

            // 模拟延迟
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // 生成模拟图像URL
            const imageUrl = this.generateMockImageUrl(userPrompt);
            this.sendTextContent(subject, messageId, `图像已生成：${imageUrl}`);
        } catch (error) {
            console.error('Image generation error:', error);
            // 发送错误内容
            this.sendTextContent(subject, messageId, '抱歉，图像生成过程中出现了错误。');
        }
    }

    // 实现扩展方法
    async processMessage(message: string): Promise<ChatMessage> {
        const artifacts: ArtifactContent[] = [];

        // 生成图像内容
        const imageUrl = this.generateMockImageUrl(message);

        const artifact = createArtifactContent(
            'image-content',
            'Generated Image',
            {
                url: imageUrl,
                prompt: message,
                width: 512,
                height: 512
            },
            { editable: true }
        );

        artifacts.push(artifact);

        const response = `我为您生成了图像。您可以在右侧查看它。`;

        return createChatMessage('assistant', response, artifacts);
    }

    async generateArtifact(prompt: string, type: string): Promise<ArtifactContent> {
        if (!this.validateArtifactType(type)) {
            throw new Error(`不支持的图像类型: ${type}`);
        }

        const imageUrl = this.generateMockImageUrl(prompt);

        return createArtifactContent(
            type,
            `Image: ${prompt}`,
            {
                url: imageUrl,
                prompt: prompt,
                width: 512,
                height: 512
            },
            { editable: true }
        );
    }

    renderArtifactPage(): any {
        // 在服务器端环境中返回 null
        if (!React || !ImageArtifactPage) {
            return null;
        }

        // 返回一个适配器组件来处理 props 差异
        return (artifactProps: ArtifactPageProps) => {
            const adaptedProps = {
                ...artifactProps,
                title: artifactProps.content.title,
                content: String(artifactProps.content.content || ''),
                onSaveContent: (newContent: string) => {
                    if (artifactProps.onContentChange) {
                        artifactProps.onContentChange({
                            ...artifactProps.content,
                            content: newContent,
                            updatedAt: new Date()
                        });
                    }
                },
                metadata: artifactProps.content.metadata || {},
                setMetadata: () => {},
                status: 'idle' as const,
                isCurrentVersion: true,
                currentVersionIndex: 0,
                suggestions: [],
                mode: 'edit' as const,
                isInline: false,
                isLoading: false
            };

            return React.createElement(ImageArtifactPage, adaptedProps);
        };
    }

    private generateMockImageUrl(prompt: string): string {
        // 生成模拟图像URL
        const encodedPrompt = encodeURIComponent(prompt);

        // 使用 picsum.photos 作为占位符图像服务
        const imageId = Math.floor(Math.random() * 1000) + 1;
        return `https://picsum.photos/512/512?random=${imageId}&text=${encodedPrompt}`;
    }
}

// 创建默认实例并导出
const imageAgent = new ImageAgent();
export default imageAgent;
