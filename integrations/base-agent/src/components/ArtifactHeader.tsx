"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  SaveIcon,
  ShareIcon,
  DownloadIcon,
  CopyIcon,
  EditIcon,
  MoreHorizontalIcon,
  ExternalLinkIcon,
  RefreshCwIcon,
  TrashIcon,
} from "lucide-react";
import { Artifact } from "../types";
import { ArtifactHeaderProps } from "../types";
import { cn, getArtifactIcon, formatRelativeTime } from "../utils";

export function ArtifactHeader({
  artifact,
  onSave,
  onExport,
  onShare,
  onEdit,
  onDelete,
  onRefresh,
  className,
}: ArtifactHeaderProps & {
  onEdit?: () => void;
  onDelete?: () => void;
  onRefresh?: () => void;
}) {
  const [isRenaming, setIsRenaming] = useState(false);
  const [newTitle, setNewTitle] = useState(artifact.title);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  const handleRename = () => {
    if (newTitle.trim() && newTitle !== artifact.title) {
      onEdit?.();
    }
    setIsRenaming(false);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content);
      // TODO: Add toast notification
    } catch (error) {
      console.error("Failed to copy content:", error);
    }
  };

  const handleExport = () => {
    const blob = new Blob([artifact.content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${artifact.title}.${getFileExtension(artifact.type)}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    onExport?.();
  };

  const getFileExtension = (type: string): string => {
    const extensions = {
      text: "txt",
      code: "js",
      image: "png",
      chart: "json",
      table: "csv",
      form: "html",
      component: "tsx",
    };
    return extensions[type as keyof typeof extensions] || "txt";
  };

  return (
    <div className={cn("flex items-center justify-between p-4 border-b border-border bg-background", className)}>
      {/* Left side - Artifact info */}
      <div className="flex items-center space-x-3">
        <div className="text-2xl">{getArtifactIcon(artifact.type)}</div>
        <div className="space-y-1">
          {isRenaming ? (
            <Input
              value={newTitle}
              onChange={(e) => setNewTitle(e.target.value)}
              onBlur={handleRename}
              onKeyDown={(e) => {
                if (e.key === "Enter") handleRename();
                if (e.key === "Escape") {
                  setNewTitle(artifact.title);
                  setIsRenaming(false);
                }
              }}
              className="h-6 text-lg font-semibold"
              autoFocus
            />
          ) : (
            <h1 
              className="text-lg font-semibold cursor-pointer hover:text-primary"
              onClick={() => setIsRenaming(true)}
            >
              {artifact.title}
            </h1>
          )}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <span className="capitalize">{artifact.type}</span>
            <span>•</span>
            <span>{formatRelativeTime(artifact.updatedAt)}</span>
            <span>•</span>
            <span className={cn(
              "px-2 py-0.5 rounded-full text-xs font-medium",
              artifact.status === "complete" && "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300",
              artifact.status === "in_progress" && "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300",
              artifact.status === "draft" && "bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300",
              artifact.status === "error" && "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300"
            )}>
              {artifact.status}
            </span>
          </div>
        </div>
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center space-x-2">
        {/* Primary actions */}
        <Button
          variant="outline"
          size="sm"
          onClick={onSave}
          className="hidden sm:flex"
        >
          <SaveIcon className="w-4 h-4 mr-2" />
          Save
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleCopy}
        >
          <CopyIcon className="w-4 h-4 mr-2" />
          Copy
        </Button>

        <Dialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <ShareIcon className="w-4 h-4 mr-2" />
              Share
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Share Artifact</DialogTitle>
              <DialogDescription>
                Share this artifact with others or export it in different formats.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="share-url">Share URL</Label>
                <div className="flex space-x-2">
                  <Input
                    id="share-url"
                    value={`${window.location.origin}/artifact/${artifact.id}`}
                    readOnly
                  />
                  <Button
                    variant="outline"
                    onClick={() => navigator.clipboard.writeText(`${window.location.origin}/artifact/${artifact.id}`)}
                  >
                    <CopyIcon className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsShareDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                onShare?.();
                setIsShareDialogOpen(false);
              }}>
                Share
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* More actions dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <MoreHorizontalIcon className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsRenaming(true)}>
              <EditIcon className="w-4 h-4 mr-2" />
              Rename
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExport}>
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onRefresh}>
              <RefreshCwIcon className="w-4 h-4 mr-2" />
              Refresh
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <ExternalLinkIcon className="w-4 h-4 mr-2" />
              Open in new tab
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={onDelete}
              className="text-destructive focus:text-destructive"
            >
              <TrashIcon className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

// Toolbar component for different artifact types
export function ArtifactToolbar({ 
  artifact, 
  onAction,
  className 
}: { 
  artifact: Artifact;
  onAction?: (action: string, data?: any) => void;
  className?: string;
}) {
  const getToolbarForType = (type: string) => {
    switch (type) {
      case "code":
        return (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => onAction?.("format")}>
              Format
            </Button>
            <Button variant="outline" size="sm" onClick={() => onAction?.("run")}>
              Run
            </Button>
            <Button variant="outline" size="sm" onClick={() => onAction?.("debug")}>
              Debug
            </Button>
          </div>
        );
      case "text":
        return (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => onAction?.("bold")}>
              <strong>B</strong>
            </Button>
            <Button variant="outline" size="sm" onClick={() => onAction?.("italic")}>
              <em>I</em>
            </Button>
            <Button variant="outline" size="sm" onClick={() => onAction?.("link")}>
              Link
            </Button>
          </div>
        );
      case "chart":
        return (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => onAction?.("edit_data")}>
              Edit Data
            </Button>
            <Button variant="outline" size="sm" onClick={() => onAction?.("change_type")}>
              Chart Type
            </Button>
            <Button variant="outline" size="sm" onClick={() => onAction?.("export_image")}>
              Export Image
            </Button>
          </div>
        );
      default:
        return (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => onAction?.("edit")}>
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={() => onAction?.("preview")}>
              Preview
            </Button>
          </div>
        );
    }
  };

  return (
    <div className={cn("flex items-center justify-between p-3 border-b border-border bg-muted/30", className)}>
      <div className="flex items-center space-x-2">
        {getToolbarForType(artifact.type)}
      </div>
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <span>{artifact.content.length} characters</span>
        {artifact.metadata?.language && (
          <>
            <span>•</span>
            <span>{artifact.metadata.language}</span>
          </>
        )}
      </div>
    </div>
  );
}

export default ArtifactHeader;
