"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, CardContent, CardHeader } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import {
  ExpandIcon,
  EyeIcon,
  CodeIcon,
  FileTextIcon,
  ImageIcon,
  BarChartIcon,
  TableIcon,
  FormInputIcon,
  ComponentIcon,
  ExternalLinkIcon,
} from "lucide-react";
import { Artifact } from "../types";
import { ArtifactPreviewProps } from "../types";
import { cn, getArtifactIcon, formatRelativeTime, truncateText } from "../utils";
import { ArtifactFooterCompact } from "./ArtifactFooter";

export function ArtifactPreview({
  artifact,
  isExpanded = false,
  onExpand,
  onAction,
  className,
}: ArtifactPreviewProps & {
  onAction?: (action: string, data?: any) => void;
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "code":
        return <CodeIcon className="w-4 h-4" />;
      case "text":
        return <FileTextIcon className="w-4 h-4" />;
      case "image":
        return <ImageIcon className="w-4 h-4" />;
      case "chart":
        return <BarChartIcon className="w-4 h-4" />;
      case "table":
        return <TableIcon className="w-4 h-4" />;
      case "form":
        return <FormInputIcon className="w-4 h-4" />;
      case "component":
        return <ComponentIcon className="w-4 h-4" />;
      default:
        return <FileTextIcon className="w-4 h-4" />;
    }
  };

  const getPreviewContent = () => {
    switch (artifact.type) {
      case "code":
        return (
          <pre className="text-xs font-mono bg-muted/50 p-2 rounded overflow-hidden">
            <code>{truncateText(artifact.content, 200)}</code>
          </pre>
        );
      case "text":
        return (
          <div className="text-sm text-muted-foreground">
            {truncateText(artifact.content, 150)}
          </div>
        );
      case "chart":
        return (
          <div className="flex items-center justify-center h-20 bg-muted/50 rounded">
            <BarChartIcon className="w-8 h-8 text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Chart Preview</span>
          </div>
        );
      case "table":
        return (
          <div className="text-xs">
            <div className="grid grid-cols-3 gap-2 mb-1">
              <div className="font-medium">Col 1</div>
              <div className="font-medium">Col 2</div>
              <div className="font-medium">Col 3</div>
            </div>
            <div className="grid grid-cols-3 gap-2 text-muted-foreground">
              <div>Data...</div>
              <div>Data...</div>
              <div>Data...</div>
            </div>
          </div>
        );
      case "image":
        return (
          <div className="flex items-center justify-center h-20 bg-muted/50 rounded">
            <ImageIcon className="w-8 h-8 text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Image Preview</span>
          </div>
        );
      default:
        return (
          <div className="text-sm text-muted-foreground">
            {truncateText(artifact.content, 150)}
          </div>
        );
    }
  };

  if (isExpanded) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="text-lg">{getArtifactIcon(artifact.type)}</div>
              <div>
                <h3 className="font-semibold">{artifact.title}</h3>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <span className="capitalize">{artifact.type}</span>
                  <span>•</span>
                  <span>{formatRelativeTime(artifact.updatedAt)}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={
                artifact.status === "complete" ? "default" :
                artifact.status === "in_progress" ? "secondary" :
                artifact.status === "error" ? "destructive" : "outline"
              }>
                {artifact.status}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAction?.("view_full")}
              >
                <ExternalLinkIcon className="w-4 h-4 mr-1" />
                Open
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {getPreviewContent()}
            {artifact.metadata?.description && (
              <p className="text-sm text-muted-foreground">
                {artifact.metadata.description}
              </p>
            )}
          </div>
        </CardContent>
        <ArtifactFooterCompact
          artifact={artifact}
          onAction={onAction}
        />
      </Card>
    );
  }

  return (
    <div className={cn("group", className)}>
      <Card className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 rounded-lg bg-muted/50 flex items-center justify-center">
                {getTypeIcon(artifact.type)}
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium truncate">{artifact.title}</h4>
                <Badge 
                  variant="outline" 
                  className="ml-2 flex-shrink-0 text-xs"
                >
                  {artifact.type}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground mb-2">
                {truncateText(artifact.content, 100)}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">
                  {formatRelativeTime(artifact.updatedAt)}
                </span>
                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <EyeIcon className="w-3 h-3" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto">
                      <ArtifactPreview
                        artifact={artifact}
                        isExpanded={true}
                        onAction={onAction}
                      />
                    </DialogContent>
                  </Dialog>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-6 w-6 p-0"
                    onClick={() => {
                      onExpand?.();
                      onAction?.("expand");
                    }}
                  >
                    <ExpandIcon className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Grid layout for multiple artifacts
export function ArtifactPreviewGrid({
  artifacts,
  onArtifactAction,
  className,
}: {
  artifacts: Artifact[];
  onArtifactAction?: (artifactId: string, action: string, data?: any) => void;
  className?: string;
}) {
  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", className)}>
      {artifacts.map((artifact) => (
        <ArtifactPreview
          key={artifact.id}
          artifact={artifact}
          onAction={(action, data) => onArtifactAction?.(artifact.id, action, data)}
        />
      ))}
    </div>
  );
}

// List layout for artifacts
export function ArtifactPreviewList({
  artifacts,
  onArtifactAction,
  className,
}: {
  artifacts: Artifact[];
  onArtifactAction?: (artifactId: string, action: string, data?: any) => void;
  className?: string;
}) {
  return (
    <div className={cn("space-y-3", className)}>
      {artifacts.map((artifact) => (
        <ArtifactPreview
          key={artifact.id}
          artifact={artifact}
          isExpanded={false}
          onAction={(action, data) => onArtifactAction?.(artifact.id, action, data)}
          className="w-full"
        />
      ))}
    </div>
  );
}

// Compact preview for chat messages
export function ArtifactPreviewCompact({
  artifact,
  onAction,
  className,
}: {
  artifact: Artifact;
  onAction?: (action: string, data?: any) => void;
  className?: string;
}) {
  return (
    <div className={cn("flex items-center space-x-3 p-3 border border-border rounded-lg bg-background", className)}>
      <div className="text-lg">{getArtifactIcon(artifact.type)}</div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <h4 className="font-medium truncate">{artifact.title}</h4>
          <Badge variant="outline" className="text-xs">
            {artifact.type}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground truncate">
          {truncateText(artifact.content, 80)}
        </p>
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={() => onAction?.("view")}
      >
        View
      </Button>
    </div>
  );
}

export default ArtifactPreview;
