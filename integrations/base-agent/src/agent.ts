import {
  AbstractAgent,
  AgentConfig,
  EventType,
  BaseEvent,
  Message,
  AssistantMessage,
  RunAgentInput,
  MessagesSnapshotEvent,
  RunFinishedEvent,
  RunStartedEvent,
  TextMessageChunkEvent,
  ToolCallArgsEvent,
  ToolCallEndEvent,
  ToolCallStartEvent,
  ToolCall,
  ToolMessage,
} from "@ag-ui/client";
import { Observable } from "rxjs";
import { createOpenAI } from "@ai-sdk/openai";
import {
  CoreMessage,
  LanguageModelV1,
  processDataStream,
  streamText,
  tool as createTool,
  ToolChoice,
  ToolSet,
} from "ai";
import { randomUUID } from "crypto";
import { z } from "zod";
import { BaseAgentConfig, Artifact, ArtifactType } from "./types";
import { BASE_SYSTEM_PROMPT, buildSystemPrompt, getRoleSpecificPrompt } from "./prompts";
import { createArtifact, generateId } from "./utils";

type ProcessedEvent =
  | MessagesSnapshotEvent
  | RunFinishedEvent
  | RunStartedEvent
  | TextMessageChunkEvent
  | ToolCallArgsEvent
  | ToolCallEndEvent
  | ToolCallStartEvent;

export interface BaseAgentOptions extends AgentConfig {
  model?: LanguageModelV1;
  maxSteps?: number;
  toolChoice?: ToolChoice<Record<string, unknown>>;
  config?: BaseAgentConfig;
  openaiConfig?: {
    apiKey?: string;
    baseURL?: string;
    model?: string;
  };
}

export class BaseAgent extends AbstractAgent {
  model: LanguageModelV1;
  maxSteps: number;
  toolChoice: ToolChoice<Record<string, unknown>>;
  config: BaseAgentConfig;
  artifacts: Map<string, Artifact> = new Map();

  constructor({
    model,
    maxSteps,
    toolChoice,
    config,
    openaiConfig,
    ...rest
  }: BaseAgentOptions = {}) {
    super({ ...rest });
    
    // 使用用户偏好的Meituan AIGC配置
    const openaiProvider = createOpenAI({
      apiKey: openaiConfig?.apiKey ?? "1914304559263223873",
      baseURL: openaiConfig?.baseURL ?? "https://aigc.sankuai.com/v1/openai/native/",
    });
    this.model = model ?? openaiProvider(openaiConfig?.model ?? "gpt-4o-2024-11-20");
    
    this.maxSteps = maxSteps ?? 3;
    this.toolChoice = toolChoice ?? "auto";
    this.config = {
      agentId: "base-agent",
      description: "A comprehensive base agent with artifact support",
      model: openaiConfig?.model ?? "gpt-4o-2024-11-20",
      temperature: 0.7,
      maxTokens: 4000,
      systemPrompt: BASE_SYSTEM_PROMPT,
      enabledFeatures: ["chat", "generative_ui", "tool_calling"],
      ui: {
        theme: "system",
        showHeader: true,
        showFooter: true,
        showToolbar: true,
        layout: "default",
      },
      ...config,
    };
  }

  public run(input: RunAgentInput): Observable<BaseEvent> {
    const finalMessages: Message[] = [...input.messages];

    return new Observable<ProcessedEvent>((subscriber) => {
      subscriber.next({
        type: EventType.RUN_STARTED,
        threadId: input.threadId,
        runId: input.runId,
      } as RunStartedEvent);

      // 构建系统提示词
      const systemPrompt = buildSystemPrompt(
        this.config.systemPrompt,
        this.config.agentId ? getRoleSpecificPrompt(this.config.agentId) : undefined
      );

      // 添加系统消息
      const messagesWithSystem: CoreMessage[] = [
        { role: "system", content: systemPrompt },
        ...this.convertMessagesToCoreMessages(input.messages),
      ];

      const response = streamText({
        model: this.model,
        messages: messagesWithSystem,
        tools: this.createTools(input.tools),
        maxSteps: this.maxSteps,
        toolChoice: this.toolChoice,
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens,
      });

      let messageId = randomUUID();
      let assistantMessage: AssistantMessage = {
        id: messageId,
        role: "assistant",
        content: "",
        toolCalls: [],
      };
      finalMessages.push(assistantMessage);

      processDataStream({
        stream: response.toDataStreamResponse().body!,
        onTextPart: (text) => {
          assistantMessage.content += text;
          const event: TextMessageChunkEvent = {
            type: EventType.TEXT_MESSAGE_CHUNK,
            role: "assistant",
            messageId,
            delta: text,
          };
          subscriber.next(event);
        },
        onFinishMessagePart: () => {
          // Emit message snapshot
          const event: MessagesSnapshotEvent = {
            type: EventType.MESSAGES_SNAPSHOT,
            messages: finalMessages,
          };
          subscriber.next(event);

          // Emit run finished event
          subscriber.next({
            type: EventType.RUN_FINISHED,
            threadId: input.threadId,
            runId: input.runId,
          } as RunFinishedEvent);

          // Complete the observable
          subscriber.complete();
        },
        onToolCallPart: (streamPart) => {
          let toolCall: ToolCall = {
            id: streamPart.toolCallId,
            type: "function",
            function: {
              name: streamPart.toolName,
              arguments: JSON.stringify(streamPart.args),
            },
          };
          assistantMessage.toolCalls!.push(toolCall);

          const startEvent: ToolCallStartEvent = {
            type: EventType.TOOL_CALL_START,
            parentMessageId: messageId,
            toolCallId: streamPart.toolCallId,
            toolCallName: streamPart.toolName,
          };
          subscriber.next(startEvent);

          const argsEvent: ToolCallArgsEvent = {
            type: EventType.TOOL_CALL_ARGS,
            toolCallId: streamPart.toolCallId,
            delta: JSON.stringify(streamPart.args),
          };
          subscriber.next(argsEvent);

          const endEvent: ToolCallEndEvent = {
            type: EventType.TOOL_CALL_END,
            toolCallId: streamPart.toolCallId,
          };
          subscriber.next(endEvent);
        },
        onToolResultPart: (streamPart) => {
          const toolMessage: ToolMessage = {
            role: "tool",
            id: randomUUID(),
            toolCallId: streamPart.toolCallId,
            content: JSON.stringify(streamPart.result),
          };
          finalMessages.push(toolMessage);
        },
        onErrorPart: (streamPart) => {
          subscriber.error(streamPart);
        },
      }).catch((error) => {
        console.error("BaseAgent stream error:", error);
        subscriber.error(error);
      });

      return () => {};
    });
  }

  private convertMessagesToCoreMessages(messages: Message[]): CoreMessage[] {
    const result: CoreMessage[] = [];

    for (const message of messages) {
      if (message.role === "assistant") {
        const parts: any[] = message.content ? [{ type: "text", text: message.content }] : [];
        for (const toolCall of message.toolCalls ?? []) {
          parts.push({
            type: "tool-call",
            toolCallId: toolCall.id,
            toolName: toolCall.function.name,
            args: JSON.parse(toolCall.function.arguments),
          });
        }
        result.push({
          role: "assistant",
          content: parts,
        });
      } else if (message.role === "user") {
        result.push({
          role: "user",
          content: message.content || "",
        });
      } else if (message.role === "tool") {
        let toolName = "unknown";
        for (const msg of messages) {
          if (msg.role === "assistant") {
            for (const toolCall of msg.toolCalls ?? []) {
              if (toolCall.id === message.toolCallId) {
                toolName = toolCall.function.name;
                break;
              }
            }
          }
        }
        result.push({
          role: "tool",
          content: [
            {
              type: "tool-result",
              toolCallId: message.toolCallId,
              toolName: toolName,
              result: message.content,
            },
          ],
        });
      }
    }

    return result;
  }

  private createTools(tools: RunAgentInput["tools"]): ToolSet {
    const baseTools = this.getBaseTools();
    const inputTools = this.convertInputTools(tools);
    
    return {
      ...baseTools,
      ...inputTools,
    };
  }

  private getBaseTools(): ToolSet {
    return {
      create_artifact: createTool({
        description: "Create a new artifact (document, code, component, etc.)",
        parameters: z.object({
          type: z.enum(["text", "code", "image", "chart", "table", "form", "component"]),
          title: z.string().describe("Title of the artifact"),
          content: z.string().describe("Content of the artifact"),
          metadata: z.record(z.any()).optional().describe("Additional metadata"),
        }),
        execute: async ({ type, title, content, metadata }) => {
          const artifact = createArtifact(type as ArtifactType, title, content, metadata);
          this.artifacts.set(artifact.id, artifact);
          return {
            success: true,
            artifactId: artifact.id,
            message: `Created ${type} artifact: ${title}`,
          };
        },
      }),
      
      update_artifact: createTool({
        description: "Update an existing artifact",
        parameters: z.object({
          artifactId: z.string().describe("ID of the artifact to update"),
          title: z.string().optional().describe("New title"),
          content: z.string().optional().describe("New content"),
          metadata: z.record(z.any()).optional().describe("Updated metadata"),
        }),
        execute: async ({ artifactId, title, content, metadata }) => {
          const artifact = this.artifacts.get(artifactId);
          if (!artifact) {
            return { success: false, message: "Artifact not found" };
          }
          
          const updates: Partial<Artifact> = {};
          if (title) updates.title = title;
          if (content) updates.content = content;
          if (metadata) updates.metadata = { ...artifact.metadata, ...metadata };
          
          const updatedArtifact = { ...artifact, ...updates, updatedAt: new Date() };
          this.artifacts.set(artifactId, updatedArtifact);
          
          return {
            success: true,
            message: `Updated artifact: ${updatedArtifact.title}`,
          };
        },
      }),
    };
  }

  private convertInputTools(tools: RunAgentInput["tools"]): ToolSet {
    return tools.reduce(
      (acc: ToolSet, tool: RunAgentInput["tools"][number]) => ({
        ...acc,
        [tool.name]: createTool({
          description: tool.description,
          parameters: this.convertJsonSchemaToZodSchema(tool.parameters, true),
        }),
      }),
      {},
    );
  }

  private convertJsonSchemaToZodSchema(jsonSchema: any, required: boolean): z.ZodSchema {
    if (jsonSchema.type === "object") {
      const spec: { [key: string]: z.ZodSchema } = {};

      if (!jsonSchema.properties || !Object.keys(jsonSchema.properties).length) {
        return !required ? z.object(spec).optional() : z.object(spec);
      }

      for (const [key, value] of Object.entries(jsonSchema.properties)) {
        spec[key] = this.convertJsonSchemaToZodSchema(
          value,
          jsonSchema.required ? jsonSchema.required.includes(key) : false,
        );
      }
      let schema = z.object(spec).describe(jsonSchema.description);
      return required ? schema : schema.optional();
    } else if (jsonSchema.type === "string") {
      let schema = z.string().describe(jsonSchema.description);
      return required ? schema : schema.optional();
    } else if (jsonSchema.type === "number") {
      let schema = z.number().describe(jsonSchema.description);
      return required ? schema : schema.optional();
    } else if (jsonSchema.type === "boolean") {
      let schema = z.boolean().describe(jsonSchema.description);
      return required ? schema : schema.optional();
    } else if (jsonSchema.type === "array") {
      let itemSchema = this.convertJsonSchemaToZodSchema(jsonSchema.items, true);
      let schema = z.array(itemSchema).describe(jsonSchema.description);
      return required ? schema : schema.optional();
    }
    throw new Error("Invalid JSON schema");
  }

  // Public methods for artifact management
  public getArtifact(id: string): Artifact | undefined {
    return this.artifacts.get(id);
  }

  public getAllArtifacts(): Artifact[] {
    return Array.from(this.artifacts.values());
  }

  public deleteArtifact(id: string): boolean {
    return this.artifacts.delete(id);
  }
}
