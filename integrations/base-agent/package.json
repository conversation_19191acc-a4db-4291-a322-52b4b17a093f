{"name": "@ag-ui/base-agent", "version": "0.0.1", "description": "A comprehensive base agent implementation using AG-UI and shadcn/ui components", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**", "src/**"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./server": {"types": "./dist/server.d.ts", "import": "./dist/server.mjs", "require": "./dist/server.js"}, "./components": {"types": "./dist/components/index.d.ts", "import": "./dist/components/index.mjs", "require": "./dist/components/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "keywords": ["ag-ui", "agent", "ai", "copilotkit", "react", "nextjs", "shadcn", "base-agent"], "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "rxjs": "7.8.1"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "jest": "^30.0.1", "ts-jest": "^29.4.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "dependencies": {"@ag-ui/client": "workspace:*", "@ag-ui/core": "workspace:*", "@workspace/ui": "workspace:*", "@ai-sdk/openai": "^1.3.22", "@copilotkit/react-core": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@radix-ui/react-slot": "^1.2.3", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "openai": "^4.104.0", "react-resizable-panels": "^2.1.7", "tailwind-merge": "^2.6.0", "zod": "^3.25.67"}}