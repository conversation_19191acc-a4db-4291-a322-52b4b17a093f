// Haiku Integration 统一导出
// 使用命名空间模式避免命名冲突

import type { IntegrationPackage } from '@workspace/shared';
import { HaikuWorkspace as HaikuClientWorkspace } from './client.js';
import { HaikuWorkspace } from './workspace.js';
import { createAgent } from './agent.js';
import { DEFAULT_CONFIG } from './config.js';
import { topicExtractorTool } from './tools/topic-tool.js';
import { contentGeneratorTool } from './tools/generate-tool.js';

// 导出命名空间对象，避免命名冲突
export const HaikuIntegration = {
  // 基础信息
  id: 'haiku',
  name: 'Haiku Integration',
  description: '专业的俳句生成助手，能够创作优美的中英双语俳句',
  version: '0.0.1',

  // 核心功能
  createAgent,
  HaikuAgent: createAgent, // 别名

  // UI组件
  Workspace: HaikuWorkspace,

  // 配置
  defaultConfig: DEFAULT_CONFIG,

  // 工具
  tools: {
    topicExtractor: topicExtractorTool,
    contentGenerator: contentGeneratorTool
  },

  // 工具数组（用于注册）
  toolsArray: [topicExtractorTool, contentGeneratorTool]
};

// 创建 Integration Package 用于注册
export const haikuIntegrationPackage: IntegrationPackage = {
  id: HaikuIntegration.id,
  name: HaikuIntegration.name,
  description: HaikuIntegration.description,
  version: HaikuIntegration.version,
  createAgent: HaikuIntegration.createAgent,
  workspace: HaikuIntegration.Workspace,
  defaultConfig: HaikuIntegration.defaultConfig,
  tools: HaikuIntegration.toolsArray
};

// 默认导出命名空间
export default HaikuIntegration;

// 导出类型
export type {
  HaikuAgentConfig,
  HaikuAgentState,
  HaikuWorkspaceProps,
  Item,
  GenerateParams,
  GenerateResult
} from './types.js';

// 导出核心组件
export { HaikuAgent, createAgent } from './agent.js';
export { HaikuWorkspace as HaikuClientWorkspace } from './client.js';
export { HaikuWorkspace } from './workspace.js';
export { DEFAULT_CONFIG } from './config.js';
export { topicExtractorTool } from './tools/topic-tool.js';
export { contentGeneratorTool } from './tools/generate-tool.js';
