import { AbstractAgent, RunAgentInput, EventType, BaseEvent } from '@ag-ui/client';
import { Observable } from 'rxjs';
import type { AgentConfig, AgentState } from './types.js';
import { TopicExtractorTool } from './tools/topic-tool.js';
import { ContentGeneratorTool } from './tools/generate-tool.js';

export class Agent extends AbstractAgent {
  private config: AgentConfig;

  constructor(config?: Partial<AgentConfig>) {
    const fullConfig: AgentConfig = {
      threadId: config?.threadId,
      initialState: {
        items: [],
        currentItem: undefined,
        selectedIndex: -1,
        topic: '',
        isGenerating: false,
        history: [],
        preferences: {
          style: 'traditional',
          outputFormat: 'both',
          includeExtras: true
        },
        lastError: undefined,
        ...config?.initialState
      },
      ...config
    };

    super(fullConfig);
    this.config = fullConfig;
  }

  protected run(input: RunAgentInput): Observable<BaseEvent> {
    return new Observable<BaseEvent>((observer) => {
      // Emit RUN_STARTED event
      observer.next({
        type: EventType.RUN_STARTED,
        timestamp: Date.now()
      });

      // Process the input asynchronously
      this.processInput(input)
        .then(() => {
          // Emit RUN_FINISHED event
          observer.next({
            type: EventType.RUN_FINISHED,
            timestamp: Date.now()
          });
          observer.complete();
        })
        .catch((error) => {
          // Emit RUN_ERROR event
          observer.next({
            type: EventType.RUN_ERROR,
            timestamp: Date.now(),
            rawEvent: { error: error.message }
          });
          observer.error(error);
        });
    });
  }

  private async processInput(input: RunAgentInput): Promise<void> {
    const { messages } = input;

    // Get the last user message
    const lastUserMessage = messages?.filter((msg) => msg.role === 'user')?.pop();

    if (!lastUserMessage?.content) {
      throw new Error('No user message found');
    }

    const messageId = Date.now().toString();

    // Extract topic from the message
    const topicTool = new TopicExtractorTool();
    const topic = await topicTool.execute(lastUserMessage.content);

    // Update state
    const currentState = this.state as AgentState;
    this.state = {
      ...currentState,
      topic,
      isGenerating: true
    };

    // Generate content
    const generateTool = new ContentGeneratorTool();
    const result = await generateTool.execute({ topic });

    // Create new item object
    const newItem = {
      primary: result.primary,
      secondary: result.secondary,
      extras: result.extras,
      metadata: result.metadata
    };

    // Update state with new item
    const updatedState = this.state as AgentState;
    const newItems = [...(updatedState.items || []), newItem];

    this.state = {
      ...updatedState,
      currentItem: newItem,
      items: newItems,
      selectedIndex: newItems.length - 1,
      history: [
        ...(updatedState.history || []),
        {
          topic,
          item: newItem,
          timestamp: Date.now()
        }
      ],
      isGenerating: false
    };

    // Add the result to messages
    this.messages.push({
      id: messageId,
      role: 'assistant',
      content: `${newItem.primary}\n\n${newItem.secondary}`
    });
  }

  // Public methods for interacting with the agent
  getCurrentItem(): any {
    const state = this.state as AgentState;
    return state?.currentItem || null;
  }

  getAllItems(): any[] {
    const state = this.state as AgentState;
    return state?.items || [];
  }

  getHistory(): AgentState['history'] {
    const state = this.state as AgentState;
    return state?.history || [];
  }

  updatePreferences(preferences: Partial<AgentState['preferences']>): void {
    const currentState = this.state as AgentState;
    if (currentState) {
      this.state = {
        ...currentState,
        preferences: {
          ...currentState.preferences,
          ...preferences
        }
      };
    }
  }

  clearHistory(): void {
    const currentState = this.state as AgentState;
    if (currentState) {
      this.state = {
        ...currentState,
        items: [],
        history: [],
        selectedIndex: -1,
        currentItem: undefined
      };
    }
  }
}

// Factory function for creating Agent
export const createAgent = (config?: Partial<AgentConfig>): Agent => {
  return new Agent(config);
};
