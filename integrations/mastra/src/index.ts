import type {
  AgentConfig,
  AssistantMessage,
  BaseEvent,
  Message,
  MessagesSnapshotEvent,
  RunAgentInput,
  RunFinishedEvent,
  RunStartedEvent,
  TextMessageChunkEvent,
  ToolCall,
  ToolCallArgsEvent,
  ToolCallEndEvent,
  ToolCallStartEvent,
  ToolMessage,
} from "@ag-ui/client";
import { AbstractAgent, EventType } from "@ag-ui/client";
import {
  CopilotRuntime,
  copilotRuntimeNodeHttpEndpoint,
  CopilotServiceAdapter,
  ExperimentalEmptyAdapter,
} from "@copilotkit/runtime";
import { processDataStream } from "@ai-sdk/ui-utils";
import type { CoreMessage, Mastra } from "@mastra/core";
import { registerApiRoute } from "@mastra/core/server";
import type { Agent as LocalMastraAgent } from "@mastra/core/agent";
import type { Context } from "hono";
import { RuntimeContext } from "@mastra/core/runtime-context";
import { randomUUID } from "crypto";
import { Observable } from "rxjs";
import { MastraClient } from "@mastra/client-js";
type RemoteMastraAgent = ReturnType<MastraClient["getAgent"]>;

export interface MastraAgentConfig extends AgentConfig {
  agent: LocalMastraAgent | RemoteMastraAgent;
  resourceId?: string;
  runtimeContext?: RuntimeContext;
}

interface MastraAgentStreamOptions {
  onTextPart?: (text: string) => void;
  onFinishMessagePart?: () => void;
  onToolCallPart?: (streamPart: { toolCallId: string; toolName: string; args: any }) => void;
  onToolResultPart?: (streamPart: { toolCallId: string; result: any }) => void;
  onError?: (error: Error) => void;
}

export class MastraAgent extends AbstractAgent {
  agent: LocalMastraAgent | RemoteMastraAgent;
  resourceId?: string;
  runtimeContext?: RuntimeContext;

  constructor({ agent, resourceId, runtimeContext, ...rest }: MastraAgentConfig) {
    super(rest);
    this.agent = agent;
    this.resourceId = resourceId;
    this.runtimeContext = runtimeContext;
  }
}

/**
 * Embedded Mastra Agent that doesn't require external @mastra/core dependencies
 * Provides simplified agent functionality with built-in tools
 */
export class EmbeddedMastraAgent extends AbstractAgent {
  resourceId?: string;
  name: string;
  description: string;

  constructor({ 
    agentId, 
    name, 
    description, 
    resourceId 
  }: { 
    agentId: string;
    name: string;
    description: string;
    resourceId?: string;
  }) {
    super({ agentId });
    this.name = name;
    this.description = description;
    this.resourceId = resourceId;
  }

  protected run(input: RunAgentInput): Observable<BaseEvent> {
    const finalMessages: Message[] = [...input.messages];
    let messageId = randomUUID();
    let assistantMessage: AssistantMessage = {
      id: messageId,
      role: "assistant",
      content: "",
      toolCalls: [],
    };
    finalMessages.push(assistantMessage);

    return new Observable<BaseEvent>((subscriber) => {
      subscriber.next({
        type: EventType.RUN_STARTED,
        threadId: input.threadId,
        runId: input.runId,
      } as RunStartedEvent);

      // Simulate processing with embedded logic
      this.processEmbeddedAgent(input, {
        onTextPart: (text) => {
          assistantMessage.content += text;
          const event: TextMessageChunkEvent = {
            type: EventType.TEXT_MESSAGE_CHUNK,
            role: "assistant",
            messageId,
            delta: text,
          };
          subscriber.next(event);
        },
        onFinishMessagePart: () => {
          const event: MessagesSnapshotEvent = {
            type: EventType.MESSAGES_SNAPSHOT,
            messages: finalMessages,
          };
          subscriber.next(event);

          subscriber.next({
            type: EventType.RUN_FINISHED,
            threadId: input.threadId,
            runId: input.runId,
          } as RunFinishedEvent);

          subscriber.complete();
        },
        onError: (error) => {
          subscriber.error(error);
        },
      });

      return () => {};
    });
  }

  private async processEmbeddedAgent(
    input: RunAgentInput,
    callbacks: {
      onTextPart: (text: string) => void;
      onFinishMessagePart: () => void;
      onError: (error: Error) => void;
    }
  ) {
    try {
      const lastMessage = input.messages[input.messages.length - 1];
      const userQuery = lastMessage?.content || "";
      
      let response = "";
      
      if (this.agentId === 'math_assistant') {
        response = this.handleMathQuery(userQuery);
      } else {
        response = this.handleGeneralQuery(userQuery);
      }
      
      // Simulate streaming by sending response in chunks
      const chunks = response.split(' ');
      for (let i = 0; i < chunks.length; i++) {
        const chunk = i === 0 ? chunks[i] : ' ' + chunks[i];
        callbacks.onTextPart(chunk);
        await new Promise(resolve => setTimeout(resolve, 50)); // Simulate delay
      }
      
      callbacks.onFinishMessagePart();
    } catch (error) {
      callbacks.onError(error as Error);
    }
  }

  private handleMathQuery(query: string): string {
    // Simple math processing
    const mathPattern = /([0-9+\-*/().\s]+)/g;
    const mathExpression = query.match(mathPattern)?.[0];
    
    if (mathExpression) {
      try {
        // Basic math evaluation (safe for simple expressions)
        const result = Function('"use strict"; return (' + mathExpression.replace(/[^0-9+\-*/().\s]/g, '') + ')')();
        return `The result of ${mathExpression.trim()} is ${result}.`;
      } catch {
        return "I can help with basic math calculations. Please provide a simple mathematical expression like '2 + 2' or '10 * 5'.";
      }
    }
    
    return "I'm a math assistant. I can help you with calculations, equations, and mathematical problems. What would you like to calculate?";
  }

  private handleGeneralQuery(query: string): string {
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('hello') || lowerQuery.includes('hi')) {
      return "Hello! I'm your general assistant. How can I help you today?";
    }
    
    if (lowerQuery.includes('time')) {
      return `The current time is ${new Date().toLocaleTimeString()}.`;
    }
    
    if (lowerQuery.includes('date')) {
      return `Today's date is ${new Date().toLocaleDateString()}.`;
    }
    
    if (lowerQuery.includes('weather')) {
      return "I'm a general assistant and don't have access to real-time weather data. You might want to check a weather service or app for current conditions.";
    }
    
    return "I'm here to help! I can assist with general questions, provide information about time and date, or help with various tasks. What would you like to know?";
  }

  static async getRemoteAgents({
    mastraClient,
    resourceId,
  }: {
    mastraClient: MastraClient;
    resourceId?: string;
  }): Promise<Record<string, AbstractAgent>> {
    const agents = await mastraClient.getAgents();

    return Object.entries(agents).reduce(
      (acc, [agentId]) => {
        const agent = mastraClient.getAgent(agentId);

        acc[agentId] = new MastraAgent({
          agentId,
          agent,
          resourceId,
        });

        return acc;
      },
      {} as Record<string, AbstractAgent>,
    );
  }

  static getLocalAgents({
    mastra,
    resourceId,
    runtimeContext,
  }: {
    mastra: Mastra;
    resourceId?: string;
    runtimeContext?: RuntimeContext;
  }): Record<string, AbstractAgent> {
    const agents = mastra.getAgents() || {};
    const networks = mastra.getNetworks() || [];

    const networkAGUI = networks.reduce(
      (acc, network) => {
        acc[network.name!] = new MastraAgent({
          agentId: network.name!,
          agent: network as unknown as LocalMastraAgent,
          resourceId,
          runtimeContext,
        });
        return acc;
      },
      {} as Record<string, AbstractAgent>,
    );

    const agentAGUI = Object.entries(agents).reduce(
      (acc, [agentId, agent]) => {
        acc[agentId] = new MastraAgent({
          agentId,
          agent,
          resourceId,
          runtimeContext,
        });
        return acc;
      },
      {} as Record<string, AbstractAgent>,
    );

    return {
      ...agentAGUI,
      ...networkAGUI,
    };
  }

  /**
   * Creates embedded local agents without requiring external Mastra instance
   * This method provides a simplified way to create agents with built-in functionality
   */
  static getEmbeddedAgents({
    resourceId,
  }: {
    resourceId?: string;
  } = {}): Record<string, AbstractAgent> {
    // Create embedded agent implementations that don't require @mastra/core
    const embeddedAgents: Record<string, AbstractAgent> = {};

    // General Assistant Agent
    embeddedAgents['general_assistant'] = new EmbeddedMastraAgent({
      agentId: 'general_assistant',
      name: 'General Assistant',
      description: 'A helpful general-purpose assistant',
      resourceId,
    });

    // Math Assistant Agent
    embeddedAgents['math_assistant'] = new EmbeddedMastraAgent({
      agentId: 'math_assistant', 
      name: 'Math Assistant',
      description: 'A specialized assistant for mathematical calculations',
      resourceId,
    });

    return embeddedAgents;
  }

  static getLocalAgent({
    mastra,
    agentId,
    resourceId,
    runtimeContext,
  }: {
    mastra: Mastra;
    agentId: string;
    resourceId?: string;
    runtimeContext?: RuntimeContext;
  }) {
    const agent = mastra.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }
    return new MastraAgent({
      agentId,
      agent,
      resourceId,
      runtimeContext,
    }) as AbstractAgent;
  }

  static getNetwork({
    mastra,
    networkId,
    resourceId,
    runtimeContext,
  }: {
    mastra: Mastra;
    networkId: string;
    resourceId?: string;
    runtimeContext?: RuntimeContext;
  }) {
    const network = mastra.getNetwork(networkId);
    if (!network) {
      throw new Error(`Network ${networkId} not found`);
    }
    return new MastraAgent({
      agentId: network.name!,
      agent: network as unknown as LocalMastraAgent,
      resourceId,
      runtimeContext,
    }) as AbstractAgent;
  }

  protected run(input: RunAgentInput): Observable<BaseEvent> {
    const finalMessages: Message[] = [...input.messages];
    let messageId = randomUUID();
    let assistantMessage: AssistantMessage = {
      id: messageId,
      role: "assistant",
      content: "",
      toolCalls: [],
    };
    finalMessages.push(assistantMessage);

    return new Observable<BaseEvent>((subscriber) => {
      subscriber.next({
        type: EventType.RUN_STARTED,
        threadId: input.threadId,
        runId: input.runId,
      } as RunStartedEvent);

      this.streamMastraAgent(input, {
        onTextPart: (text) => {
          assistantMessage.content += text;
          const event: TextMessageChunkEvent = {
            type: EventType.TEXT_MESSAGE_CHUNK,
            role: "assistant",
            messageId,
            delta: text,
          };
          subscriber.next(event);
        },
        onToolCallPart: (streamPart) => {
          let toolCall: ToolCall = {
            id: streamPart.toolCallId,
            type: "function",
            function: {
              name: streamPart.toolName,
              arguments: JSON.stringify(streamPart.args),
            },
          };
          assistantMessage.toolCalls!.push(toolCall);

          const startEvent: ToolCallStartEvent = {
            type: EventType.TOOL_CALL_START,
            parentMessageId: messageId,
            toolCallId: streamPart.toolCallId,
            toolCallName: streamPart.toolName,
          };
          subscriber.next(startEvent);

          const argsEvent: ToolCallArgsEvent = {
            type: EventType.TOOL_CALL_ARGS,
            toolCallId: streamPart.toolCallId,
            delta: JSON.stringify(streamPart.args),
          };
          subscriber.next(argsEvent);

          const endEvent: ToolCallEndEvent = {
            type: EventType.TOOL_CALL_END,
            toolCallId: streamPart.toolCallId,
          };
          subscriber.next(endEvent);
        },
        onToolResultPart(streamPart) {
          const toolMessage: ToolMessage = {
            role: "tool",
            id: randomUUID(),
            toolCallId: streamPart.toolCallId,
            content: JSON.stringify(streamPart.result),
          };
          finalMessages.push(toolMessage);
        },
        onFinishMessagePart: () => {
          // Emit message snapshot
          const event: MessagesSnapshotEvent = {
            type: EventType.MESSAGES_SNAPSHOT,
            messages: finalMessages,
          };
          subscriber.next(event);

          // Emit run finished event
          subscriber.next({
            type: EventType.RUN_FINISHED,
            threadId: input.threadId,
            runId: input.runId,
          } as RunFinishedEvent);

          // Complete the observable
          subscriber.complete();
        },
        onError: (error) => {
          console.error("error", error);
          // Handle error
          subscriber.error(error);
        },
      });

      return () => {};
    });
  }

  /**
   * Streams in process or remote mastra agent.
   * @param input - The input for the mastra agent.
   * @param options - The options for the mastra agent.
   * @returns The stream of the mastra agent.
   */
  private streamMastraAgent(
    { threadId, runId, messages, tools }: RunAgentInput,
    {
      onTextPart,
      onFinishMessagePart,
      onToolCallPart,
      onToolResultPart,
      onError,
    }: MastraAgentStreamOptions,
  ) {
    const clientTools = tools.reduce(
      (acc, tool) => {
        acc[tool.name as string] = {
          id: tool.name,
          description: tool.description,
          inputSchema: tool.parameters,
        };
        return acc;
      },
      {} as Record<string, any>,
    );
    const resourceId = this.resourceId ?? threadId;
    const convertedMessages = convertAGUIMessagesToMastra(messages);
    const runtimeContext = this.runtimeContext;

    function isLocalMastraAgent(
      agent: LocalMastraAgent | RemoteMastraAgent,
    ): agent is LocalMastraAgent {
      return "metrics" in agent;
    }

    if (isLocalMastraAgent(this.agent)) {
      // in process agent
      return this.agent
        .stream(convertedMessages, {
          threadId,
          resourceId,
          runId,
          clientTools,
          runtimeContext,
        })
        .then((response) => {
          return processDataStream({
            stream: (response as any).toDataStreamResponse().body!,
            onTextPart,
            onToolCallPart,
            onToolResultPart,
            onFinishMessagePart,
          });
        })
        .catch((error) => {
          onError?.(error);
        });
    } else {
      // remote agent
      return this.agent
        .stream({
          threadId,
          resourceId,
          runId,
          messages: convertedMessages,
          clientTools,
        })
        .then((response) => {
          return response.processDataStream({
            onTextPart,
            onToolCallPart,
            onToolResultPart,
            onFinishMessagePart,
          });
        })
        .catch((error) => {
          onError?.(error);
        });
    }
  }
}

export function convertAGUIMessagesToMastra(messages: Message[]): CoreMessage[] {
  const result: CoreMessage[] = [];

  for (const message of messages) {
    if (message.role === "assistant") {
      const parts: any[] = message.content ? [{ type: "text", text: message.content }] : [];
      for (const toolCall of message.toolCalls ?? []) {
        parts.push({
          type: "tool-call",
          toolCallId: toolCall.id,
          toolName: toolCall.function.name,
          args: JSON.parse(toolCall.function.arguments),
        });
      }
      result.push({
        role: "assistant",
        content: parts,
      });
    } else if (message.role === "user") {
      result.push({
        role: "user",
        content: message.content || "",
      });
    } else if (message.role === "tool") {
      let toolName = "unknown";
      for (const msg of messages) {
        if (msg.role === "assistant") {
          for (const toolCall of msg.toolCalls ?? []) {
            if (toolCall.id === message.toolCallId) {
              toolName = toolCall.function.name;
              break;
            }
          }
        }
      }
      result.push({
        role: "tool",
        content: [
          {
            type: "tool-result",
            toolCallId: message.toolCallId,
            toolName: toolName,
            result: message.content,
          },
        ],
      });
    }
  }

  return result;
}

export function registerCopilotKit<T extends Record<string, any> | unknown = unknown>({
  path,
  resourceId,
  serviceAdapter = new ExperimentalEmptyAdapter(),
  agents,
  setContext,
}: {
  path: string;
  resourceId: string;
  serviceAdapter?: CopilotServiceAdapter;
  agents?: Record<string, AbstractAgent>;
  setContext?: (
    c: Context<{
      Variables: {
        mastra: Mastra;
      };
    }>,
    runtimeContext: RuntimeContext<T>,
  ) => void | Promise<void>;
}) {
  return registerApiRoute(path, {
    method: `ALL`,
    handler: async (c) => {
      const mastra = c.get("mastra");

      const runtimeContext = new RuntimeContext<T>();

      if (setContext) {
        await setContext(c, runtimeContext);
      }

      const aguiAgents =
        agents ||
        MastraAgent.getLocalAgents({
          resourceId,
          mastra,
          runtimeContext,
        });

      const runtime = new CopilotRuntime({
        agents: aguiAgents,
      });

      const handler = copilotRuntimeNodeHttpEndpoint({
        endpoint: path,
        runtime,
        serviceAdapter,
      });

      return handler.handle(c.req.raw, {});
    },
  });
}
