/**
 * Tool-based generative UI agent using Vercel AI SDK.
 * This is equivalent to the LangGraph tool_based_generative_ui agent.
 */

import { VercelAISDKAgent, VercelAISDKAgentConfig } from "../index";
import { RunAgentInput, BaseEvent } from "@ag-ui/client";
import { Observable } from "rxjs";
import { CoreMessage, streamText } from "ai";
import { randomUUID } from "crypto";

// List of available images (same as LangGraph version)
const IMAGE_LIST = [
  "Osaka_Castle_Turret_Stone_Wall_Pine_Trees_Daytime.jpg",
  "Tokyo_Skyline_Night_Tokyo_Tower_Mount_Fuji_View.jpg",
  "Itsukushima_Shrine_Miyajima_Floating_Torii_Gate_Sunset_Long_Exposure.jpg",
  "Takachiho_Gorge_Waterfall_River_Lush_Greenery_Japan.jpg",
  "Bonsai_Tree_Potted_Japanese_Art_Green_Foliage.jpeg",
  "Shirakawa-go_Gassho-zukuri_Thatched_Roof_Village_Aerial_View.jpg",
  "Ginkaku-ji_Silver_Pavilion_Kyoto_Japanese_Garden_Pond_Reflection.jpg",
  "Senso-ji_Temple_Asakusa_Cherry_Blossoms_Kimono_Umbrella.jpg",
  "Cherry_Blossoms_Sakura_Night_View_City_Lights_Japan.jpg",
  "Mount_Fuji_Lake_Reflection_Cherry_Blossoms_Sakura_Spring.jpg",
];

// Haiku generation tool
const GENERATE_HAIKU_TOOL = {
  name: "generate_haiku",
  description:
    "Generate a haiku in Japanese and its English translation. Also select exactly 3 relevant images from the provided list based on the haiku's theme.",
  parameters: {
    type: "object",
    properties: {
      japanese: {
        type: "array",
        items: {
          type: "string",
        },
        description: "An array of three lines of the haiku in Japanese",
      },
      english: {
        type: "array",
        items: {
          type: "string",
        },
        description: "An array of three lines of the haiku in English",
      },
      image_names: {
        type: "array",
        items: {
          type: "string",
        },
        description:
          "An array of EXACTLY THREE image filenames from the provided list that are most relevant to the haiku.",
      },
    },
    required: ["japanese", "english", "image_names"],
  },
};

export interface ToolBasedGenerativeUIAgentConfig extends VercelAISDKAgentConfig {
  // No additional config needed
}

export class ToolBasedGenerativeUIAgent extends VercelAISDKAgent {
  constructor(config: ToolBasedGenerativeUIAgentConfig) {
    super(config);
  }

  protected run(input: RunAgentInput): Observable<BaseEvent> {
    // Add the haiku generation tool to the available tools
    const toolsWithHaiku = [...input.tools, GENERATE_HAIKU_TOOL];

    // Create enhanced input with the haiku tool
    const enhancedInput = {
      ...input,
      tools: toolsWithHaiku,
    };

    // Add system message about available images
    const imageListStr = IMAGE_LIST.map((img) => `- ${img}`).join("\n");
    const systemMessage = `You assist the user in generating a haiku.
When generating a haiku using the 'generate_haiku' tool, you MUST also select exactly 3 image filenames from the following list that are most relevant to the haiku's content or theme. Return the filenames in the 'image_names' parameter.

Available images:
${imageListStr}

Don't provide the relevant image names in your final response to the user.`;

    // We could override the messages to include system message, but for now let's use the base implementation
    return super.run(enhancedInput);
  }
}
