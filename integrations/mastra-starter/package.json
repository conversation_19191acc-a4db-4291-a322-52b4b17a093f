{"name": "@ag-ui/mastra", "version": "0.0.1", "license": "Elastic-2.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "private": false, "publishConfig": {"access": "public"}, "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@ag-ui/client": "workspace:*", "@ai-sdk/ui-utils": "^1.1.19", "@mastra/client-js": "^0.10.1", "hono": "^4.5.1", "rxjs": "7.8.1"}, "peerDependencies": {"@copilotkit/runtime": "^1.8.13", "@mastra/core": "^0.10.1", "zod": "^3.0.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.11.19", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}}