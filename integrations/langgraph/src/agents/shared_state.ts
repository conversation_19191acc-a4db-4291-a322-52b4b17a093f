/**
 * A demo of shared state between the agent and CopilotKit using LangGraph.js.
 */

import { StateGraph, MessagesAnnotation } from "@langchain/langgraph";
import { BaseMessage, SystemMessage, ToolMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { RunnableConfig } from "@langchain/core/runnables";

enum SkillLevel {
  BEGINNER = "Beginner",
  INTERMEDIATE = "Intermediate",
  ADVANCED = "Advanced",
}

enum SpecialPreferences {
  HIGH_PROTEIN = "High Protein",
  LOW_CARB = "Low Carb",
  SPICY = "Spicy",
  BUDGET_FRIENDLY = "Budget-Friendly",
  ONE_POT_MEAL = "One-Pot Meal",
  VEGETARIAN = "Vegetarian",
  VEGAN = "Vegan",
}

enum CookingTime {
  FIVE_MIN = "5 min",
  FIFTEEN_MIN = "15 min",
  THIRTY_MIN = "30 min",
  FORTY_FIVE_MIN = "45 min",
  SIXTY_PLUS_MIN = "60+ min",
}

interface Recipe {
  skill_level: SkillLevel;
  special_preferences: SpecialPreferences[];
  cooking_time: CookingTime;
  ingredients: Array<{
    icon: string;
    name: string;
    amount: string;
  }>;
  instructions: string[];
  changes?: string;
}

const GENERATE_RECIPE_TOOL = {
  type: "function" as const,
  function: {
    name: "generate_recipe",
    description:
      "Using the existing (if any) ingredients and instructions, proceed with the recipe to finish it. Make sure the recipe is complete. ALWAYS provide the entire recipe, not just the changes.",
    parameters: {
      type: "object",
      properties: {
        recipe: {
          type: "object",
          properties: {
            skill_level: {
              type: "string",
              enum: Object.values(SkillLevel),
              description: "The skill level required for the recipe",
            },
            special_preferences: {
              type: "array",
              items: {
                type: "string",
                enum: Object.values(SpecialPreferences),
              },
              description: "A list of special preferences for the recipe",
            },
            cooking_time: {
              type: "string",
              enum: Object.values(CookingTime),
              description: "The cooking time of the recipe",
            },
            ingredients: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  icon: {
                    type: "string",
                    description:
                      "The icon emoji (not emoji code like '\\x1f35e', but the actual emoji like 🥕) of the ingredient",
                  },
                  name: { type: "string" },
                  amount: { type: "string" },
                },
              },
              description:
                "Entire list of ingredients for the recipe, including the new ingredients and the ones that are already in the recipe",
            },
            instructions: {
              type: "array",
              items: { type: "string" },
              description:
                "Entire list of instructions for the recipe, including the new instructions and the ones that are already there",
            },
            changes: {
              type: "string",
              description: "A description of the changes made to the recipe",
            },
          },
        },
      },
      required: ["recipe"],
    },
  },
};

export interface AgentState {
  messages: BaseMessage[];
  recipe?: Recipe;
  tools: any[];
}

export async function startFlow(state: AgentState, config?: RunnableConfig): Promise<Partial<AgentState>> {
  /**
   * This is the entry point for the flow.
   */

  // Initialize recipe if not exists
  let recipe = state.recipe;
  if (!recipe) {
    recipe = {
      skill_level: SkillLevel.BEGINNER,
      special_preferences: [],
      cooking_time: CookingTime.FIFTEEN_MIN,
      ingredients: [{ icon: "🍴", name: "Sample Ingredient", amount: "1 unit" }],
      instructions: ["First step instruction"],
    };
  }

  return {
    messages: state.messages,
    recipe: recipe,
  };
}

export async function chatNode(state: AgentState, config?: RunnableConfig): Promise<Partial<AgentState>> {
  /**
   * Standard chat node.
   */
  // Create a safer serialization of the recipe
  let recipeJson = "No recipe yet";
  if (state.recipe) {
    try {
      recipeJson = JSON.stringify(state.recipe, null, 2);
    } catch (error) {
      recipeJson = `Error serializing recipe: ${error}`;
    }
  }

  const systemPrompt = `You are a helpful assistant for creating recipes. 
    This is the current state of the recipe: ${recipeJson}
    You can improve the recipe by calling the generate_recipe tool.
    
    IMPORTANT:
    1. Create a recipe using the existing ingredients and instructions. Make sure the recipe is complete.
    2. For ingredients, append new ingredients to the existing ones.
    3. For instructions, append new steps to the existing ones.
    4. 'ingredients' is always an array of objects with 'icon', 'name', and 'amount' fields
    5. 'instructions' is always an array of strings

    If you have just created or modified the recipe, just answer in one sentence what you did. dont describe the recipe, just say what you did.
    `;

  // Define the model with custom OpenAI configuration
  const model = new ChatOpenAI({
    model: "gpt-4o-2024-11-20",
    openAIApiKey: "1914304559263223873",
    configuration: {
      baseURL: "https://aigc.sankuai.com/v1/openai/native/",
    },
  });

  // Bind the tools to the model
  const modelWithTools = model.bindTools([...state.tools, GENERATE_RECIPE_TOOL], {
    // Disable parallel tool calls to avoid race conditions
    parallel_tool_calls: false,
  });

  // Run the model and generate a response
  const response = await modelWithTools.invoke([new SystemMessage(systemPrompt), ...state.messages], config);

  // Update messages with the response
  let messages = [...state.messages, response];

  // Handle tool calls
  if (response.tool_calls && response.tool_calls.length > 0) {
    const toolCall = response.tool_calls[0];

    if (toolCall.name === "generate_recipe") {
      // Update recipe state with tool_call_args
      const recipeData = toolCall.args.recipe;

      let recipe: Recipe;
      // If we have an existing recipe, update it
      if (state.recipe) {
        recipe = { ...state.recipe };
        for (const [key, value] of Object.entries(recipeData)) {
          if (value !== null && value !== undefined) {
            (recipe as any)[key] = value;
          }
        }
      } else {
        // Create a new recipe
        recipe = {
          skill_level: recipeData.skill_level || SkillLevel.BEGINNER,
          special_preferences: recipeData.special_preferences || [],
          cooking_time: recipeData.cooking_time || CookingTime.FIFTEEN_MIN,
          ingredients: recipeData.ingredients || [],
          instructions: recipeData.instructions || [],
        };
      }

      // Add tool response to messages
      const toolResponse = new ToolMessage({
        content: "Recipe generated.",
        tool_call_id: toolCall.id!,
      });

      messages = [...messages, toolResponse];

      // Return with updated recipe
      return {
        messages: messages,
        recipe: recipe,
      };
    }
  }

  return {
    messages: messages,
    recipe: state.recipe,
  };
}

// Define the graph
const workflow = new StateGraph(MessagesAnnotation.spec)
  .addNode("start_flow", startFlow)
  .addNode("chat_node", chatNode)
  .addEdge("__start__", "start_flow")
  .addEdge("start_flow", "chat_node")
  .addEdge("chat_node", "__end__");

// Compile the graph
export const sharedStateGraph = workflow.compile();
