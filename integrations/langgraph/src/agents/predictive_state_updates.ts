/**
 * A demo of predictive state updates using LangGraph.js.
 */

import { StateGraph, MessagesAnnotation } from "@langchain/langgraph";
import { BaseMessage, SystemMessage, ToolMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { RunnableConfig } from "@langchain/core/runnables";
import { v4 as uuidv4 } from "uuid";

const WRITE_DOCUMENT_TOOL = {
  type: "function" as const,
  function: {
    name: "write_document",
    description:
      "Write a document. Use markdown formatting to format the document. It's good to format the document extensively so it's easy to read. You can use all kinds of markdown. However, do not use italic or strike-through formatting, it's reserved for another purpose. You MUST write the full document, even when changing only a few words. When making edits to the document, try to make them minimal - do not change every word. Keep stories SHORT!",
    parameters: {
      type: "object",
      properties: {
        document: {
          type: "string",
          description: "The document to write",
        },
      },
    },
  },
};

export interface AgentState {
  messages: BaseMessage[];
  document?: string;
  tools: any[];
}

export async function startFlow(state: AgentState, config?: RunnableConfig): Promise<Partial<AgentState>> {
  /**
   * This is the entry point for the flow.
   */
  return {
    messages: state.messages,
    document: state.document,
  };
}

export async function chatNode(state: AgentState, config?: RunnableConfig): Promise<Partial<AgentState>> {
  /**
   * Standard chat node.
   */

  const systemPrompt = `
    You are a helpful assistant for writing documents. 
    To write the document, you MUST use the write_document tool.
    You MUST write the full document, even when changing only a few words.
    When you wrote the document, DO NOT repeat it as a message. 
    Just briefly summarize the changes you made. 2 sentences max.
    This is the current state of the document: ----\n ${state.document || ""}\n-----
    `;

  // Define the model with custom OpenAI configuration
  const model = new ChatOpenAI({
    model: "gpt-4o-2024-11-20",
    openAIApiKey: "1914304559263223873",
    configuration: {
      baseURL: "https://aigc.sankuai.com/v1/openai/native/",
    },
  });

  // Bind the tools to the model
  const modelWithTools = model.bindTools([...state.tools, WRITE_DOCUMENT_TOOL], {
    // Disable parallel tool calls to avoid race conditions
    parallel_tool_calls: false,
  });

  // Run the model to generate a response
  const response = await modelWithTools.invoke([new SystemMessage(systemPrompt), ...state.messages], config);

  // Update messages with the response
  let messages = [...state.messages, response];

  // Extract any tool calls from the response
  if (response.tool_calls && response.tool_calls.length > 0) {
    const toolCall = response.tool_calls[0];

    if (toolCall.name === "write_document") {
      // Add the tool response to messages
      const toolResponse = new ToolMessage({
        content: "Document written.",
        tool_call_id: toolCall.id!,
      });

      // Add confirmation tool call (simulated)
      const confirmToolCall = {
        role: "assistant" as const,
        content: "",
        tool_calls: [
          {
            id: uuidv4(),
            type: "function" as const,
            function: {
              name: "confirm_changes",
              arguments: "{}",
            },
          },
        ],
      };

      messages = [...messages, toolResponse];

      // Return with updated document
      return {
        messages: messages,
        document: toolCall.args.document,
      };
    }
  }

  // If no tool was called, go to end
  return {
    messages: messages,
    document: state.document,
  };
}

// Define the graph
const workflow = new StateGraph(MessagesAnnotation.spec)
  .addNode("start_flow", startFlow)
  .addNode("chat_node", chatNode)
  .addEdge("__start__", "start_flow")
  .addEdge("start_flow", "chat_node")
  .addEdge("chat_node", "__end__");

// Compile the graph
export const predictiveStateUpdatesGraph = workflow.compile();
