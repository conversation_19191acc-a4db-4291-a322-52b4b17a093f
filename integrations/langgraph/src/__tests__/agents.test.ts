/**
 * Basic tests to verify agents can be imported and initialized
 */

import {
  agenticChatGraph,
  agenticGenerativeUIGraph,
  humanInTheLoopGraph,
  predictiveStateUpdatesGraph,
  sharedStateGraph,
  toolBasedGenerativeUIGraph,
} from "../index";

describe("LangGraph Agents", () => {
  test("should import all agents successfully", () => {
    expect(agenticChatGraph).toBeDefined();
    expect(agenticGenerativeUIGraph).toBeDefined();
    expect(humanInTheLoopGraph).toBeDefined();
    expect(predictiveStateUpdatesGraph).toBeDefined();
    expect(sharedStateGraph).toBeDefined();
    expect(toolBasedGenerativeUIGraph).toBeDefined();
  });

  test("agents should have compile method", () => {
    expect(typeof agenticChatGraph.invoke).toBe("function");
    expect(typeof agenticGenerativeUIGraph.invoke).toBe("function");
    expect(typeof humanInTheLoopGraph.invoke).toBe("function");
    expect(typeof predictiveStateUpdatesGraph.invoke).toBe("function");
    expect(typeof sharedStateGraph.invoke).toBe("function");
    expect(typeof toolBasedGenerativeUIGraph.invoke).toBe("function");
  });

  test("agents should have stream method", () => {
    expect(typeof agenticChatGraph.stream).toBe("function");
    expect(typeof agenticGenerativeUIGraph.stream).toBe("function");
    expect(typeof humanInTheLoopGraph.stream).toBe("function");
    expect(typeof predictiveStateUpdatesGraph.stream).toBe("function");
    expect(typeof sharedStateGraph.stream).toBe("function");
    expect(typeof toolBasedGenerativeUIGraph.stream).toBe("function");
  });
});
