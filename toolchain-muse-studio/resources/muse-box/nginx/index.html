<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Environment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e1e1e 0%, #2d2d30 100%);
            color: #d4d4d4;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            text-align: center;
            padding: 2rem;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #569cd6, #4ec9b0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #808080;
            margin-bottom: 3rem;
        }

        .buttons {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .btn:hover::before {
            transform: translateX(100%);
        }

        .btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border-color: #569cd6;
        }

        .btn-terminal:hover {
            border-color: #4ec9b0;
        }

        .btn-remote:hover {
            border-color: #dcdcaa;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .btn-label {
            font-size: 1.2rem;
            font-weight: 500;
        }

        .features {
            margin-top: 3rem;
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .feature {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem 2rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .feature-title {
            font-weight: bold;
            color: #569cd6;
            margin-bottom: 0.5rem;
        }

        .admin-links {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .admin-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #d4d4d4;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .admin-link:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #569cd6;
            transform: translateY(-2px);
        }

        .admin-icon {
            font-size: 1.2rem;
        }

        .status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
            }
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }

            .btn {
                width: 150px;
                height: 150px;
            }

            .icon {
                font-size: 3rem;
            }

            .features {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <h1>🎵 MUSE BOX</h1>
    <p class="subtitle">完整的在线开发环境，支持 Java、Go、Node.js 等多语言开发</p>

    <div class="buttons">
        <a href="/ide/" class="btn">
            <span class="icon">💻</span>
            <span class="btn-label">VS Code IDE</span>
        </a>
        <a href="/terminal/" class="btn btn-terminal">
            <span class="icon">⚡</span>
            <span class="btn-label">Web Terminal</span>
        </a>
        <a href="/remote/" class="btn btn-remote">
            <span class="icon">🔗</span>
            <span class="btn-label">Remote Service</span>
        </a>
    </div>

    <div class="features">
        <div class="feature">
            <div class="feature-title">开发工具</div>
            <div>Java (8/11/17), Go, Node.js, Python, Maven</div>
        </div>
        <div class="feature">
            <div class="feature-title">开发环境</div>
            <div>SDKMan, Git, SSH</div>
        </div>
        <div class="feature">
            <div class="feature-title">AI 助手</div>
            <div>Cline, Claude-dev, Codex</div>
        </div>
        <div class="feature">
            <div class="feature-title">安全访问</div>
            <div>HTTPS 加密，统一代理</div>
        </div>
    </div>

    <div class="admin-links">
        <a href="/health" class="admin-link">
            <span class="admin-icon">🏥</span>
            <span>健康检查</span>
        </a>
        <a href="/status" class="admin-link">
            <span class="admin-icon">📊</span>
            <span>系统状态</span>
        </a>
    </div>
</div>

<div class="status">
    <span class="status-dot"></span>
    <span>服务运行中</span>
</div>
</body>
</html>
