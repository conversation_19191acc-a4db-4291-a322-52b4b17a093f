#!/bin/bash

# 设置错误时退出
set -e

# 定义变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 获取当前日期时间作为版本标签
TAG=$(date +"%Y%m%d%H%M%S")
IMAGE_NAME="muse-box"
FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"
LATEST_IMAGE_NAME="${IMAGE_NAME}:latest"
TARBALL_NAME="${IMAGE_NAME}-${TAG}.tar.gz"
LATEST_TARBALL_NAME="${IMAGE_NAME}-latest.tar.gz"

# 下载 SSH 密钥
echo "===== 下载 SSH 密钥 ====="
curl -s -o id_rsa https://s3plus.sankuai.com/toolchain-common/muse-studio/id_rsa
curl -s -o id_rsa.pub https://s3plus.sankuai.com/toolchain-common/muse-studio/id_rsa.pub
curl -s -o known_hosts https://s3plus.sankuai.com/toolchain-common/muse-studio/known_hosts

# 检查密钥和配置文件是否下载成功
if [ ! -f "id_rsa" ] || [ ! -f "id_rsa.pub" ]; then
    echo "错误: SSH 密钥下载失败"
    exit 1
fi

if [ ! -f "known_hosts" ]; then
    echo "警告: known_hosts 文件下载失败，将使用不安全的SSH配置"
fi

# 设置正确的权限
chmod 600 id_rsa
chmod 644 id_rsa.pub
if [ -f "known_hosts" ]; then
    chmod 644 known_hosts
fi

echo "SSH 密钥下载完成"

echo "===== 开始构建 Docker 镜像：${FULL_IMAGE_NAME} ====="

# 构建 Docker 镜像
docker build -t "$FULL_IMAGE_NAME" -t "$LATEST_IMAGE_NAME" .

echo "===== Docker 镜像构建完成 ====="

# 将镜像保存为 tar 文件并压缩
echo "===== 开始导出并压缩镜像 ====="
docker save "$FULL_IMAGE_NAME" | gzip > "$TARBALL_NAME"

echo "===== 镜像压缩完成 ====="
echo "镜像已保存为：${TARBALL_NAME}"
echo "镜像大小：$(du -h ${TARBALL_NAME} | cut -f1)"

# 同时压缩 latest 镜像
echo "===== 开始导出并压缩 latest 镜像 ====="
docker save "$LATEST_IMAGE_NAME" | gzip > "$LATEST_TARBALL_NAME"

echo "===== latest 镜像压缩完成 ====="
echo "latest 镜像已保存为：${LATEST_TARBALL_NAME}"
echo "latest 镜像大小：$(du -h ${LATEST_TARBALL_NAME} | cut -f1)"

# 显示构建信息
echo "===== 构建信息 ====="
echo "镜像名称：${FULL_IMAGE_NAME}"
echo "最新标签：${LATEST_IMAGE_NAME}"
echo "压缩文件：${TARBALL_NAME}"
echo "latest 压缩文件：${LATEST_TARBALL_NAME}"

# 清理 SSH 密钥和配置文件
echo "===== 清理 SSH 密钥和配置文件 ====="
rm -f id_rsa id_rsa.pub known_hosts
echo "SSH 密钥和配置文件已清理"

echo "===== 构建过程完成 ====="
