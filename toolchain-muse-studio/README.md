# Muse Studio

基于 Next.js@15 和 React@19 构建的 Muse Studio 应用。

## 功能特点

- ⚛️ 基于Next.js@15和React@19构建
- 🎨 使用tailwindcss和各种Radix UI组件构建的现代UI
- 🔄 支持明暗模式切换
- 🔧 TypeScript确保类型安全
- 📝 ESLint和Prettier保证代码质量
- 🚀 使用PNPM作为包管理器
- 🤖 集成OpenAI SDK，支持AI功能

## 项目结构

```
src/
  ├── app/              # Next.js App Router 路由和页面
  │   ├── (main)/       # 主应用页面
  │   └── (backend)/    # 后端API路由
  ├── components/       # React 组件
  │   └── ui/           # 基础UI组件
  ├── config/           # 配置文件
  │   ├── index.ts      # 配置导出
  │   ├── ai-config.ts  # AI相关配置
  │   └── sso-config.ts # SSO认证配置
  ├── context/          # React Context
  ├── hooks/            # 自定义React Hooks
  ├── lib/              # 工具函数和通用库
  └── types/            # TypeScript类型定义
```

## 配置文件

项目配置文件集中在 `src/config` 目录下:

- `ai-config.ts`: AI模型和API配置
- `sso-config.ts`: SSO认证相关配置
- `index.ts`: 统一导出所有配置

引用配置示例:

```typescript
// 导入配置
import { openaiClient, ssoConfig } from '@/config';
```

## 开始使用

### 前提条件

- Node.js >= 22
- PNPM >= 10.10.0

### 安装

```bash
# 安装依赖
pnpm install
```

### 开发

```bash
# 启动开发服务器
pnpm dev
```

访问Web应用：[http://localhost:8000](http://localhost:8000)

### 构建

```bash
# 构建应用
pnpm build

# 启动生产服务
pnpm start
```

## 依赖管理

### 更新依赖

```bash
# 检查更新（不执行更新）
pnpm update:deps:check

# 更新依赖（交互式）
pnpm update:deps

# 更新特定类型的依赖
pnpm update:deps:patch  # 仅补丁更新
pnpm update:deps:minor  # 仅次要版本更新
pnpm update:deps:major  # 主要版本更新（谨慎使用！）

# 安装更新后的依赖
pnpm update:install
```

## 贡献

- 遵循ESLint和Prettier强制执行的代码风格
- 提交前运行`pnpm lint`确保代码质量
- 已设置提交前钩子自动检查和格式化代码

## Cursor规则

为了提高开发效率和保持代码一致性，我们推荐使用Cursor IDE并遵循以下规则：

### 代码导航规则

- 使用`Cmd+P`（macOS）或`Ctrl+P`（Windows/Linux）快速打开文件
- 使用`Cmd+Shift+F`（macOS）或`Ctrl+Shift+F`（Windows/Linux）全局搜索
- 使用`F12`跳转到定义，`Option+F12`（macOS）或`Alt+F12`（Windows/Linux）查看定义

### AI辅助规则

- 使用`Cmd+K`（macOS）或`Ctrl+K`（Windows/Linux）激活AI助手
- 编写清晰、具体的提示以获得最佳AI帮助
- 需要生成完整功能时，描述需求并提供相关上下文

### 版本控制规则

- 使用Cursor的Git集成进行提交和拉取
- 提交前确保代码通过`pnpm lint`检查
- 遵循项目的提交信息规范

## 部署

### 使用PM2部署

项目配置了PM2的`ecosystem.config.js`文件，可以轻松部署到生产环境：

```bash
# 安装PM2（如果尚未安装）
npm install -g pm2

# 构建应用
pnpm build

# 使用PM2启动应用
pm2 start ecosystem.config.js

# 查看应用状态
pm2 status

# 查看日志
pm2 logs muse-studio

# 重启应用
pm2 restart muse-studio

# 停止应用
pm2 stop muse-studio

# 设置开机自启
pm2 startup
pm2 save
```

PM2配置说明：

- 应用名称：muse-studio
- 运行模式：cluster模式，最多4个实例
- 内存限制：最大2GB，超过时自动重启
- 端口：8000
- 日志文件：./logs/out.log 和 ./logs/error.log
- 自动重启：最多5次尝试

要修改配置，请编辑`ecosystem.config.js`文件。

## 开发指南

[查看完整开发文档](./docs/DEVELOPMENT.md)

