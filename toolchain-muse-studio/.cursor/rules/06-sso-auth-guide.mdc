---
description: SSO单点登录认证和用户管理指南
globs: ["src/lib/sso-config.ts", "src/lib/sso.ts", "src/components/providers/sso-provider.tsx", "src/components/providers/user-provider.tsx", "src/app/sso/**"]
alwaysApply: false
---
# SSO 认证指南

本项目使用SSO（单点登录）进行用户认证，并通过上下文提供者管理用户状态。

## SSO 配置

- [src/lib/sso-config.ts](mdc:src/lib/sso-config.ts): SSO配置文件
  - 包含测试和生产环境的不同配置
  - 定义客户端ID、密钥和API路径
  - 配置登录回调和登出重定向路径
  
```typescript
export const ssoConfig = {
  clientId: process.env.SSO_CLIENT_ID ?? '',
  clientSecret: process.env.SSO_CLIENT_SECRET ?? '',
  apiUrl: process.env.SSO_API_URL ?? 'https://sso-api.example.com',
  callbackUrl: `${process.env.NEXT_PUBLIC_APP_URL}/sso/callback`,
  logoutRedirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/login`,
};
```

## SSO 认证流程

1. 初始化：在 [src/lib/sso.ts](mdc:src/lib/sso.ts) 中初始化SSO客户端
2. 登录：通过 [src/components/providers/sso-provider.tsx](mdc:src/components/providers/sso-provider.tsx) 管理登录状态
3. 回调：使用 [src/app/sso/callback](mdc:src/app/sso/callback) 处理登录成功回调
4. 登出：使用 [src/app/sso/logout](mdc:src/app/sso/logout) 处理登出流程

### 用户认证状态管理

```typescript
// src/components/providers/sso-provider.tsx
const SSOProvider = ({ children }: { children: React.ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [token, setToken] = useState<string | null>(null);
  
  // 初始化，检查本地存储的令牌
  useEffect(() => {
    const storedToken = localStorage.getItem('sso_token');
    if (storedToken) {
      setToken(storedToken);
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, []);
  
  // 提供登录和登出方法
  const login = () => {
    window.location.href = `${ssoConfig.apiUrl}/oauth/authorize?client_id=${ssoConfig.clientId}&redirect_uri=${ssoConfig.callbackUrl}`;
  };
  
  const logout = () => {
    localStorage.removeItem('sso_token');
    setToken(null);
    setIsAuthenticated(false);
    window.location.href = ssoConfig.logoutRedirectUrl;
  };
  
  return (
    <SSOContext.Provider value={{ isAuthenticated, isLoading, token, login, logout }}>
      {children}
    </SSOContext.Provider>
  );
};
```

## 用户数据管理

- [src/components/providers/user-provider.tsx](mdc:src/components/providers/user-provider.tsx): 用户数据提供者
  - 获取和缓存用户信息
  - 提供用户状态（加载、错误等）
  - 在组件间共享用户数据
  
### 用户数据结构

```typescript
type User = {
  id: string;
  name: string;
  loginName: string;
  email: string;
  avatar?: string;
  department?: string;
  role: string;
};
```

### 用户数据获取

```typescript
// src/components/providers/user-provider.tsx
const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const { token, isAuthenticated } = useSSO();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    if (!isAuthenticated || !token) {
      setIsLoading(false);
      return;
    }
    
    const fetchUser = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`${ssoConfig.apiUrl}/api/user`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
        
        const userData = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUser();
  }, [token, isAuthenticated]);
  
  return (
    <UserContext.Provider value={{ user, isLoading, error }}>
      {children}
    </UserContext.Provider>
  );
};
```
  
## 用户界面组件

- [src/components/nav-user.tsx](mdc:src/components/nav-user.tsx): 用户导航组件
  - 显示用户头像和名称
  - 提供登出功能
- [src/components/ui/user-skeleton.tsx](mdc:src/components/ui/user-skeleton.tsx): 用户加载骨架屏

```tsx
// 用户导航组件示例
const NavUser = () => {
  const { user, isLoading } = useUser();
  const { logout } = useSSO();
  
  if (isLoading) {
    return <UserSkeleton />;
  }
  
  if (!user) {
    return null;
  }
  
  return (
    <div className="flex items-center gap-2">
      <div className="relative">
        {user.avatar ? (
          <Avatar src={user.avatar} alt={user.name} />
        ) : (
          <AvatarFallback>
            {user.name.charAt(0).toUpperCase()}
          </AvatarFallback>
        )}
      </div>
      <div className="flex flex-col">
        <span className="text-sm font-medium">{user.loginName}</span>
        <span className="text-xs text-muted-foreground">{user.name}</span>
      </div>
      <Button variant="ghost" size="icon" onClick={logout}>
        <LogoutIcon className="h-4 w-4" />
      </Button>
    </div>
  );
};
```

## 最佳实践

1. 使用 `useSSO()` hook 获取SSO状态和方法
   ```typescript
   const { isAuthenticated, token, login, logout } = useSSO();
   ```

2. 使用 `useUser()` hook 获取用户数据和状态
   ```typescript
   const { user, isLoading, error } = useUser();
   ```

3. 在需要认证的API请求中添加access-token头
   ```typescript
   const response = await fetch('/api/protected-route', {
     headers: {
       Authorization: `Bearer ${token}`
     }
   });
   ```

4. 处理登录失败和Token过期情况
   ```typescript
   if (response.status === 401) {
     // 令牌已过期，重新登录
     logout();
     login();
   }
   ```

5. 使用骨架屏处理加载状态，提升用户体验
   ```tsx
   {isLoading ? <UserSkeleton /> : <UserProfile user={user} />}
   ```

6. 在处理用户头像时提供备用显示方案
   ```tsx
   {user.avatar ? 
     <img src={user.avatar} alt={user.name} /> : 
     <div className="avatar-fallback">{user.name.charAt(0)}</div>}
   ```
