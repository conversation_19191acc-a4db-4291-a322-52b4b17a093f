/**
 * Taze configuration
 * https://github.com/antfu/taze
 */
export default {
    // Exclude packages from being updated (美团内部包不自动更新)
    exclude: ['@bfe/*', '@dp/*', '@fdfe/*', '@gfe/*', '@mtfe/*', '@ss/*'],

    // Override specific package versions (强制指定特定包版本)
    over: {
        openai: '^4.104.0',
        rxjs: '7.8.1'
    },

    // Custom registry for specific packages (美团内部包使用美团源)
    packageOptions: {
        // @bfe 包
        '@bfe/*': {
            registry: 'http://r.npm.sankuai.com'
        },

        // @dp 包
        '@dp/*': {
            registry: 'http://r.npm.sankuai.com'
        },

        // @fdfe 包
        '@fdfe/*': {
            registry: 'http://r.npm.sankuai.com'
        },

        // @gfe 包
        '@gfe/*': {
            registry: 'http://r.npm.sankuai.com'
        },

        // @mtfe 包
        '@mtfe/*': {
            registry: 'http://r.npm.sankuai.com'
        },

        // @ss 包
        '@ss/*': {
            registry: 'http://r.npm.sankuai.com'
        }
    },

    // 默认使用官方源
    registry: 'https://registry.npmjs.org/'
};
