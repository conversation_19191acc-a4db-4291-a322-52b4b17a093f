#!/usr/bin/python3
# coding: utf-8
"""
带AG-UI支持的服务启动脚本
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="LangGraph AG-UI Service",
    description="LangGraph服务，支持AG-UI协议",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 异常处理
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors(), "body": exc.body}
    )

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "LangGraph AG-UI Service"}

# 根路径
@app.get("/")
async def root():
    return {
        "message": "LangGraph AG-UI Service",
        "endpoints": {
            "health": "/health",
            "ag_ui_websocket": "/ws/ag-ui",
            "ag_ui_http": "/ag-ui/run",
            "ag_ui_health": "/ag-ui/health",
            "ag_ui_info": "/ag-ui/info",
            "original_websocket": "/ws/chat"
        }
    }

try:
    # 导入原有的路由
    logger.info("导入原有路由...")
    from apps.endpoints import monitor
    from apps.endpoints.llm import api as llm_api
    
    app.include_router(monitor.router, prefix="/monitor", tags=["monitor"])
    app.include_router(llm_api.router, prefix="/llm", tags=["llm"])
    
    logger.info("✅ 所有路由导入成功")
    
except Exception as e:
    logger.error(f"❌ 路由导入失败: {e}")
    logger.info("尝试仅导入AG-UI路由...")
    
    try:
        # 如果原有路由导入失败，至少导入AG-UI路由
        from apps.endpoints.llm.ag_ui_api import router as ag_ui_router
        app.include_router(ag_ui_router, tags=["ag-ui"])
        logger.info("✅ AG-UI路由导入成功")
    except Exception as e2:
        logger.error(f"❌ AG-UI路由导入也失败: {e2}")

if __name__ == "__main__":
    logger.info("启动LangGraph AG-UI服务...")
    logger.info("服务地址: http://localhost:8080")
    logger.info("AG-UI WebSocket: ws://localhost:8080/ws/ag-ui")
    logger.info("AG-UI HTTP: http://localhost:8080/ag-ui/run")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )