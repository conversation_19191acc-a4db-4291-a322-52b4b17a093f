version: v1
common:
  os: centos7
build:
  tools:
    git:
    python: 3.12.3
    node: 20
    mt_oraclejdk: 8
  cache:
    dirs:
  run:
    workDir: ./
    cmd:
      - sh ./deploy/build.sh
  target:
    distDir: ./
    files:
      - ./
autodeploy:
  os: centos7
  hulkos: centos7
  tools:
    git:
    python: 3.12.3
    node: 20
    mt_oraclejdk: 8
  targetDir: /opt/meituan/tool/projects
  env:
    HOME: /home/<USER>
    USER: sankuai
  run: sh ./deploy/run.sh
  check: sh ./deploy/check.sh
  checkRetry: 100
  checkInterval: 10s