#!/usr/bin/python3
# coding: utf-8

import os
import sys
import datetime
from pathlib import Path
from loguru import logger

BASE_DIR = Path(__file__).resolve().parent.parent

if BASE_DIR.__str__() not in sys.path:
    sys.path.insert(0, BASE_DIR.__str__())

LOG_DIR = os.path.join(BASE_DIR, "logs")

class Rotator:
    def __init__(self, *, size, at):
        now = datetime.datetime.now()

        self._size_limit = size
        self._time_limit = now.replace(hour=at.hour, minute=at.minute, second=at.second)

        if now >= self._time_limit:
            self._time_limit += datetime.timedelta(days=1)

    def should_rotate(self, message, file):
        file.seek(0, 2)
        if file.tell() + len(message) > self._size_limit:
            return True
        if message.record["time"].timestamp() > self._time_limit.timestamp():
            self._time_limit += datetime.timedelta(days=1)
            return True
        return False


# Rotate file if over 500 MB or at midnight every day
rotator = Rotator(size=5e+8, at=datetime.time(0, 0, 0))

fmt = "{time} | {level: <8} | {name: ^15} | {function: ^15} | {line: >3} | {message}"

# logger.add(sys.stderr, colorize=True, format=fmt, level="DEBUG", backtrace=True, diagnose=True)
logger.add(os.path.join(LOG_DIR, "access.log"), format=fmt, level="INFO", rotation=rotator.should_rotate)
logger.add(os.path.join(LOG_DIR, "error.log"), format=fmt, level="ERROR", rotation=rotator.should_rotate)

HEADERS = {
    "Content-Encoding": "gzip",
    "cache-control": "no-cache",
    "content-type": "application/json",
}

ORIGINS = [
    "*",
    "127.0.0.1",
    "10.*",

    "192.168.*",

    "*.sankuai.com",
    "*.meituan.com"
]
