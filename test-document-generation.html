<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文档生成功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 文档生成功能测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于测试修复后的文档生成功能。我们已经修复了以下问题：</p>
        <ul>
            <li>✅ Agent ID匹配问题（从"base_agent"改为"agentic_chat"）</li>
            <li>✅ 添加了详细的调试日志</li>
            <li>✅ 确保API路由正确配置</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>点击下面的按钮打开应用</li>
            <li>在聊天框中输入"文档生成"</li>
            <li>观察是否出现ResponseAborted错误</li>
            <li>检查右侧是否显示文档生成助手状态</li>
        </ol>
        
        <button onclick="openApp()">🚀 打开应用测试</button>
        <button onclick="checkLogs()">📋 查看控制台日志</button>
    </div>

    <div class="test-section">
        <h2>预期结果</h2>
        <div class="status success">
            ✅ 输入"文档生成"后应该看到：
            <ul>
                <li>Base Agent初始化成功</li>
                <li>没有ResponseAborted错误</li>
                <li>右侧显示"文档生成助手"状态</li>
                <li>AI正常响应文档生成请求</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>应用预览</h2>
        <iframe id="appFrame" src="http://localhost:3001" title="AG-UI应用"></iframe>
    </div>

    <script>
        function openApp() {
            window.open('http://localhost:3001', '_blank');
        }

        function checkLogs() {
            alert('请打开浏览器开发者工具（F12）查看控制台日志，或查看终端输出。');
        }

        // 自动刷新iframe以确保最新版本
        setTimeout(() => {
            document.getElementById('appFrame').src = 'http://localhost:3001?' + Date.now();
        }, 1000);
    </script>
</body>
</html>
