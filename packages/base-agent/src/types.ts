export interface AgentConfig {
  name: string;
  description: string;
  version?: string;
  capabilities?: string[];
  /** 语言设置，默认为中文 */
  language?: string;
  /** 系统提示词，用于指定AI的行为和语言偏好 */
  systemPrompt?: string;
}

export interface AgentMetadata extends AgentConfig {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AgentTool {
  name: string;
  description: string;
  parameters: Record<string, unknown>;
  handler: (args: Record<string, unknown>) => Promise<unknown>;
}

export interface AgentContext {
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, unknown>;
}

export interface BaseAgentOptions {
  config: AgentConfig;
  tools?: AgentTool[];
  context?: AgentContext;
}

// 移除 IBaseAgent 接口，直接使用 AbstractAgent
export interface IAgentInterface {
  getConfig(): AgentConfig;
  getTools(): AgentTool[];
}

// 新增的聊天和Artifact相关类型
export interface ArtifactContent {
  id: string;
  type: string;
  title: string;
  content: unknown;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  artifacts?: ArtifactContent[];
  metadata?: Record<string, unknown>;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  agentId: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, unknown>;
}

export interface ArtifactPageProps {
  content: ArtifactContent;
  onContentChange?: (content: ArtifactContent) => void;
  onClose?: () => void;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
}

export interface AgentCapabilities {
  canGenerateArtifacts: boolean;
  supportedArtifactTypes: string[];
  canExecuteCode: boolean;
  canAccessFiles: boolean;
  canUseTools: boolean;
}

// 扩展的Agent接口
export interface ExtendedAgentMetadata {
  id: string;
  name: string;
  description: string;
  version: string;
  capabilities: string[];
  tags?: string[];
  author?: string;
  icon?: string;
  agentCapabilities?: AgentCapabilities;
  keywords?: string[]; // 添加keywords字段用于agent路由
}

// 工具函数
export function createArtifactContent(
  type: string,
  title: string,
  content: unknown,
  metadata?: Record<string, unknown>
): ArtifactContent {
  return {
    id: `artifact-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type,
    title,
    content,
    metadata: metadata || {},
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

export function createChatMessage(
  role: 'user' | 'assistant' | 'system',
  content: string,
  artifacts?: ArtifactContent[]
): ChatMessage {
  return {
    id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    role,
    content,
    timestamp: new Date(),
    artifacts: artifacts || [],
    metadata: {}
  };
}

export function createChatSession(agentId: string, title?: string): ChatSession {
  return {
    id: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    title: title || `Chat ${new Date().toLocaleString()}`,
    messages: [],
    agentId,
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {}
  };
}
