"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Progress } from "@workspace/ui/components/progress";
import {
  <PERSON>ltip,
  TooltipContent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import {
  ClockIcon,
  UserIcon,
  TagIcon,
  TrendingUpIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  InfoIcon,
  ThumbsUpIcon,
  ThumbsDownIcon,
  MessageSquareIcon,
} from "lucide-react";
import { Artifact } from "../types";
import { ArtifactFooterProps } from "../types";
import { cn, formatTime, formatRelativeTime } from "../utils";

export function ArtifactFooter({
  artifact,
  onAction,
  className,
}: ArtifactFooterProps) {
  const [feedback, setFeedback] = useState<"up" | "down" | null>(null);

  const handleFeedback = (type: "up" | "down") => {
    setFeedback(type);
    onAction?.(type === "up" ? "like" : "dislike");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "complete":
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case "in_progress":
        return <TrendingUpIcon className="w-4 h-4 text-blue-500" />;
      case "error":
        return <AlertCircleIcon className="w-4 h-4 text-red-500" />;
      default:
        return <InfoIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  const getProgressValue = (status: string) => {
    switch (status) {
      case "complete":
        return 100;
      case "in_progress":
        return 60;
      case "error":
        return 0;
      default:
        return 30;
    }
  };

  return (
    <TooltipProvider>
      <div className={cn("border-t border-border bg-muted/30", className)}>
        {/* Progress bar for in-progress artifacts */}
        {artifact.status === "in_progress" && (
          <div className="px-4 pt-2">
            <Progress value={getProgressValue(artifact.status)} className="h-1" />
          </div>
        )}

        <div className="flex items-center justify-between p-4">
          {/* Left side - Status and metadata */}
          <div className="flex items-center space-x-4">
            {/* Status */}
            <div className="flex items-center space-x-2">
              {getStatusIcon(artifact.status)}
              <span className="text-sm text-muted-foreground capitalize">
                {artifact.status.replace("_", " ")}
              </span>
            </div>

            {/* Timestamps */}
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center space-x-1">
                    <ClockIcon className="w-3 h-3" />
                    <span>Created {formatRelativeTime(artifact.createdAt)}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{formatTime(artifact.createdAt)}</p>
                </TooltipContent>
              </Tooltip>

              {artifact.updatedAt.getTime() !== artifact.createdAt.getTime() && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-1">
                      <span>•</span>
                      <span>Updated {formatRelativeTime(artifact.updatedAt)}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{formatTime(artifact.updatedAt)}</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>

            {/* Tags */}
            {artifact.metadata?.tags && Array.isArray(artifact.metadata.tags) && (
              <div className="flex items-center space-x-2">
                <TagIcon className="w-3 h-3 text-muted-foreground" />
                <div className="flex space-x-1">
                  {artifact.metadata.tags.slice(0, 3).map((tag: string, index: number) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {artifact.metadata.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{artifact.metadata.tags.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Author */}
            {artifact.metadata?.author && (
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <UserIcon className="w-3 h-3" />
                <span>{artifact.metadata.author}</span>
              </div>
            )}
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-2">
            {/* Feedback buttons */}
            <div className="flex items-center space-x-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFeedback("up")}
                    className={cn(
                      "h-8 w-8 p-0",
                      feedback === "up" && "text-green-600 bg-green-50 dark:bg-green-950"
                    )}
                  >
                    <ThumbsUpIcon className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>This is helpful</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFeedback("down")}
                    className={cn(
                      "h-8 w-8 p-0",
                      feedback === "down" && "text-red-600 bg-red-50 dark:bg-red-950"
                    )}
                  >
                    <ThumbsDownIcon className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>This needs improvement</p>
                </TooltipContent>
              </Tooltip>
            </div>

            {/* Comment button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onAction?.("comment")}
                  className="h-8 w-8 p-0"
                >
                  <MessageSquareIcon className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Add comment</p>
              </TooltipContent>
            </Tooltip>

            {/* Action buttons based on status */}
            {artifact.status === "error" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAction?.("retry")}
              >
                Retry
              </Button>
            )}

            {artifact.status === "draft" && (
              <Button
                variant="default"
                size="sm"
                onClick={() => onAction?.("complete")}
              >
                Complete
              </Button>
            )}

            {artifact.status === "complete" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAction?.("edit")}
              >
                Edit
              </Button>
            )}
          </div>
        </div>

        {/* Additional info bar for specific artifact types */}
        {artifact.type === "code" && artifact.metadata?.language && (
          <div className="px-4 pb-2">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center space-x-4">
                <span>Language: {artifact.metadata.language}</span>
                {artifact.metadata.lines && (
                  <span>Lines: {artifact.metadata.lines}</span>
                )}
                {artifact.metadata.size && (
                  <span>Size: {artifact.metadata.size}</span>
                )}
              </div>
              {artifact.metadata.lastRun && (
                <span>Last run: {formatRelativeTime(new Date(artifact.metadata.lastRun))}</span>
              )}
            </div>
          </div>
        )}

        {artifact.type === "chart" && artifact.metadata?.dataPoints && (
          <div className="px-4 pb-2">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Data points: {artifact.metadata.dataPoints}</span>
              {artifact.metadata.chartType && (
                <span>Type: {artifact.metadata.chartType}</span>
              )}
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}

// Compact footer for preview mode
export function ArtifactFooterCompact({
  artifact,
  onAction,
  className,
}: ArtifactFooterProps) {
  return (
    <div className={cn("flex items-center justify-between p-2 border-t border-border bg-muted/20", className)}>
      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
        {getStatusIcon(artifact.status)}
        <span className="capitalize">{artifact.status}</span>
        <span>•</span>
        <span>{formatRelativeTime(artifact.updatedAt)}</span>
      </div>
      <div className="flex items-center space-x-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onAction?.("expand")}
          className="h-6 px-2 text-xs"
        >
          View
        </Button>
      </div>
    </div>
  );
}

function getStatusIcon(status: string) {
  switch (status) {
    case "complete":
      return <CheckCircleIcon className="w-3 h-3 text-green-500" />;
    case "in_progress":
      return <TrendingUpIcon className="w-3 h-3 text-blue-500" />;
    case "error":
      return <AlertCircleIcon className="w-3 h-3 text-red-500" />;
    default:
      return <InfoIcon className="w-3 h-3 text-gray-500" />;
  }
}

export default ArtifactFooter;
