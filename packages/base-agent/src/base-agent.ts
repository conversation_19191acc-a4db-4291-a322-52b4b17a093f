import { AbstractAgent } from '@ag-ui/client';
import { RunAgentInput, BaseEvent, EventType } from '@ag-ui/core';
import { Observable, Subject } from 'rxjs';
import { createOpenAI } from '@ai-sdk/openai';
import { streamText, type CoreMessage } from 'ai';
import * as React from 'react';
import {
    IAgentInterface,
    AgentConfig,
    AgentTool,
    BaseAgentOptions,
    ArtifactContent,
    ChatMessage,
    ArtifactPageProps,
    AgentCapabilities,
    ExtendedAgentMetadata
} from './types';

// 定义具体的事件类型
interface RunStartedEvent extends BaseEvent {
    type: EventType.RUN_STARTED;
    threadId: string;
    runId: string;
    timestamp: number;
}

interface TextMessageStartEvent extends BaseEvent {
    type: EventType.TEXT_MESSAGE_START;
    messageId: string;
    timestamp: number;
}

interface TextMessageContentEvent extends BaseEvent {
    type: EventType.TEXT_MESSAGE_CONTENT;
    messageId: string;
    delta: string;
    timestamp: number;
}

interface TextMessageEndEvent extends BaseEvent {
    type: EventType.TEXT_MESSAGE_END;
    messageId: string;
    timestamp: number;
}

interface RunFinishedEvent extends BaseEvent {
    type: EventType.RUN_FINISHED;
    threadId: string;
    runId: string;
    timestamp: number;
}

interface RunErrorEvent extends BaseEvent {
    type: EventType.RUN_ERROR;
    threadId: string;
    runId: string;
    error: string;
    timestamp: number;
}

interface StateSnapshotEvent extends BaseEvent {
    type: EventType.STATE_SNAPSHOT;
    state: Record<string, unknown>;
    timestamp: number;
}

// 自定义 Artifact 事件类型（不扩展 BaseEvent 以避免类型冲突）
interface ArtifactContentEvent {
    type: 'ARTIFACT_CONTENT';
    messageId: string;
    artifact: {
        kind: string;
        title: string;
        content: string;
        metadata?: Record<string, unknown>;
    };
    timestamp: number;
}

export abstract class BaseAgent extends AbstractAgent implements IAgentInterface {
    protected config: AgentConfig;
    protected tools: AgentTool[];
    protected openaiProvider: ReturnType<typeof createOpenAI>;

    // 扩展属性
    public readonly metadata: ExtendedAgentMetadata;
    public readonly capabilities: AgentCapabilities;

    constructor(
        options: BaseAgentOptions & {
            metadata: ExtendedAgentMetadata;
            capabilities: AgentCapabilities;
        }
    ) {
        super();
        
        // 为所有Agent设置默认的中文配置
        this.config = {
            language: 'zh-CN',
            systemPrompt: '请始终使用简体中文回复，无论用户使用什么语言提问。所有回复内容、解释、代码注释都必须使用中文。',
            ...options.config
        };
        
        this.tools = options.tools || [];
        this.metadata = options.metadata;
        this.capabilities = options.capabilities;

        // 正确初始化 OpenAI provider
        this.openaiProvider = createOpenAI({
            apiKey: process.env.OPENAI_API_KEY || '1914304559263223873',
            baseURL: process.env.OPENAI_BASE_URL || 'https://aigc.sankuai.com/v1/openai/native/'
        });
    }

    getConfig(): AgentConfig {
        return this.config;
    }

    getTools(): AgentTool[] {
        return this.tools;
    }

    public run(input: RunAgentInput): Observable<BaseEvent> {
        const subject = new Subject<BaseEvent>();
        this.executeAgent(input, subject);
        return subject.asObservable();
    }

    // 新增方法：支持传入subject的流式执行
    public async runWithSubject(input: RunAgentInput, subject: Subject<BaseEvent>): Promise<void> {
        await this.executeAgent(input, subject);
    }

    private async executeAgent(input: RunAgentInput, subject: Subject<BaseEvent>) {
        try {
            // 发送开始事件
            subject.next({
                type: EventType.RUN_STARTED,
                threadId: input.threadId,
                runId: input.runId,
                timestamp: Date.now()
            } as RunStartedEvent);

            // 生成消息ID
            const messageId = `msg_${Date.now()}`;

            // 发送消息开始事件
            subject.next({
                type: EventType.TEXT_MESSAGE_START,
                messageId,
                timestamp: Date.now()
            } as TextMessageStartEvent);

            // 调用具体的 agent 实现
            await this.processRequest(input, subject, messageId);

            // 发送消息结束事件
            subject.next({
                type: EventType.TEXT_MESSAGE_END,
                messageId,
                timestamp: Date.now()
            } as TextMessageEndEvent);

            // 发送运行完成事件
            subject.next({
                type: EventType.RUN_FINISHED,
                threadId: input.threadId,
                runId: input.runId,
                timestamp: Date.now()
            } as RunFinishedEvent);

            subject.complete();
        } catch (error) {
            subject.next({
                type: EventType.RUN_ERROR,
                threadId: input.threadId,
                runId: input.runId,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: Date.now()
            } as RunErrorEvent);
            subject.error(error);
        }
    }

    // 子类需要实现的抽象方法
    protected abstract processRequest(
        input: RunAgentInput,
        subject: Subject<BaseEvent>,
        messageId: string
    ): Promise<void>;

    // 辅助方法：发送文本内容
    protected sendTextContent(subject: Subject<BaseEvent>, messageId: string, content: string) {
        subject.next({
            type: EventType.TEXT_MESSAGE_CONTENT,
            messageId,
            delta: content,
            timestamp: Date.now()
        } as TextMessageContentEvent);
    }

    // 辅助方法：发送 artifact 内容
    protected sendArtifactContent(
        subject: Subject<BaseEvent>,
        messageId: string,
        artifact: {
            type: string;
            title: string;
            content: string;
            metadata?: Record<string, unknown>;
        }
    ) {
        const artifactEvent: ArtifactContentEvent = {
            type: 'ARTIFACT_CONTENT',
            messageId,
            artifact: {
                kind: artifact.type,
                title: artifact.title,
                content: artifact.content,
                metadata: artifact.metadata
            },
            timestamp: Date.now()
        };

        // 使用类型断言将自定义事件发送到 subject
        subject.next(artifactEvent as any);
    }

    // 辅助方法：发送状态快照
    protected sendStateSnapshot(subject: Subject<BaseEvent>, state: Record<string, unknown>) {
        subject.next({
            type: EventType.STATE_SNAPSHOT,
            state,
            timestamp: Date.now()
        } as StateSnapshotEvent);
    }

    // 辅助方法：使用 OpenAI 生成流式响应
    protected async streamOpenAIResponse(messages: CoreMessage[], subject: Subject<BaseEvent>, messageId: string) {
        const result = await streamText({
            model: this.openaiProvider(process.env.OPENAI_MODEL || 'gpt-4o-2024-11-20'),
            messages,
            temperature: 0.7
        });

        for await (const chunk of result.textStream) {
            this.sendTextContent(subject, messageId, chunk);
        }
    }

    // 扩展方法：处理聊天消息（子类可以重写）
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async processMessage(_message: string, _context?: Record<string, unknown>): Promise<ChatMessage> {
        // 默认实现，子类应该重写
        throw new Error('processMessage method must be implemented by subclass');
    }

    // 扩展方法：生成 Artifact（子类可以重写）
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async generateArtifact(_prompt: string, _type: string): Promise<ArtifactContent> {
        // 默认实现，子类应该重写
        throw new Error('generateArtifact method must be implemented by subclass');
    }

    // 扩展方法：渲染 Artifact 页面（子类必须实现）
    abstract renderArtifactPage(props: ArtifactPageProps): React.ComponentType<ArtifactPageProps>;

    // 验证 Artifact 类型
    validateArtifactType(type: string): boolean {
        return this.capabilities.supportedArtifactTypes.includes(type);
    }

    // 初始化方法（子类可以重写）
    async initialize(): Promise<void> {
        // 默认初始化 - 可以被重写
        console.log(`Initializing agent: ${this.metadata.name} v${this.metadata.version}`);
    }

    // 清理方法（子类可以重写）
    async cleanup(): Promise<void> {
        // 默认清理 - 可以被重写
        console.log(`Cleaning up agent: ${this.metadata.name}`);
    }
}
