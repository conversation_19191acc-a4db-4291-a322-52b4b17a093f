// 通用的 Agent 相关类型定义
import { AgentConfig } from '@ag-ui/client';
import { ComponentType } from 'react';

// 基础 Agent 配置接口，扩展自 AG-UI 的 AgentConfig
export interface BaseAgentConfig extends AgentConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  metadata?: Record<string, any>;
}

// 简化的工具接口
export interface AgentTool {
  id: string;
  name: string;
  description: string;
  execute: (params: any) => Promise<any>;
}

// 通用的 Workspace 属性接口
export interface BaseWorkspaceProps {
  isAgentActive: boolean;
  setIsAgentActive: (active: boolean) => void;
  lastMessage?: string;
}

// Agent 状态类型
export interface AgentState {
  status: 'idle' | 'thinking' | 'executing' | 'completed' | 'error';
  currentStep?: string;
  progress?: number;
  result?: any;
  error?: string;
}

// Integration 包接口 - 每个 integration 包都需要实现这个接口
export interface IntegrationPackage {
  id: string;
  name: string;
  description: string;
  version: string;
  createAgent: (config?: any) => any; // 创建 AG-UI Agent 实例的工厂函数
  workspace: ComponentType<BaseWorkspaceProps>;
  defaultConfig: BaseAgentConfig;
  tools?: AgentTool[];
}

// 兼容性：保留原有的 AgentPackage 接口
export interface AgentPackage extends IntegrationPackage {}

// Integration 注册信息
export interface IntegrationInfo {
  id: string;
  name: string;
  description: string;
  version: string;
  package: IntegrationPackage;
}

// Integration 类型枚举
export enum IntegrationType {
  AGENT = 'agent',
  TOOL = 'tool',
  SERVICE = 'service'
}

// Integration 元数据
export interface IntegrationMetadata {
  id: string;
  name: string;
  description: string;
  version: string;
  type: IntegrationType;
  tags?: string[];
  author?: string;
  license?: string;
  repository?: string;
}
