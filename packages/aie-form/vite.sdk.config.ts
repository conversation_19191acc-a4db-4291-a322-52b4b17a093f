import react from '@vitejs/plugin-react';
import autoprefixer from 'autoprefixer';
import path from 'path';
import tailwindcss from 'tailwindcss';
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';

// useDevMode 开启时与热更新插件冲突,使用变量切换
const useDevMode = process.env.NODE_ENV === 'development';

export default defineConfig({
  plugins: [
    !useDevMode && react(),
    dts({
      rollupTypes: true,
    }),
  ],
  build: {
    outDir: 'dist',
    sourcemap: true,
    lib: {
      entry: path.resolve(__dirname, 'src/entry.tsx'),
      name: 'AieForm',
      fileName: (format) => `index.${format}.js`,
      formats: ['es'],
    },
    rollupOptions: {
      external: [
        'react', 
        'react-dom', 
        'antd'
      ],
    },
  },
  css: {
    modules: {
      //
    },
    preprocessorOptions: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
      less: {
        javascriptEnabled: true,
      },
    },
  },
  resolve: {
    alias: {
      '@api': path.resolve(__dirname, 'src/api'),
      '@views': path.resolve(__dirname, 'src/views'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@assets': path.resolve(__dirname, 'src/assets'),
    },
  },
});
