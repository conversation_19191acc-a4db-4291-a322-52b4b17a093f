import React from 'react';
import { Tooltip } from 'antd';
import '../../index.css';

interface IProps {
  data: any;
}

const ElementTooltip: React.FC<IProps> = (props: IProps): any => {
  const { data } = props;
  // const { id, name, namePathList = [], locator } = data || {};
  const { id, name, locator } = data || {};
  const title = () => (<div className='text-black text-xs'>
    <div className=''>
      <img
        className='w-[auto] rounded-md'
        src={'https://s3plus.sankuai.com/rpa-bucket/20240429/%E5%85%AC%E4%BC%97%E5%8F%B7logo4%2520(1)%2520(1).png'}
        alt={name}
      />
    </div>
    <div className='leading-[24px]'>
      <b>{name}</b>
      <div className='flex'>
        <div className='w-[40px]'>ID: </div>
        <div className='pl-4'>{id}</div>
      </div>
      <div className='flex'>
        <div className='w-[40px]'>归属: </div>
        {/* <div className='pl-4'>{namePathList?.[namePathList?.length - 1]}</div> */}
      </div>
      <div className='flex'>
        <div className='w-[40px]'>xpath</div>
        <div className='pl-4'>{locator?.locator}</div>
      </div>
    </div>
  </div>)
  return (<Tooltip
    placement='right'
    destroyTooltipOnHide
    autoAdjustOverflow
    arrow={false}
    title={title}
    trigger={'click'}
    overlayStyle={{ width: '600px' }}
    overlayInnerStyle={{ background: '#fff', width: '400px' }}
    fresh
  >
    <div className='w-[120px] h-[120px] mx-1 mb-1 p-2.5' 
      style={{ border: '2px dashed lightgray' }}
    >
      <img
        className='h-[100px] w-[100px] rounded-md'
        src={'https://s3plus.sankuai.com/rpa-bucket/20240429/%E5%85%AC%E4%BC%97%E5%8F%B7logo4%2520(1)%2520(1).png'}
        alt={name}
      />
    </div>
  </Tooltip>)
};

export default ElementTooltip;
