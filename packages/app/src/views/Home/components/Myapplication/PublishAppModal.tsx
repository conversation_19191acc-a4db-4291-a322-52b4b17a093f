import React from "react";
import { Form, Modal, Input, message } from "antd";
import { pageReleaseApi } from "@api/homeApi/index";
const { TextArea } = Input;

interface PublishAppModalProps {
  releaseVersionOpen: boolean;
  setReleaseVersionOpen: (open: boolean) => void;
  applicationId: string | null;
  version: string | null;
  ActionSuccess: () => void;
  spaceId?: string | null;
}

const PublishAppModal: React.FC<PublishAppModalProps> = ({
  releaseVersionOpen,
  setReleaseVersionOpen,
  applicationId,
  version,
  ActionSuccess,
  spaceId
}) => {
  const [form] = Form.useForm();

  const releaseVersionOk = async () => {
    try {
      const values = await form.validateFields();
      const { publishedLog } = values;
      if (applicationId) {
      const res = await pageReleaseApi({
          applicationId,
          publishedLog,
          version,
          spaceId
        });
        if(res.data.code === 0){
          message.success("发布成功");
          setReleaseVersionOpen(false);
          ActionSuccess();
        }else{
          message.error(res.data.msg || "发布失败");
        }
      }
    } catch (error) {
      console.error("Failed to release version:", error);
      message.error("发布失败");
    }
  };
  const releaseVersionCancel = () => {
    setReleaseVersionOpen(false);
    // form.resetFields()
  };

  return (
    <Modal
      title="发布版本"
      open={releaseVersionOpen}
      onOk={releaseVersionOk}
      onCancel={releaseVersionCancel}
      afterClose={() => form.resetFields()} 
      okText="确定"
      cancelText="取消"
    >
      <div className="mb-4 w-50 h-[35px] bg-blue-50 border border-blue-300 p-2 rounded flex items-center">
        <i className="aieicon aieicon-information-fill text-[#166FF7] cursor-pointer " />
        &ensp;
        <span>发版后，已获取此应用的用户也会同步更新</span>
      </div>
      <Form form={form}>
        <Form.Item label="当前版本" name="版本号">
          <p>{version}</p>
        </Form.Item>
        <Form.Item
          name="publishedLog"
          label="更新日志"
          rules={[{ max: 100, message: "日志不能超过100个字符" }]}
        >
          <TextArea allowClear showCount maxLength={100} placeholder="请输入" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PublishAppModal;
