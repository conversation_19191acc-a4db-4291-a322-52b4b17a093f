import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, message, TreeSelect } from 'antd';
import { CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import { getDepartmentTree, getInviteLinkList, getInviteLink,deleteInviteLink } from '@api/personSpaceApi';

interface InviteMemberModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
  spaceId: string | null;
}

interface DepartmentOption {
  value: string;
  title: string;
  children?: DepartmentOption[];
}

interface InviteLink {
  id: string;
  invite_token: string;
  name: string;
  dep_id: string;
}

const InviteMemberModal: React.FC<InviteMemberModalProps> = ({
  visible,
  onCancel,
  onOk,
  spaceId
}) => {
  const [selectedDepartment, setSelectedDepartment] = useState<string>();
  const [departmentOptions, setDepartmentOptions] = useState<DepartmentOption[]>([]);
  const [inviteLinks, setInviteLinks] = useState<InviteLink[]>([]);

  useEffect(() => {
    if (visible && spaceId) {
      fetchDepartmentTree();
      fetchInviteLinks();
    }
  }, [visible, spaceId]);

  const fetchDepartmentTree = async () => {
    try {
      const response = await getDepartmentTree({ spaceId });
      if (response.data.code === 0) {
        const options = formatDepartmentTree(response.data.data);
        setDepartmentOptions(options);
      } 
    } catch (error) {
      console.error('获取部门列表失败:', error);
      message.error('获取部门列表失败');
    }
  };

  const formatDepartmentTree = (data: any[]): DepartmentOption[] => {
    return data.map(item => ({
      value: item.id,
      title: item.name,
      children: item.children ? formatDepartmentTree(item.children) : undefined
    }));
  };

  const fetchInviteLinks = async () => {
    try {
      const response = await getInviteLinkList({ spaceId });
      if (response.data.code === 0) {
        setInviteLinks(response.data.data);
      }else{
        message.error(response.data.msg || '链接有误');
      }
    } catch (error) {
      console.error('获取邀请链接列表失败:', error);
      message.error('获取邀请链接列表失败');
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success('复制成功');
  };

  const handleCreateLink = async () => {
    if (!selectedDepartment) {
      message.warning('请选择部门');
      return;
    }
    try {
      const response = await getInviteLink({
        spaceId,
        depId: selectedDepartment
      });

      if (response.data.code === 0) {
        message.success('创建成功');
        fetchInviteLinks(); // 刷新链接列表
        setSelectedDepartment(undefined); // 清空选择
      } else {
        message.error(response.data.msg || '邀请链接有误');
      }
    } catch (error) {
      console.error('创建邀请链接失败:', error);
      message.error('创建失败');
    }
  };

  const handleDeleteLink = async (token: string) => {
    try {
      const response = await deleteInviteLink({
        spaceId,
        token
      });

      if (response.data.code === 0) {
        message.success('删除成功');
        fetchInviteLinks(); // 刷新列表
      } else {
        message.error(response.data.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除邀请链接失败:', error);
      message.error('删除失败');
    }
  };

  return (
    <Modal
      title='邀请成员'
      open={visible}
      footer={null}
      onCancel={onCancel}
      width={800}
      className="top-10"
    >
      <div className="py-4">
        <div className="mb-8">
          <div className="flex items-center mb-2">
            <span className="text-gray-700 mr-4 text-sm w-[180px]">创建公开邀请链接：</span>
            <TreeSelect
              placeholder="请选择部门"
              value={selectedDepartment}
              onChange={setSelectedDepartment}
              className='w-full'
              treeData={departmentOptions}
              showSearch
              treeDefaultExpandAll
              allowClear
              filterTreeNode={(inputValue, treeNode) => {
                return (treeNode?.title as string)
                  .toLowerCase()
                  .includes(inputValue.toLowerCase());
              }}
              treeNodeFilterProp="title"
            />
            <Button type="primary" onClick={handleCreateLink}>创建</Button> 
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg p-6 bg-white">
          <div className="text-base font-medium mb-6 text-gray-800">已创建的邀请链接</div>
          <div className="space-y-5">
            {inviteLinks.map((item) => (
              <div key={item.id} className="pb-5 border-b border-gray-100 last:border-b-0 last:pb-0">
                <div className="text-sm text-gray-700 mb-2">{item.name}</div>
                <div className="flex items-center">
                  <Input
                    value={`${window.location.origin}/app/invite?token=${item.invite_token}`}
                    readOnly
                    className="flex-1 bg-gray-50 border-gray-200 text-sm"
                    size="middle"
                  />
                  <div className="flex ml-3 space-x-5">
                    <button 
                      className="text-gray-400 hover:text-gray-600 cursor-pointer p-1"
                      onClick={() => handleCopy(`${window.location.origin}/app/invite?token=${item.invite_token}`)}
                    >
                      <CopyOutlined className="text-sm" />
                    </button>
                    <button 
                      className="text-gray-400 hover:text-gray-600 cursor-pointer p-1"
                      onClick={() => handleDeleteLink(item.invite_token)}
                    >
                      <DeleteOutlined className="text-sm" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end mt-8 space-x-4">
          <Button
            onClick={onCancel}
            className="!rounded-button whitespace-nowrap px-6 h-9 text-sm"
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={onOk}
            className="!rounded-button whitespace-nowrap px-6 h-9 text-sm"
          >
            确定
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default InviteMemberModal; 
