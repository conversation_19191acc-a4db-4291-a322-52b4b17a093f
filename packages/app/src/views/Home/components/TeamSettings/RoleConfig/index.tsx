import React, { useState, useEffect } from 'react';
import { But<PERSON>, Tabs, Dropdown, Modal, Form, message, Typography } from 'antd';
import type { MenuProps } from 'antd';
import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import { getRoleList, addRole, updateRoleName, deleteRole, updateRolePermission, copyRole,updateRolePermissionChange, getRoleMemberList } from '@api/personSpaceApi';
import AddRoleModal from './components/AddRoleModal';
import EditRoleModal from './components/EditRoleModal';
import PermissionTable from './components/PermissionTable';
import type { PermissionData } from './types';

interface RoleConfigProps {
  spaceId: string | null;
  isAdmin: boolean;
}

interface RoleItem {
  id: string;
  role_display_name: string;
}

const RoleConfig: React.FC<RoleConfigProps> = ({ spaceId, isAdmin }) => {
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('');
  const [roles, setRoles] = useState<RoleItem[]>([]);
  const [currentRole, setCurrentRole] = useState<RoleItem | null>(null);
  const [permissions, setPermissions] = useState<PermissionData | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  useEffect(() => {
    if (spaceId) {
      fetchRoles();
    }
  }, [spaceId]);

  useEffect(() => {
    if (spaceId && activeTab) {
      fetchRolePermissions();
    }
  }, [spaceId, activeTab]);

  const fetchRoles = async (selectNewRole?: string) => {
    try {
      const response = await getRoleList({ spaceId });
      
      if (response.data.code === 0) {
        setRoles(response.data.data);
        if (selectNewRole) {
          setActiveTab(selectNewRole);
        } 
        else if (!activeTab && response.data.data.length > 0) {
          setActiveTab(response.data.data[0].id);
        }
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
    }
  };

  const fetchRolePermissions = async () => {
    try {
      const response = await updateRolePermission({
        spaceId,
        roleId: activeTab
      });
      
      setPermissions(response.data.data);
      
      const selectedIds = Object.values(response.data.data).reduce<string[]>((acc, items: any[]) => {
        const ids = items.reduce<string[]>((itemAcc, item) => {
          if (item.children) {
            const childIds = item.children
              .filter(child => child.rolePermission_Id)
              .map(child => child.id);
            return [...itemAcc, ...childIds];
          }
          return itemAcc;
        }, []);
        return [...acc, ...ids];
      }, []);
      
      setSelectedPermissions(selectedIds);
    } catch (error) {
      console.error('获取角色权限失败:', error);
    }
  };

  const handleAddRole = async () => {
    try {
      const values = await form.validateFields();
      const res = await addRole({
        spaceId,
        role_display_name: values.role_display_name
      });
      if(res.data.code === 0) {
        setAddModalVisible(false);
        message.success('添加角色成功');
        form.resetFields();
        const response = await getRoleList({ spaceId });
        if (response.data.code === 0) {
          const newRole = response.data.data.find(
            role => role.role_display_name === values.role_display_name
          );
          if (newRole) {
            fetchRoles(newRole.id);
          }
        }
      } else {
        message.error(res.data.msg || '添加失败');
      }    
    } catch (error) {
      console.error('添加角色失败:', error);
    }
  };

  const handleEditRole = async () => {
    try {
      const values = await editForm.validateFields();
      const res = await updateRoleName({
        spaceId,
        id: currentRole?.id,
        role_display_name: values.role_display_name
      });
      if(res.data.code === 0){
        message.success('角色名称更新成功');
        setEditModalVisible(false);
        editForm.resetFields();
        fetchRoles();
      }else{
        message.error(res.data.msg || '更新失败');
      }
    } catch (error) {
      console.error('更新角色失败:', error);
      message.error('更新失败');
    }
  };

  const handleDeleteRole = (roleId: string, roleName: string) => {
    const checkAndDelete = async () => {
      try {
        const memberResponse = await getRoleMemberList({
          spaceId,
          // type: 'user',
          roleId: roleId,
        });
        console.log(memberResponse.data.data,'memberResponse.data.data');
        if (memberResponse.data.code === 0) {
          const memberCount = memberResponse.data.data.total;
          console.log(memberResponse.data.data);
          Modal.confirm({
            title: (
              <div className="w-[350px] flex text-[15px]">
                <Typography.Text ellipsis={true}>
                  确定删除"{roleName}"吗?
                </Typography.Text>
              </div>
            ),
            okText: "删除",
            cancelText: "取消",
            icon: null,
            okButtonProps: {
              danger: true,
            },
            content: memberCount > 0 
              ? `此角色当前有${memberCount}个成员，删除角色后，成员将一起删除，无法打开该空间，请谨慎操作！`
              : `删除后，该角色无法恢复`,
            onOk: async () => {
              try {
                const res = await deleteRole({ spaceId, roleId });
                if (res.data.code === 0) {
                  message.success('删除成功');
                  fetchRoles('');
                } else {
                  message.error(res.data.msg || '删除失败');
                }
              } catch (error) {
                console.error('删除角色失败:', error);
                message.error('删除失败');
              }
            }
          });
        }
      } catch (error) {
        console.error('检查角色成员失败:', error);
        message.error('检查角色成员失败');
      }
    };

    checkAndDelete();
  };

  const handleCopyRole = async (roleId: string) => {
    try {
     const res = await copyRole({
        spaceId,
        id: roleId
      });
      if(res.data.code === 0){
        message.success('复制角色成功');
        fetchRoles();
      }else{
        message.error(res.data.msg);
      }
    } catch (error) {
      console.error('复制角色失败:', error);
      message.error('复制失败');
    }
  };

  const handleMenuClick = (key: string, role: RoleItem) => {
    switch (key) {
      case '1':
        setCurrentRole(role);
        editForm.setFieldsValue({ role_display_name: role.role_display_name });
        setEditModalVisible(true);
        break;
      case '2':
        handleCopyRole(role.id);
        break;
      case '3':
        handleDeleteRole(role.id, role.role_display_name);
        break;
    }
  };

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: '编辑',
    },
    {
      key: '2',
      label: '复制',
    },
    {
      key: '3',
      label: '删除',
      danger: true,
    },
  ];

  
  const TabLabel = ({ label, role }: { label: string; role: RoleItem }) => (
    <div className="flex items-center justify-center gap-2 relative group">
      {label}
      {isAdmin && !isSpecialRole(role.role_display_name) && (
        <Dropdown 
          menu={{ 
            items,
            onClick: ({ key }) => handleMenuClick(key, role)
          }} 
          placement="bottomRight" 
          trigger={['click']}
        >
          <Button
            type="text"
            icon={<EllipsisOutlined />}
            size="small"
            className="!rounded-button min-w-0 p-0 hover:bg-transparent opacity-0 group-hover:opacity-100 transition-opacity"
          />
        </Dropdown>
      )}
    </div>
  );

  const handleObjectCheck = (category: string, checked: boolean) => {
    const newSelectedPermissions = [...selectedPermissions];
    
    const categoryPermissions = (permissions?.[category as keyof PermissionData] as any[])?.reduce((acc: string[], item) => {
      if (item.children) {
        return [...acc, item.id, ...item.children.map(child => child.id)];
      }
      return [...acc, item.id];
    }, []) || [];

    if (checked) {
      categoryPermissions.forEach(id => {
        if (!newSelectedPermissions.includes(id)) {
          newSelectedPermissions.push(id);
        }
      });
    } else {
      const filtered = newSelectedPermissions.filter(
        id => !categoryPermissions.includes(id)
      );
      setSelectedPermissions(filtered);
      return;
    }

    setSelectedPermissions(newSelectedPermissions);
  };

  const handlePermissionCheck = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId]);
    } else {
      setSelectedPermissions(selectedPermissions.filter(id => id !== permissionId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allPermissions = Object.values(permissions || {}).reduce((acc, items) => {
        items.forEach(item => {
          if (item.children) {
            acc.push(item.id, ...item.children.map(child => child.id));
          } else {
            acc.push(item.id);
          }
        });
        return acc;
      }, [] as string[]);
      
      setSelectedPermissions(allPermissions);
    } else {
      setSelectedPermissions([]);
    }
  };  

  const handleSave = async () => {
    try {
     const res = await updateRolePermissionChange({
        roleId: activeTab,
        permissionIds: selectedPermissions
      });
      if(res.data.code === 0){
        message.success('保存成功');
      }else{
        message.error(res.data.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const isSpecialRole = (roleName: string) => {
    return ['管理员', '成员'].includes(roleName);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <p className="text-[16px] font-bold leading-loose ml-5">角色配置</p>
      </div>
      <div className="bg-white">
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex gap-4">
              <Tabs
                defaultActiveKey={roles[0]?.id}
                activeKey={activeTab}
                onChange={(key) => setActiveTab(key)}
                items={roles.map(role => ({
                  label: <TabLabel label={role.role_display_name} role={role} />,
                  key: role.id,
                }))}
                className="custom-tabs"
              />
              {isAdmin && (
                <Button
                  type="text"
                  icon={<PlusOutlined />}
                  className="!rounded-button text-blue-600 text-blue-700 mt-[10px]"
                  onClick={() => setAddModalVisible(true)}
                >
                  角色
                </Button>
              )}
            </div>
            {!isSpecialRole(roles.find(r => r.id === activeTab)?.role_display_name || '') && (
              <Button 
                type="primary" 
                onClick={handleSave}
                disabled={!isAdmin}
              >
                保存
              </Button>
            )}
          </div>
        </div>
        <div className="px-6 py-4">
          <PermissionTable
            permissions={permissions}
            selectedPermissions={selectedPermissions}
            onPermissionChange={handlePermissionCheck}
            onCategoryChange={handleObjectCheck}
            onSelectAll={handleSelectAll}
            isAdmin={isAdmin && !isSpecialRole(roles.find(r => r.id === activeTab)?.role_display_name || '')}
          />
        </div>
      </div>

      <AddRoleModal
        visible={addModalVisible}
        onOk={handleAddRole}
        onCancel={() => {
          setAddModalVisible(false);
          form.resetFields();
        }}
        form={form}
      />

      <EditRoleModal
        visible={editModalVisible}
        onOk={handleEditRole}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        form={editForm}
      />
    </div>
  );
};

export default RoleConfig; 
