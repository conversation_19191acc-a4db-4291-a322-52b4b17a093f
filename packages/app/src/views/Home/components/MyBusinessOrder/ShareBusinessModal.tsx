import React,{useEffect} from "react";
import { Modal, Form, Select, message } from "antd";
import Api from "../../../../api/businessRpa";
import { DebounceSelect, fetchUserList } from "../../common/newSelect";
import { getPersonSpaceList } from "@api/personSpaceApi";
interface ShareBusinessModalProps {
  shareModal: boolean;
  setShareModal: (open: boolean) => void;
  currentId: number;
  ActionBusinessData: () => void;
  spaceId:string;
}
interface UserValue {
  label: string;
  value: string;
}
const ShareBusinessModal: React.FC<ShareBusinessModalProps> = ({
  shareModal,
  setShareModal,
  currentId,
  ActionBusinessData,
  spaceId
}) => {
  const [spaceList, setSpaceList] = React.useState<any>([]);
  const [value, setValue] = React.useState<UserValue[]>([]);
  const [form] = Form.useForm();
  const fetchSpaceList = async () => {
    try {
      const response = await getPersonSpaceList({title:'',permissionCode:'' ,excludeSelf:true,excludeSpaceId:spaceId });
      if (response.data.code === 0) {
        const list = response.data?.data?.list?.map(item => ({
          value: item.id,
          label: item.title
        }));
        setSpaceList(list);
      }
    } catch (error) {
      console.error('获取工作空间列表失败:', error);
    }
  };
  const handleShareSubmit = async () => {
    try {
      const values = await form.validateFields();
      const { data: res } = await Api.commandShare({
        relevanceId: currentId,
        person: value.map((user) => user.value),
        team: values.team || [],
        spaceId: spaceId
      });

      if (res.code === 0) {
        message.success("分享成功");
        setShareModal(false);
        ActionBusinessData();
      } else {
        message.error(res.msg || "分享失败");
      }
    } catch (error) {
      console.error("分享失败:", error);
    }
  };
  const handleCancel = () => {
    setShareModal(false);
    form.resetFields();
    setValue([]);
  };
  const handleSpaceChange = (values: string[]) => {
    form.setFieldsValue({ team: values });
  };
  useEffect(() => {
    if (shareModal) {
      fetchSpaceList();
    }
  }, [shareModal]);
  return (
    <Modal
      title="分享"
      open={shareModal}
      onOk={handleShareSubmit}
      onCancel={handleCancel}
      okText="确定"
      cancelText="取消"
      okButtonProps={{ type: null }}
      destroyOnClose={true}
      afterClose={() => {
        form.resetFields();
        setValue([]);
      }}
    >
      <Form 
        form={form}
        preserve={false}
      >
        <Form.Item label="分享至个人:" name="person">
          <DebounceSelect
            mode="multiple"
            maxTagCount="responsive"
            value={value}
            placeholder="请输入员工mis号"
            fetchOptions={fetchUserList}
            onChange={(newValue) => {
              setValue(newValue as UserValue[]);
            }}
            style={{ width: "100%" }}
            allowClear
          />
        </Form.Item>
        <Form.Item name="team" label="分享至团队空间:">
          <Select
            mode="multiple"
            showSearch
            allowClear
            options={spaceList}
            placeholder="请选择团队空间"
            onChange={handleSpaceChange}
            filterOption={(input, option) => 
              typeof option?.label === 'string' ? 
                option.label.toLowerCase().includes(input.toLowerCase()) : false
            }
            optionFilterProp="label"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ShareBusinessModal;
