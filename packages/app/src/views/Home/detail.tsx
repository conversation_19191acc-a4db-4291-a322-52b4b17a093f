import React, { useState, useEffect, useCallback } from "react";
import { Layout, Splitter } from "antd";
import {
  PushpinOutlined,
  ExportOutlined,
  ExpandAltOutlined,
  LinkOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import RenderHeader from "./header";
import Element from "../Element";
import Variable from "../Variable"
import "./style.less";
import { Device } from "./device";

const {  Content } = Layout;


const IconButton = ({ icon, ...props }) => (
  <button className="p-1 hover:bg-gray-100 rounded" {...props}>
    {React.createElement(icon)}
  </button>
);

const FloatingPanel = ({ title, children, onClose, position, setPosition }) => {
  const handleDragStart = useCallback(
    (e) => {
      const startPosition = { x: e.clientX, y: e.clientY };
      const startOffset = { ...position };

      const handleDrag = (e) => {
        setPosition({
          x: startOffset.x + e.clientX - startPosition.x,
          y: startOffset.y + e.clientY - startPosition.y,
        });
      };

      const handleDragEnd = () => {
        document.removeEventListener("mousemove", handleDrag);
        document.removeEventListener("mouseup", handleDragEnd);
      };

      document.addEventListener("mousemove", handleDrag);
      document.addEventListener("mouseup", handleDragEnd);
    },
    [position, setPosition]
  );

  return (
    <div
      className="floating-window w-[340px] h-[770px] overflow-hidden"
      style={{ left: position.x, top: position.y }}
    >
      <div className="p-4 h-full">
        <div
          className="floating-window-header flex justify-between items-center mb-4"
          onMouseDown={handleDragStart}
        >
          <span>{title}</span>
          <IconButton icon={PushpinOutlined} onClick={onClose} />
        </div>
        {children}
      </div>
    </div>
  );
};

const useWindowResize = (callback) => {
  useEffect(() => {
    window.addEventListener("resize", callback);
    return () => window.removeEventListener("resize", callback);
  }, [callback]);
};

const App = () => {
  const [panels, setPanels] = useState({
    left: { floating: false, position: { x: 20, y: 20 } },
    right: { floating: false, position: { x: window.innerWidth - 276, y: 20 } },
  });
  const [activeLeftPanel, setActiveLeftPanel] = useState("元素");
  const [showLeftPanel, setShowLeftPanel] = useState(true);

  const updatePanelState = useCallback((side, updates) => {
    setPanels((prev) => ({ ...prev, [side]: { ...prev[side], ...updates } }));
  }, []);

  useWindowResize(
    useCallback(() => {
      updatePanelState("right", {
        position: { ...panels.right.position, x: window.innerWidth - 276 },
      });
    }, [panels.right.position, updatePanelState])
  );

  

  const renderSplitterPanel = (side, content, defaultSize) => (
    <Splitter.Panel collapsible={false} defaultSize={defaultSize}>
      <Content className="bg-gray-100 h-full p-2">
        <div className="bg-white border border-gray-200 rounded-lg h-full flex flex-col relative">
          {side === "center" && (
            <div className="absolute top-2 left-2 flex space-x-2">
              {["指令", "元素", "变量"].map((item) => (
                <button
                  key={item}
                  className={`px-3 py-1 rounded text-sm ${
                    showLeftPanel && activeLeftPanel === item
                      ? "bg-blue-500 text-white"
                      : "bg-gray-200 text-gray-700"
                  }`}
                  onClick={() => {
                    setActiveLeftPanel(item);
                    setShowLeftPanel(true);
                  }}
                >
                  {item}
                </button>
              ))}
            </div>
          )}
          {side === "left" && (
            <div className="absolute top-2 right-2 flex space-x-2">
              <IconButton
                icon={CloseOutlined}
                onClick={() => {
                  setShowLeftPanel(false);
                  setActiveLeftPanel("");
                }}
              />
              <IconButton
                icon={ExportOutlined}
                onClick={() => updatePanelState(side, { floating: true })}
              />
            </div>
          )}
          {side === "right" && (
            <div className="absolute top-2 right-2">
              <IconButton
                icon={ExportOutlined}
                onClick={() => updatePanelState(side, { floating: true })}
              />
            </div>
          )}
          <div className={`flex-1 flex items-center justify-center ${side === 'right' ? '' : 'pt-6'}`}>
            {content}
          </div>
        </div>
      </Content>
    </Splitter.Panel>
  );

  const renderFloatingPanel = (side, title, content) => (
    <FloatingPanel
      onClose={() => updatePanelState(side, { floating: false })}
      title={title}
      position={panels[side].position}
      setPosition={(newPosition) =>
        updatePanelState(side, { position: newPosition })
      }
    >
      {content}
    </FloatingPanel>
  );

  const renderLeftPanelContent = () => {
    const contents = {
      指令: <div>指令</div>,
      元素: <Element></Element>,
      变量: <Variable></Variable>,
    };
    return contents[activeLeftPanel] || null;
  };

  return (
    <Layout className="h-screen">
      <RenderHeader></RenderHeader>
      <Splitter
        className="flex-1"
        style={{ boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)" }}
      >
        {showLeftPanel &&
          !panels.left.floating &&
          renderSplitterPanel("left", renderLeftPanelContent(), 25)}
        {renderSplitterPanel(
          "center",
          "编辑器",
          showLeftPanel && !panels.left.floating ? 50 : 75
        )}
        {!panels.right.floating && renderSplitterPanel("right", <Device/>, 25)}
      </Splitter>
      {panels.left.floating &&
        renderFloatingPanel("left", activeLeftPanel, renderLeftPanelContent())}
      {panels.right.floating &&
        renderFloatingPanel(
          "right",
          "设备控制",
          <div className="flex justify-between items-center mb-4">
            <span>设备控制</span>
            <div className="flex space-x-2">
              <IconButton icon={ExpandAltOutlined} />
              <IconButton icon={LinkOutlined} />
            </div>
          </div>
        )}
    </Layout>
  );
};

export default App;
