{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 8899", "build:test": "tsc -b && vite build --mode test", "build:staging": "tsc -b && vite build --mode staging", "lint": "npx tsc -b && eslint . --quiet", "preview": "vite preview", "build:webstatic": "webstatic build --appkey=com.sankuai.waimaiqafc.aie --env=test --build-command='npm run build:test' --token=************************************", "deploy:webstatic": "webstatic deploy --appkey=com.sankuai.waimaiqafc.aie --artifact=build --env=test --token=************************************"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@eslint/js": "^9.14.0", "@hello-pangea/dnd": "^17.0.0", "@waimai/waimai-qa-aie-utils": "^1.1.6", "aie-form": "file:../aie-form", "antd": "^5.22.5", "eslint": "^9.14.0", "lodash": "^4.17.21", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "^6.19.0", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.6", "@types/lodash": "^4.17.12", "@types/node": "^22.8.7", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "less": "^4.2.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "typescript-eslint": "^8.0.0", "vite": "^5.4.10", "vite-plugin-global": "^0.0.1", "vite-plugin-qiankun": "^1.0.15"}, "postcss": {"plugins": {"tailwindcss": {}, "autoprefixer": {}}}, "browserslist": ["last 2 Chrome versions", "last 2 Firefox versions", "last 2 Safari versions", "last 2 Edge versions"]}