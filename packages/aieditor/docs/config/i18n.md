# Internationalized configuration

## Samples

```typescript
new AiEditor({
    element: "#aiEditor",
    lang: "zh",
    i18n: {
        zh :{
            "undo": "撤销（自定义）",
            "redo": "重做（自定义...）",
        },
        en:{
            "undo": "Undo",
            "redo": "Redo",
        }
    }
})
```


- **lang**：The language setting used by the editor.
- **i18n**： custom internationalization configuration.

::: warning
AiEditor has built-in internationalization content for `zh` (Chinese) and `en` (English). You only need to configure the `lang` attribute. However, you can override the built-in content using the `i18n` configuration or add additional languages.
:::

## Add support for other languages


```typescript
new AiEditor({
    element: "#aiEditor",
    lang: "otherLang",
    i18n: {
        otherLang :{
            "undo": "xxx",
            "redo": "xxx",
        },
    }
})
```

## 切换语言

```typescript
const aiEditor = new AiEditor({
    element: "#aiEditor",
    lang: "zh",
    i18n: {
        zh :{
            "undo": "撤销（自定义）",
            "redo": "重做（自定义...）",
        },
        en:{
            "undo": "Undo",
            "redo": "Redo",
        }
    }
})

//Switch the current language to English.
aiEditor.changeLang("en")
```

