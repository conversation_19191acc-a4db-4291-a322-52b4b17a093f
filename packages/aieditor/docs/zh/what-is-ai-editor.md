# AiEditor 是什么


## 简介

> AiEditor 是一个面向 AI 的下一代富文本编辑器，它基于 Web Component 开发，因此支持 Layui、Vue、React、Angular、Svelte 等几乎任何前端框架。她适配了 PC Web 端和手机端，并提供了 亮色 和 暗色 两个主题。除此之外，她还提供了灵活的配置，开发者可以方便的使用其开发任何文字编辑的应用。


## 为什么要开发 AiEditor

现在，AI 时代已到，AIGC 正在蓬勃发展。

在 2023 年，我们开始为自己的产品选择合适的 AI 富文本编辑器，我们找到了 CKEditor、TinyMCE、和 Tiptap 等优秀的产品。但是，他们或多或少的存在了以下一些问题：

- CKEditor 和 TinyMCE 都是基于 GPL 开源协议，开源协议并不友好。
- Tiptap 是一个无头编辑器，在使用的时候需要基于其做许多的额外开发工作。

**最为主要的是：**
> 在使用 CKEditor、TinyMCE、和 Tiptap 等编辑器的 AI 功能时，必须使用他们的 **_付费_** 插件，以及 AI 的云服务。在这种情况下，我们基于其开发的应用就回面临许多限制，
> 比如：无法私有化部署，无法使用私有的大模型 ApiKey 等。

因此，所以我才决定开发了 AiEditor，用于解决以上问题。


## AiEditor 的定位

1. 我们开发 AiEditor 的初衷，本身是为了解决 AI 编辑问题的。所以，在 AI 方面，AiEditor 支持使用私有的 ApiKey 对接任何的大模型，包括 ChatGPT、讯飞星火、文心一言以及任何的私有化大模型。
2. 我们希望 AiEditor 拥有更多的使用场景，不受限于任何的 UI 渲染框架，比如 Vue、React、Angular、Svelte 等。 因此我们是基于 Web Component 开发的，它可以很好的和任何框架集成。
3. 我们提供了友好的 UI 页面，支持亮色和暗色两个主题，支持使用 Markdown 的书写习惯、支持灵活的功能配置以及自定义布局，以及使用了比 CKEditor 和 TinyMCE 更加友好的开源协议 LGPL。
4. 除此之外，我们会继续学习优秀的产品，比如 Notion 等，为大家提供一些列的好用的 AI 功能... 当然，目前 AiEditor 还在持续进化中，我们需要你的支持。




## 开源

经过一段时间的开发，AiEditor 终于发布了第一个开源版本，而且它已经有了一个编辑器该有的基本功能，比如：

| 功能            | 描述                                                                    |
|---------------|-----------------------------------------------------------------------|
| **基础功能**      | 标题、正文、字体、字号、加粗、斜体、下划线、删除线、链接、行内代码、上标、下标、分割线、引用、打印                     |
| **增强功能**      | 撤回、重做、格式刷、橡皮擦（清除格式）、待办事项、字体颜色、背景颜色、Emoji 表情、对齐方式、行高、有（无）序列表、段落缩进、强制换行 |
| **附件功能**      | 支持图片、视频、文件功能，支持选择上传、粘贴上传、拖拽上传、支持拖动调整大小...                             |
| **代码功能**      | 行内代码、代码块、语言类型选择、**AI 自动注释**、**AI 代码解释**...                            |
| **Markdown**  | 标题、引用、表格、图片、代码块、**高亮块（类似 vuepress 的 :::）**、各种列表、粗体、斜体、删除线...          |
| **AI 功能**     | AI 续写、AI 优化、AI 校对、AI 翻译、自定义 AI 菜单及其 Prompts                           |
| **更多功能**      | 国际化、亮色主题、暗色主题、手机版适配、全屏编辑、@某某某（提及）...                                  |

接下来，我们还会推出一系列功能，比如：

* 团队协作
* AI 插入图片
* AI 图生图（AI 图片优化）
* AI 一键排版
* 进一步强化粘贴功能
* 上传视频自动获取缩略图
* WORD 导入、导出
* PDF 导出、PDF 预览
* 类 Notion 拖拽功能
* 等等


让我们一起见证，一个更好的文本编辑器。

### 开源地址
- Gitee: https://gitee.com/aieditor-team/aieditor
- Github: https://github.com/aieditor-team/aieditor
