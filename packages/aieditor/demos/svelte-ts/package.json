{"name": "svelte-ts", "version": "0.0.1", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@fontsource/fira-mono": "^5.0.0", "@neoconfetti/svelte": "^2.0.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "typescript": "^5.0.0", "vite": "^5.0.3"}, "type": "module", "dependencies": {"aieditor": "^1.0.13"}}