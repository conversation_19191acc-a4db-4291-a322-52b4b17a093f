import { AbstractDropdownMenuButton } from "../AbstractDropdownMenuButton.ts";
import { Editor, EditorEvents } from "@tiptap/core";
import { AiEditorOptions } from "../../core/AiEditor.ts";
import { AiMenu } from "../../ai/AiGlobalConfig.ts";
import { t } from "i18next";
import { usePlanSave } from "../../../../ai-case/src/api/index.ts"
import { getUrlParams } from '../../../../utils/url.ts';
import { getUserInfo } from '../../../../utils/localStorage.ts';

export const dafaultAiMenus: AiMenu[] = [
  {
    icon: ``,
    name: `ai-optimization`,
    prompt: "",
    text: "selected",
    model: "auto",
  },
]

async function handleContentSave(params: any) {
  const res = await usePlanSave(params)
  console.log('save res-->', res)
}
// @ts-ignore
export class GenerateStep extends AbstractDropdownMenuButton<AiMenu> {
  // onMenuTextRender(index: number): Element | string {
  //   // throw new Error('Method not implemented.');
  // }
  // onDropdownItemRender(index: number): Element | string {
  //   // throw new Error('Method not implemented.');
  // }
  aiMenus = dafaultAiMenus.map((menu) => {
    return {
      ...menu,
      name: `${t(menu.name)}`
    }
  });

  constructor() {
    super();
    this.dropDivHeight = "auto"
    this.dropDivWith = "fit-content"
    this.width = "86px"
    this.menuTextWidth = "20px"
  }

  onCreate(_: EditorEvents["create"], options: AiEditorOptions) {
    super.onCreate(_, options);
    this.menuData = options.ai?.menus || this.aiMenus;
  }

  renderTemplate() {
    this.addEventListener('click', () => {
      this.onDropdownItemClick()
    })
    this.template = `
         <div style="width: ${this.width};">
         <div id="tippy" class="menu-ai" id="text" style="width: 86px">
             <span> 重新生成步骤 </span>
         </div>
         </div>
        `
  }


  // createMenuElement() {

  // }

  onTransaction(_: EditorEvents["transaction"]) {
    //do nothing
  }

  onDropdownActive(_editor: Editor, _index: number): boolean {
    return false;
  }

  

  onDropdownItemClick(): void {
    // const aiMenu = this.menuData[index];
    // @ts-ignore 获取当前editor 
    const editor = this.editor?.aiEditor
    const content = editor.getMarkdown()
    const { id } = getUrlParams()

    console.log('content-->', content)
    const params = {
      mis: getUserInfo()?.email,
      content: content,
      designTaskId: id
    }
    handleContentSave(params)
  }

  // onDropdownItemRender(index: number): Element | string {
  //   return `<div style="width:18px;height: 18px;">${this.menuData[index].icon}</div><div>${this.menuData[index].name}</div>`;
  // }

  // onMenuTextRender(index: number): Element | string {
  //   return this.menuData[index].icon;
  // }

}

