import {AbstractMenuButton} from "../AbstractMenuButton.ts";
import {Editor} from "@tiptap/core";

export class BulletList extends AbstractMenuButton {
    constructor() {
        super();
        this.template = `
        <div>
             <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M8 4H21V6H8V4ZM3 3.5H6V6.5H3V3.5ZM3 10.5H6V13.5H3V10.5ZM3 17.5H6V20.5H3V17.5ZM8 11H21V13H8V11ZM8 18H21V20H8V18Z"></path></svg>
        </div>
        `;
        this.registerClickListener();
    }

    // @ts-ignore
    onClick(commands) {
        commands.toggleBulletList();
    }

    onActive(editor: Editor): boolean {
        return editor.isActive("bulletList")
    }


}


