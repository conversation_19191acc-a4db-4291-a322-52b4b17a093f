export const content = `
# 测试方案11

## 需求范围

### 渠道、版本
- 外卖APP（v8.12，9.13全量）
- 美团APP（12.14.200，9.21全量）

## 相关页面

### 新增页面
无

### 变更页面

#### 搭售订单详情页

##### 状态&去向

##### 待支付

##### vp订单
- 改了布局，把“取消订单”和“立即支付”按钮上移了

##### ap订单
- 状态注释：随单购买的兑换券需同商品订单一起支付
- 按钮：前往商品订单支付
- 点击跳转外卖订单详情页（闪购业务跳转闪购订单详情页）

##### 订单已取消

##### vp订单
- 无改动

##### ap订单
- 按钮：随单购买的兑换券已同商品订单同步取消
- 点击跳转外卖订单详情页（闪购业务跳转闪购订单详情页）

##### 去向浮层

##### 退款记录

##### 使用记录

##### 核销

##### 门店地址

##### 去使用按钮

##### 售后

##### 申请退款按钮

##### 过期退能力

##### 退款浮层

##### 小问号弹窗

##### 其他

##### 再次购买

##### 订单信息

##### 下拉刷新

##### 跳链&格式

##### 极端场景

#### 兼容页面

##### 购买记录页

##### 我的券列表

##### 外卖订单列表页

#### 券类型

##### 品牌会员券

## 专项测试

### 机型系统兼容测试

#### TOP3系统均需覆盖主链路

### 性能测试

#### 无需特别关注

### 多渠道测试

#### 多端覆盖主链路

### 回归测试

#### 回归已有弹窗的核心逻辑

### 实验测试

#### 无实验跑数

`;

/**
 * JSON 格式
 */
// const content = `
// {"type":"doc","content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"AiEditor 是一个面向 AI 的下一代富文本编辑器。"}]},{"type":"paragraph","attrs":{"lineHeight":"100%","textAlign":"left","indent":0},"content":[{"type":"text","marks":[{"type":"bold"}],"text":"提示："},{"type":"text","text":" "}]},{"type":"orderedList","attrs":{"tight":true,"start":1},"content":[{"type":"listItem","attrs":{"indent":0},"content":[{"type":"paragraph","attrs":{"lineHeight":"100%","textAlign":"left","indent":0},"content":[{"type":"text","text":"输入 空格 + \\"/\\" 可以快速弹出 AI 菜单 "}]}]},{"type":"listItem","attrs":{"indent":0},"content":[{"type":"paragraph","attrs":{"lineHeight":"100%","textAlign":"left","indent":0},"content":[{"type":"text","text":"输入 空格 + \\"@\\" 可以提及某人"}]}]}]},{"type":"paragraph","attrs":{"lineHeight":"100%","textAlign":"left","indent":0}},{"type":"paragraph","attrs":{"lineHeight":"100%","textAlign":"left","indent":0},"content":[{"type":"text","text":"请使用 Java 帮我写一个 hello world，只需要返回 java 代码内容"}]},{"type":"codeBlock","attrs":{"language":"java"},"content":[{"type":"text","text":"public class HelloWorld {\\n    public static void main(String[] args) {\\n        System.out.println(\\"Hello, World!\\");\\n    }\\n}"}]},{"type":"paragraph","attrs":{"lineHeight":"100%","textAlign":"left","indent":0}}]}
// `

/**
 * Markdown 格式
 */
// const content = `
// # 一级标题
// ## 二级标题
// - 这是详情内容
// `;
