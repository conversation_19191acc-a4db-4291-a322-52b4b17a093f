{"name": "aieditor", "private": true, "author": "yang<PERSON><PERSON>", "version": "1.1.2", "type": "module", "keywords": ["editor", "ai", "ai editor"], "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./dist/style.css": {"import": "./dist/style.css", "require": "./dist/style.css"}}, "files": ["dist", "LICENSE", "README.md"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^20.9.0", "less": "^4.2.0", "typescript": "^5.0.2", "vite": "^5.4.6", "vite-plugin-dts": "^3.6.3"}, "dependencies": {"@tiptap/core": "^2.7.1", "@tiptap/extension-bubble-menu": "^2.7.1", "@tiptap/extension-character-count": "^2.7.1", "@tiptap/extension-code-block-lowlight": "^2.7.1", "@tiptap/extension-color": "^2.7.1", "@tiptap/extension-font-family": "^2.7.1", "@tiptap/extension-gapcursor": "^2.7.1", "@tiptap/extension-heading": "^2.7.1", "@tiptap/extension-highlight": "^2.7.1", "@tiptap/extension-image": "^2.7.1", "@tiptap/extension-link": "^2.7.1", "@tiptap/extension-mention": "^2.7.1", "@tiptap/extension-placeholder": "^2.7.1", "@tiptap/extension-subscript": "^2.7.1", "@tiptap/extension-superscript": "^2.7.1", "@tiptap/extension-table": "^2.7.1", "@tiptap/extension-table-cell": "^2.7.1", "@tiptap/extension-table-header": "^2.7.1", "@tiptap/extension-table-row": "^2.7.1", "@tiptap/extension-task-item": "^2.7.1", "@tiptap/extension-task-list": "^2.7.1", "@tiptap/extension-text-align": "^2.7.1", "@tiptap/extension-text-style": "^2.7.1", "@tiptap/extension-underline": "^2.7.1", "@tiptap/pm": "^2.7.1", "@tiptap/starter-kit": "^2.7.1", "@tiptap/suggestion": "^2.7.1", "crypto-js": "^4.2.0", "i18next": "^23.7.6", "lowlight": "^3.1.0", "markdown-it": "^14.1.0", "markdown-it-container": "^4.0.0", "mitt": "^3.0.1", "node-html-parser": "^6.1.11", "react": "^18.3.1", "react-toastify": "^10.0.6", "tippy.js": "^6.3.7", "tiptap-markdown": "^0.8.10"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "https://github.com/aieditor-team/aieditor"}, "bugs": {"url": "https://github.com/aieditor-team/aieditor/issues"}, "homepage": "https://github.com/aieditor-team/aieditor#readme", "license": "LGPL"}