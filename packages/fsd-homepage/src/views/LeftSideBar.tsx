import { getSideBarData } from "@api/index";
import { useAsyncEffect } from "ahooks";
import { <PERSON>lapse, Divider, Dropdown, Space } from "antd";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { sideBarCollectGroup, USER_INFO } from "./mock";

export default function LeftSideBar({ onAgentClick, activeKey, setActiveKey, setShowCard }: { onAgentClick: (value: string, type: string) => void, activeKey: string, setActiveKey: (value: string) => void, setShowCard: (showCard: boolean	) => void }) {
	const [activeSideBar, setActiveSideBar] = useState(['history']);
	const navigate = useNavigate();
	const VITE_APP_MAIN_AGENT_UUID = import.meta.env.VITE_APP_MAIN_AGENT_UUID;
	const [collectList, setCollectList] = useState(sideBarCollectGroup);
	const [sideBarSpecialGroup, setSideBarSpecialGroup] = useState([]);
	const [sideBarHistoryGroup, setSideBarHistoryGroup] = useState([]);
	const [sideBarMyAgentGroup, setSideBarMyAgentGroup] = useState([]);

	useAsyncEffect(async () => {
		const res = await getSideBarData({
			userName: USER_INFO?.mis,
		});
		const temp = [...res?.data?.sessionList || [], {
			agentUuId: "agent-51d97449-4",
			sessionId: "xXxmrYQrEwaQCr4JHEXRc",
			sessionTopic: "查看全部...",
			value: "BaoDDAC1t4"
		}]
		setSideBarSpecialGroup(res?.data?.defaultAgents	|| []);
		setCollectList(res?.data?.myCollect || []);
		setSideBarHistoryGroup(temp || []);
		setSideBarMyAgentGroup(res?.data?.myAgent || []);
		
	}, []);


	const collectMenu = [
		{
			label: '取消收藏',
			key: 'cancelCollect',
		},
	];
	const MyAgentMenu = [
		{
			label: '收藏',
			key: 'collect',
		},
	];
	const onCollectClick = (e: any, item: any) => {
		if(e.key === 'collect') {
			const isDuplicate = collectList.some(existingItem => existingItem.agentUuId === item.agentUuId);
			if (isDuplicate) {
				return;
			}
			const temp = [item, ...collectList];
			setCollectList(temp);
		}
		else if(e.key === 'cancelCollect') {
			const temp = collectList.filter((e) => e.agentUuId !== item.agentUuId);
			console.log('temp', temp);
			setCollectList(temp);
		}
	}
  return (
		<div className={`w-[280px] bg-white border-r border-gray-200 flex justify-between flex-col p-[16px] pb-[8px]`}>
			<div>
				{sideBarSpecialGroup.map((item, index) => (
					<div 
						key={index} 
						className={`px-1.5 flex h-10 items-center gap-1.5 hover:bg-gray-50 
							${activeKey && activeKey.includes(item.agentUuId) ? 'bg-[#F2F7FF] hover:bg-[#F2F7FF]' : ''} 
							cursor-pointer transition-all duration-200`}
						onClick={() => {
							setActiveKey(item.agentUuId);
							onAgentClick(item.agentUuId, 'special');
							if(item.agentUuId === '/api-test'){
								navigate('/api-test');
							}
							else {
								navigate('/workbench');
							}
							if(item.agentUuId === VITE_APP_MAIN_AGENT_UUID) {
								setShowCard(false);
							}
							else {
								setShowCard(true);
							}
								
						}}
					>
						{item.agentUuId === VITE_APP_MAIN_AGENT_UUID && <img src='https://p0.meituan.net/waimaiqact/98995a630abebe9f2bd8d3eb181c3f0a6797.png' className="w-4 h-4" />}
						{item.agentUuId !== VITE_APP_MAIN_AGENT_UUID && <img src={item.icon} className="w-4 h-4" />}
						<span className={`text-gray-700 ${activeKey && activeKey.includes(item.agentUuId) ? 	'font-bold text-[#0052D9]' : ''} transition-colors duration-200`}>
							{item.agentUuId === VITE_APP_MAIN_AGENT_UUID ? '随便聊聊' : item.label}
						</span>
					</div>
				))}
				<Divider className="my-2" />
				<Collapse
					className={`
						[&_.ant-collapse-header]:!p-0 
						[&_.ant-collapse-content-box]:!p-0
					`}
					ghost
					activeKey={activeSideBar}
					expandIcon={() => null}
					onChange={(key) => {
						setActiveSideBar(key);
					}}
					items={[{
						key: 'history',
						label: (
              <div className="flex h-8 items-center justify-between">
								<div className="flex items-center gap-1.5">
									<i className="aieicon aieicon-chat-history-line"></i>
									<span style={{ fontWeight: 600, marginLeft: '4px' }}>历史会话</span>
								</div>
                
                <div className="ml-1">
                  {activeSideBar.includes('history') ? (
                    <i className="aieicon aieicon-arrow-down-s-line" />
                  ) : (
                    <i className="aieicon aieicon-arrow-left-s-line" />
                  )}
                </div>
              </div>
            ),
						children: sideBarHistoryGroup.map((item, index) => (
							<div 
								key={index} 
								onClick={() => {
									setActiveKey(item.sessionId);
									if(item.agentUuId) {
										navigate(`/workbench`);
										onAgentClick(item.agentUuId, 'history');
									}
								}}
								className={`group px-1.5 flex h-10 items-center gap-1.5 hover:bg-gray-50 
									${activeKey && activeKey.includes(item.sessionId) ? 'bg-[#F2F7FF] hover:bg-[#F2F7FF]' : ''} 
									cursor-pointer transition-all duration-200`}>
								<span className={`ml-[20px] text-gray-700 ${activeKey && activeKey.includes(item.sessionId) ? 'font-bold text-[#0052D9]' : ''} transition-colors duration-200`}>
									{item.sessionTopic}
								</span>
								{/* <i className="aieicon aieicon-delete-bin-line ml-auto hidden group-hover:block text-gray-400 hover:text-gray-600"></i> */}
							</div>
						))
					}]}
				/>
				<Divider className="my-2" />
				<Collapse
					className={`
						[&_.ant-collapse-header]:!p-0 
						[&_.ant-collapse-content-box]:!p-0
					`}
					ghost
					activeKey={activeSideBar}
					expandIcon={() => null}
					onChange={(key) => {
							setActiveSideBar(key);
					}}
					items={[{
						key: 'collect',
						label: (
              <div className="flex h-8 items-center justify-between">
								<div className="flex items-center gap-1.5">
									<i className="aieicon aieicon-star-line"></i>
									<span style={{ fontWeight: 600, marginLeft: '4px' }}>收藏</span>
								</div>
                
                <div className="ml-1">
                  {activeSideBar.includes('collect') ? (
                    <i className="aieicon aieicon-arrow-down-s-line" />
                  ) : (
                    <i className="aieicon aieicon-arrow-left-s-line" />
                  )}
                </div>
              </div>
            ),
						children: collectList.map((item, index) => (
							<div 
								key={index} 
								onClick={() => {
									setActiveKey(`${item.value}_collect`);
									if(item.agentUuId) {
										navigate(`/workbench`);
										onAgentClick(item.agentUuId, 'collect');
									}
								}}
								className={`group px-1.5 flex h-10 items-center gap-1.5 hover:bg-gray-50 
									${activeKey && activeKey.includes(`${item.value}_collect`) ? 'bg-[#F2F7FF] hover:bg-[#F2F7FF]' : ''} 
									cursor-pointer transition-all duration-200`}>
								<span className={`flex items-center gap-1.5 text-gray-700 ${activeKey && activeKey.includes(`${item.value}_collect`) ? 'font-bold text-[#0052D9]' : ''} transition-colors duration-200`}>
									<img src={item.icon} className="w-4 h-4" />
									{item.label}
								</span>
								<Dropdown menu={{ items: collectMenu, onClick: (e) => {
									e.domEvent.stopPropagation();
									onCollectClick(e, item);
								}, }} className="ml-auto hidden group-hover:block text-gray-400 hover:text-gray-60">
									<Space>
										<i className="aieicon aieicon-more-fill"></i>
									</Space>
								</Dropdown>
							</div>
						))
					}]}
				/>
				<Divider className="my-2" />
				<Collapse
					className={`
						[&_.ant-collapse-header]:!p-0 
						[&_.ant-collapse-content-box]:!p-0
					`}
					ghost
					activeKey={activeSideBar}
					expandIcon={() => null}
					onChange={(key) => {
							setActiveSideBar(key);
					}}
					items={[{
						key: 'myAgent',
						label: (
              <div className="flex h-8 items-center justify-between">
								<div className="flex items-center gap-1.5">
									<i className="aieicon aieicon-robot-2-line"></i>
									<span style={{ fontWeight: 600, marginLeft: '4px' }}>我的智能体</span>
								</div>
                
                <div className="ml-1">
                  {activeSideBar && activeSideBar.includes('myAgent') ? (
                    <i className="aieicon aieicon-arrow-down-s-line" />
                  ) : (
                    <i className="aieicon aieicon-arrow-left-s-line" />
                  )}
                </div>
              </div>
            ),
						children: sideBarMyAgentGroup.map((item, index) => (
							<div 
								key={index} 
								onClick={() => {
									setActiveKey(`${item.value}_myAgent`);
									if(item.agentUuId) {
										navigate(`/workbench`);
										onAgentClick(item.agentUuId, 'myAgent');
									}
								}}
								className={`group px-1.5 flex h-10 items-center gap-1.5 hover:bg-gray-50 
									${activeKey && activeKey.includes(`${item.value}_myAgent`) ? 'bg-[#F2F7FF] hover:bg-[#F2F7FF]' : ''} 
									cursor-pointer transition-all duration-200`}>
								<span className={`flex items-center gap-1.5 text-gray-700 ${activeKey && activeKey.includes(`${item.value}_myAgent`) ? 'font-bold text-[#0052D9]' : ''} transition-colors duration-200`}>
									<img src={item.icon} className="w-4 h-4" />
									{item.label}
								</span>
								<Dropdown menu={{ items: MyAgentMenu, onClick: (e) => {
                          e.domEvent.stopPropagation();
                          onCollectClick(e, item);
                        }, }} className="ml-auto hidden group-hover:block text-gray-400 hover:text-gray-60">
										<Space>
											<i className="aieicon aieicon-more-fill"></i>
										</Space>
								</Dropdown>
							</div>
						))
					}]}
				/>
			</div>
			<div onClick={() => {
				setActiveKey('explore');
				navigate('/explore');
			}}>
				<Divider className="my-2" />
				<div className={`h-[40px] px-1.5 flex items-center justify-between gap-1.5 cursor-pointer hover:bg-gray-50
				${activeKey && activeKey.includes('explore') ? 'bg-[#F2F7FF] hover:bg-[#F2F7FF]' : ''} 
cursor-pointer transition-all duration-200`}>
					<div className="flex items-center  gap-2">
						<i className="aieicon aieicon-planet-line"></i>
						<span style={{ fontWeight: 600, marginLeft: '4px' }}>发现智能体</span>
					</div>
					<i className="aieicon aieicon-arrow-right-line"></i>
				</div>
			</div>
			
		</div>
	);
}
