import { useAsyncEffect } from 'ahooks';
import { Dropdown, message, Tabs } from 'antd';
import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getSkillList } from '../api/index';
import GanttTable from '../components/gantt';
import { UserInfo } from "../types/types";
import InputForChat from './InputForChat';
import { mainContentFeaturesList, USER_INFO } from "./mock";

interface MainContentProps {
	agentId: string;
	setAgentId: (value: string) => void;
	setSideBarActiveKey: (value: string) => void;
	setShowCard: (value: boolean) => void;
}

export default function MainContent({ agentId, setAgentId, setSideBarActiveKey, setShowCard }: MainContentProps) {
	const [userInfo] = useState<UserInfo>(USER_INFO);
	const [activeKey, setActiveKey] = useState('');
	const currentTime = new Date();
	const hours = currentTime.getHours();
  const greeting = hours < 12 ? '上午好' : hours < 18 ? '下午好' : '晚上好';
	const chatRef = useRef(null);
	const firstLoad = useRef(true);
	const [activeTab, setActiveTab] = useState('schedule');
	const [skillList, setSkillList] = useState([]);
	const navigate = useNavigate();

	useAsyncEffect(async () => {
		firstLoad.current = false;
		console.log('agentId', agentId, chatRef.current);
		setActiveKey('')
		// if (chatRef.current) {
		// 	await chatRef.current.newSession();
		// }
	}, [agentId]);

	
	useAsyncEffect(async () => {
		const res = await getSkillList({uuid: import.meta.env.VITE_APP_MAIN_AGENT_UUID})
		setSkillList(res?.shortcutFunction || []);
		console.log(skillList);
		}, [])

	const items = [
		{
			label: '技能1',
			key: 'skill1',
		},
		{
			label: '技能2',
			key: 'skill2',
		},
	];

	const tabItems = [
		{
			label: '日程计划',
			key: 'schedule',
		},
		{
			label: '待办事项',
			key: 'todo',
		},
	];

	return <>
		<div className="px-[80px] pt-[56px] relative z-10 bg-white flex flex-col">
			<div className='mb-[32px]'>
				<h1 className="text-2xl font-semibold leading-8 mb-4">
					{greeting}，{userInfo?.name}
				</h1>
				<div className="text-2xl font-semibold leading-8">
					我可以帮你查找信息、提供使用的工具和解答问题，有什么我可以帮你的吗？
				</div>
			</div>
			<InputForChat 
				setAgentId={setAgentId} 
				setShowCard={setShowCard}
			/>
			

			<div className="grid grid-cols-5 gap-4 justify-center mt-[16px] mb-[16px]">
				{mainContentFeaturesList.map((feature, index) => {
					return (
						feature.value === 'more' ? 
						<Dropdown trigger={['click']} menu={{ items: items }} >
							<div 
								key={index}
								onClick={() => {
									setActiveKey(feature.value);
							}}
						className={`p-4 rounded-lg border flex items-center gap-4 cursor-pointer hover:bg-gray-50 group
							${activeKey === feature.value ? 'border-blue-500' : 'border-gray-200'}`}
					>
						
						<div>
							<div className="text-sm font-semibold leading-[22px] flex items-center gap-2 mb-[4px]" > 
								<img src={feature.icon} alt={feature.title} className="w-6 h-6" />
							{feature.title}</div>
							<div className="text-sm font-normal leading-6 text-gray-500">{feature.description}</div>
						</div>
						</div>
						</Dropdown>
						:
						<div 
							key={index}
							onClick={() => {
							setActiveKey(feature.value);
							if(feature.agentUuId === '/api-test'){
								setShowCard(true);
								navigate('/api-test');
								setSideBarActiveKey('/api-test')
							}
							else if(feature.type === 'agent') {
								setAgentId(feature.agentUuId);
								setShowCard(true);
								setSideBarActiveKey(feature.agentUuId)
							}
							else {
								message.info('触发事件');
							}
						}}
						className={`p-4 rounded-lg border flex items-center gap-4 cursor-pointer hover:bg-gray-50 group
							${activeKey === feature.value ? 'border-blue-500' : 'border-gray-200'}`}
					>
						
						<div>
							<div className="text-sm font-semibold leading-[22px] flex items-center gap-2 mb-[4px]" > 
								<img src={feature.icon} alt={feature.title} className="w-6 h-6" />
								{feature.title}
							</div>
							<div className="text-sm font-normal leading-6 text-gray-500">{feature.description}</div>
						</div>
						</div>
					)
				})}
			</div>
			<Tabs
        defaultActiveKey="schedule"
        items={tabItems}
        onChange={(value)=>{setActiveTab(value)}}
        indicator={{ size: (origin) => origin, align: 'center' }}
      />
			{activeTab === 'schedule' ? <GanttTable /> : <div>待办事项</div>}
		</div>
	</>
}
