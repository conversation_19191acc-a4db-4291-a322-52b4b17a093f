{"root": ["./src/app.tsx", "./src/entry.tsx", "./src/global.d.ts", "./src/main.tsx", "./src/public-path.d.ts", "./src/public-path.js", "./src/vite-env.d.ts", "./src/api/axiosinsance.ts", "./src/api/axiosinsancexgpt.ts", "./src/api/device-api.ts", "./src/api/editor.ts", "./src/api/index.ts", "./src/api/flowv2/index.ts", "./src/api/variable/index.ts", "./src/api/workflow/workflowcontroller.ts", "./src/api/workflow/index.ts", "./src/api/workflow/types.ts", "./src/components/customslider.tsx", "./src/components/editabletext.tsx", "./src/components/formgrouptitle.tsx", "./src/components/jsoneditorparsedrawer.tsx", "./src/components/xboticon.tsx", "./src/components/xgptdrawer.tsx", "./src/components/index.tsx", "./src/components/tooltip/whitetooltip.tsx", "./src/components/dynamic-form/dynamicform.tsx", "./src/components/dynamic-form/formeditor.tsx", "./src/components/dynamic-form/reqparamsformlist.tsx", "./src/components/dynamic-form/uploadfilecomp.tsx", "./src/components/dynamic-form/types.ts", "./src/components/dynamic-form/varutils.ts", "./src/components/dynamic-form/input-support-variable/editor.tsx", "./src/components/dynamic-form/input-support-variable/hooks.ts", "./src/components/dynamic-form/input-support-variable/index.tsx", "./src/components/dynamic-form/input-support-variable/types.ts", "./src/components/dynamic-form/input-support-variable/utils.ts", "./src/components/dynamic-form/input-support-variable/plugins/on-blur-or-focus-block.tsx", "./src/components/dynamic-form/input-support-variable/plugins/placeholder.tsx", "./src/components/dynamic-form/input-support-variable/plugins/tree-view.tsx", "./src/components/dynamic-form/input-support-variable/plugins/custom-text/node.tsx", "./src/components/dynamic-form/input-support-variable/plugins/variable-block/component.tsx", "./src/components/dynamic-form/input-support-variable/plugins/variable-block/index.tsx", "./src/components/dynamic-form/input-support-variable/plugins/variable-block/node.tsx", "./src/components/dynamic-form/input-support-variable/plugins/variable-block/variable-block-replacement-block.tsx", "./src/components/dynamic-form/input-support-variable/plugins/variable-picker-block/index.tsx", "./src/components/dynamic-form/input-support-variable/plugins/variable-picker-block/menu.tsx", "./src/store/index.ts", "./src/store/user.ts", "./src/utils/classnames.ts", "./src/utils/clone.ts", "./src/utils/index.ts", "./src/utils/permission.ts", "./src/utils/store.ts", "./src/views/form/custom-input.tsx", "./src/views/form/index.tsx", "./src/views/home/<USER>", "./src/views/home/<USER>", "./src/views/workflow/element/index.tsx", "./src/views/workflow/variable/index.tsx", "./src/views/workflow/components/jsoneditor.tsx", "./src/views/workflow/components/mobiledeviceselect.tsx", "./src/views/workflow/components/pcdeviceselect.tsx", "./src/views/workflow/components/index.tsx", "./src/views/workflow/config/addnodesider.tsx", "./src/views/workflow/config/header.tsx", "./src/views/workflow/config/index.tsx", "./src/views/workflow/config/index2.tsx", "./src/views/workflow/device/device-list-modal.tsx", "./src/views/workflow/device/index.tsx", "./src/views/workflow/device/screen.tsx", "./src/views/workflow/device/store.ts", "./src/views/workflow/device/types.ts", "./src/views/workflow/editor/editor-context.tsx", "./src/views/workflow/editor/editor.tsx", "./src/views/workflow/editor/index.tsx", "./src/views/workflow/editor/types.ts", "./src/views/workflow/editor/api/index.d.ts", "./src/views/workflow/editor/api/index.js", "./src/views/workflow/editor/common/constants.ts", "./src/views/workflow/editor/common/index.ts", "./src/views/workflow/editor/components/ant-style-var-input.tsx", "./src/views/workflow/editor/components/field.tsx", "./src/views/workflow/editor/components/input-support-select-var.tsx", "./src/views/workflow/editor/components/selector.tsx", "./src/views/workflow/editor/components/split.tsx", "./src/views/workflow/editor/components/before-run-form/form-item.tsx", "./src/views/workflow/editor/components/before-run-form/form.tsx", "./src/views/workflow/editor/components/before-run-form/index.tsx", "./src/views/workflow/editor/components/editor/base.tsx", "./src/views/workflow/editor/components/editor/prompt-editor-height-resize-wrap.tsx", "./src/views/workflow/editor/components/editor/text-editor.tsx", "./src/views/workflow/editor/components/editor/wrap.tsx", "./src/views/workflow/editor/components/editor/code-editor/index.tsx", "./src/views/workflow/editor/components/indicator/index.tsx", "./src/views/workflow/editor/components/portal-to-follow-elem/index.tsx", "./src/views/workflow/editor/components/prompt/editor.tsx", "./src/views/workflow/editor/components/prompt-editor/constants.tsx", "./src/views/workflow/editor/components/prompt-editor/hooks.ts", "./src/views/workflow/editor/components/prompt-editor/index.tsx", "./src/views/workflow/editor/components/prompt-editor/types.ts", "./src/views/workflow/editor/components/prompt-editor/utils.ts", "./src/views/workflow/editor/components/prompt-editor/plugins/on-blur-or-focus-block.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/placeholder.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/tree-view.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/component-picker-block/index.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/component-picker-block/menu.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/custom-text/node.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/workflow-variable-block/component.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/workflow-variable-block/index.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/workflow-variable-block/node.tsx", "./src/views/workflow/editor/components/prompt-editor/plugins/workflow-variable-block/workflow-variable-block-replacement-block.tsx", "./src/views/workflow/editor/components/run/meta.tsx", "./src/views/workflow/editor/components/run/result-panel.tsx", "./src/views/workflow/editor/components/run/status.tsx", "./src/views/workflow/editor/components/tooltip-plus/index.tsx", "./src/views/workflow/editor/components/variable/var-reference-vars.tsx", "./src/views/workflow/editor/custom-command/command-list.tsx", "./src/views/workflow/editor/custom-command/command.ts", "./src/views/workflow/editor/custom-command/constants.tsx", "./src/views/workflow/editor/custom-command/default-command.tsx", "./src/views/workflow/editor/custom-command/index.tsx", "./src/views/workflow/editor/custom-command/util.ts", "./src/views/workflow/editor/excute-modal/excute-modal.tsx", "./src/views/workflow/editor/hooks/use-available-var-list.ts", "./src/views/workflow/editor/hooks/use-node-config-state.ts", "./src/views/workflow/editor/hooks/use-one-step-run.ts", "./src/views/workflow/editor/hooks/use-toggle-expend.ts", "./src/views/workflow/editor/hooks/use-var-list.ts", "./src/views/workflow/editor/nodes/common.tsx", "./src/views/workflow/editor/nodes/code/outputtable.tsx", "./src/views/workflow/editor/nodes/code/paramstable.tsx", "./src/views/workflow/editor/nodes/code/default.ts", "./src/views/workflow/editor/nodes/code/node.ts", "./src/views/workflow/editor/nodes/code/node.view.tsx", "./src/views/workflow/editor/nodes/code/types.ts", "./src/views/workflow/editor/nodes/code/use-config.ts", "./src/views/workflow/editor/nodes/else/node.ts", "./src/views/workflow/editor/nodes/else/node.view.tsx", "./src/views/workflow/editor/nodes/elseif/node.ts", "./src/views/workflow/editor/nodes/elseif/node.view.tsx", "./src/views/workflow/editor/nodes/elseif/types.ts", "./src/views/workflow/editor/nodes/endnode/paramsformitem.tsx", "./src/views/workflow/editor/nodes/endnode/node.ts", "./src/views/workflow/editor/nodes/endnode/node.view.tsx", "./src/views/workflow/editor/nodes/endnode/types.ts", "./src/views/workflow/editor/nodes/for/node.ts", "./src/views/workflow/editor/nodes/for/node.view.tsx", "./src/views/workflow/editor/nodes/for/types.ts", "./src/views/workflow/editor/nodes/http/default.ts", "./src/views/workflow/editor/nodes/http/node.ts", "./src/views/workflow/editor/nodes/http/node.view.tsx", "./src/views/workflow/editor/nodes/http/types.ts", "./src/views/workflow/editor/nodes/http/use-config.ts", "./src/views/workflow/editor/nodes/http/components/api-input.tsx", "./src/views/workflow/editor/nodes/http/components/authorization/index.tsx", "./src/views/workflow/editor/nodes/http/components/authorization/radio-group.tsx", "./src/views/workflow/editor/nodes/http/components/edit-body/index.tsx", "./src/views/workflow/editor/nodes/http/components/edit-body/text.tsx", "./src/views/workflow/editor/nodes/http/components/key-value/index.tsx", "./src/views/workflow/editor/nodes/http/components/key-value/bulk-edit/index.tsx", "./src/views/workflow/editor/nodes/http/components/key-value/key-value-edit/index.tsx", "./src/views/workflow/editor/nodes/http/components/key-value/key-value-edit/input-item.tsx", "./src/views/workflow/editor/nodes/http/components/key-value/key-value-edit/item.tsx", "./src/views/workflow/editor/nodes/http/components/timeout/index.tsx", "./src/views/workflow/editor/nodes/http/hooks/use-key-value-list.ts", "./src/views/workflow/editor/nodes/http/hooks/use-name-value-list.ts", "./src/views/workflow/editor/nodes/if/configdrawer.tsx", "./src/views/workflow/editor/nodes/if/node.ts", "./src/views/workflow/editor/nodes/if/node.view.tsx", "./src/views/workflow/editor/nodes/if/types.ts", "./src/views/workflow/editor/nodes/kbnode/default.ts", "./src/views/workflow/editor/nodes/kbnode/node.ts", "./src/views/workflow/editor/nodes/kbnode/node.view.tsx", "./src/views/workflow/editor/nodes/kbnode/query-str-edit.tsx", "./src/views/workflow/editor/nodes/kbnode/types.ts", "./src/views/workflow/editor/nodes/kbnode/use-config.ts", "./src/views/workflow/editor/nodes/llmnode/modelconfigpopover.tsx", "./src/views/workflow/editor/nodes/llmnode/default.ts", "./src/views/workflow/editor/nodes/llmnode/node.ts", "./src/views/workflow/editor/nodes/llmnode/node.view.tsx", "./src/views/workflow/editor/nodes/llmnode/types.ts", "./src/views/workflow/editor/nodes/llmnode/use-config.ts", "./src/views/workflow/editor/nodes/messagenode/node.ts", "./src/views/workflow/editor/nodes/messagenode/node.view.tsx", "./src/views/workflow/editor/nodes/messagenode/types.ts", "./src/views/workflow/editor/nodes/rpa/paramsformitem.tsx", "./src/views/workflow/editor/nodes/rpa/node.ts", "./src/views/workflow/editor/nodes/rpa/node.view.tsx", "./src/views/workflow/editor/nodes/rpa/types.ts", "./src/views/workflow/editor/nodes/rpa/use-config.ts", "./src/views/workflow/editor/nodes/rpa/form/custom-input.tsx", "./src/views/workflow/editor/nodes/rpa/form/index.tsx", "./src/views/workflow/editor/nodes/rpa/schema/index.tsx", "./src/views/workflow/editor/nodes/rpa/schema/openurl.tsx", "./src/views/workflow/editor/nodes/rpa/schema/switchwinbyindex.tsx", "./src/views/workflow/editor/nodes/rpa/schema/watingpageloading.tsx", "./src/views/workflow/editor/nodes/startnode/paramsformitem.tsx", "./src/views/workflow/editor/nodes/startnode/ruleextrapopover.tsx", "./src/views/workflow/editor/nodes/startnode/constants.ts", "./src/views/workflow/editor/nodes/startnode/node.ts", "./src/views/workflow/editor/nodes/startnode/node.view.tsx", "./src/views/workflow/editor/nodes/startnode/types.ts", "./src/views/workflow/editor/nodes/startnode/select-type-item/index.tsx", "./src/views/workflow/editor/nodes/variablenode/paramsformitem.tsx", "./src/views/workflow/editor/nodes/variablenode/node.ts", "./src/views/workflow/editor/nodes/variablenode/node.view.tsx", "./src/views/workflow/editor/nodes/variablenode/types.ts", "./src/views/workflow/editor/slash-command/nodelistpopover.tsx", "./src/views/workflow/editor/slash-command/built-in-node.tsx", "./src/views/workflow/editor/slash-command/index.tsx", "./src/views/workflow/editor/slash-command/rpa-node.tsx", "./src/views/workflow/editor/utils/caseopt.ts", "./src/views/workflow/editor/utils/commandopt.ts", "./src/views/workflow/editor/utils/formitem.ts", "./src/views/workflow/editor/utils/index.ts", "./src/views/workflow/editor/utils/variable.ts", "./src/views/workflow/store/config.ts"], "errors": true, "version": "5.6.3"}