import { AxiosHttp, ICreateAxiosOptions } from "@waimai/waimai-qa-aie-utils";


const config:ICreateAxiosOptions = {
  baseURL: import.meta.env.VITE_APP_RPA_HOST,
};
// if (import.meta.env.VITE_APP_ENV === "development") {
//   config['headers'] = {
//     'Xc-Auth': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbWFpbCI6ImJhaXl1amlhbyIsImlkIjoidXNuYmdjdXV2dTh2YzcyYiIsInJvbGVzIjoib3JnLWxldmVsLWNyZWF0b3Isc3VwZXIiLCJ0b2tlbl92ZXJzaW9uIjoiNzZmNmJlZTAxZTJhMDk0Nzg1ZjQ0Y2YxNDViNTFkOTcxNDE3M2Y5NmU1OTE0Y2RjYTM2ZjE4ZjRkMzRhOGUyYjM4MmFhNzgzYTZkZGFlYjUiLCJpYXQiOjE3MzI2MjY0MDMsImV4cCI6MTczMjY2MjQwM30.U8pFKRmKsSOOiziYKy1je5OQkM3ZhG3JDlGsSbSJHRs'
//   }
// }
const axiosInstanceRpa = new AxiosHttp(config).getInstance();

export default axiosInstanceRpa;






