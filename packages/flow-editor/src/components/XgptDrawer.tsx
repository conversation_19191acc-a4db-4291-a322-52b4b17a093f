import { Drawer, DrawerProps } from 'antd';
import React from 'react';
import XbotIcon from './XbotIcon';

interface XgptDrawerProps extends Omit<DrawerProps, 'closeIcon'> {
  hideClose?: boolean;
}

const XgptDrawer: React.FC<XgptDrawerProps> = ({
  hideClose,
  children,
  onClose,
  ...rest
}) => {
  return (
    <Drawer
      closeIcon={false}
      {...rest}
      style={{ ...rest.style }}
      styles={{
        ...rest.styles,
        header: {
          ...rest.styles?.header,
          minHeight: '64px',
          maxHeight: '65px',
          fontSize: '16px',
          fontWeight: 600,
          lineHeight: '24px',
        },
      }}
      onClose={onClose}
    >
      {!hideClose && (
        <div
          className="absolute left-[-12px] top-[51px] flex h-6 w-6 cursor-pointer items-center justify-center rounded-full border-[1px] border-outline bg-white"
          onClick={onClose}
        >
          <XbotIcon className="xgpticon-close-line text-secondary" />
        </div>
      )}
      {children}
    </Drawer>
  );
};

export default XgptDrawer;
