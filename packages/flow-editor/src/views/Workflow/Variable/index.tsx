import { DeleteOutlined, EditOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Card, Flex, Form, Input, Modal, Pagination, Select, Space, Spin, message } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import Api from '../../../api/variable';
import useVariableStore from './store';
import { DATA_TYPE_OPTIONS } from '../../Workflow/editor/types';

const { Option } = Select;
const id = new URLSearchParams(window.location.search).get('id') || '';
const DATA_TYPE = (Number(new URLSearchParams(window.location.search).get('type')) as 1 | 2) || 1; //1.应用 2业务
// 类型定义
interface Variable {
  id?: number;
  name: string;
  dataType: number;
  defaultValue: string;
  description?: string;
  enumValue?: string;
  unit?: string;
  variableType: number;
  relevanceId: number | string | undefined;
  isValid?: number;
  createTime?: number;
  updateTime?: number;
  updateBy?: string;
  inOutType: number; // 新增：0-输入 1-输出
}

// 数据类型选
// VariableForm 组件
const VariableForm: React.FC<{
  variable?: Variable;
  onSave: (variable: Variable) => void;
  onCancel: () => void;
}> = ({ variable, onSave, onCancel }) => {

  const [form] = Form.useForm();
  const dataType = Form.useWatch('dataType', form);

  useEffect(() => {
    if (variable) {
      const nameWithoutPrefix = variable.name.startsWith('doc.') 
        ? variable.name.slice(4) 
        : variable.name;
      form.setFieldsValue({
        ...variable,
        name: nameWithoutPrefix,
        prename: 'doc.',
      });
    }
  }, [variable, form]);

  // 表单验证规则
  const validationRules = {
    name: [
      { required: true, message: '请输入变量名称' },
    ],
    defaultValue: [
      { required: true, message: '请输入默认值' },
      {
        validator: async (_: any, value: string) => {
          if (!value) return;

          switch (dataType) {
            case 2: // Float
            case 3: // Double
              if (!/^-?\d*\.?\d+$/.test(value)) throw new Error('请输入有效的数字');
              break;
            case 4: // Short
            case 5: // Integer
            case 6: // Long
              if (!/^-?\d+$/.test(value)) throw new Error('请输入有效的整数');
              break;
            case 7: // Boolean
              if (!['true', 'false'].includes(value.toLowerCase())) throw new Error('请输入 true 或 false');
              break;
            case 10: // JSON
              try {
                JSON.parse(value);
              } catch {
                throw new Error('请输入有效的 JSON 格式');
              }
              break;
          }
        },
      },
    ],
    enumValue: [
      {
        required: dataType === 8 || dataType === 9,
        message: '请输入枚举值',
      },
    ],
  };

  const onFinish = (values: Variable) => {
    // 确保 name 不重复添加 doc. 前缀
    const finalName = values.name.startsWith('doc.') 
      ? values.name 
      : `doc.${values.name}`;
    
    onSave({
      ...values,
      name: finalName,
      variableType: DATA_TYPE,
      relevanceId: id || undefined,
      ...(variable?.id ? { id: variable.id } : {}),
    });
  };

    const prefixSelector = (
      <Form.Item name="prename" noStyle>
        <Select style={{ width: 100 }}>
          <Option value="doc.">doc.</Option>
        </Select>
      </Form.Item>
    );

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={{ ...variable, inOutType: variable?.inOutType ?? 0 }}
      validateTrigger={['onChange', 'onBlur']}>
      <Form.Item name="name" label="变量名称" rules={validationRules.name}>
        <Input addonBefore={prefixSelector} style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item name="dataType" label="数据类型" rules={[{ required: true }]}>
        <Select
          placeholder="请选择数据类型"
          onChange={() => {
            form.setFieldsValue({ defaultValue: undefined, enumValue: undefined });
          }}>
          {DATA_TYPE_OPTIONS.map((option) => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name="defaultValue"
        label="默认值"
        rules={validationRules.defaultValue}
        extra={dataType === 10 ? '请输入有效的 JSON 格式' : undefined}>
        {dataType === 10 ? (
          <Input.TextArea rows={4} placeholder="请输入 JSON 格式的默认值" />
        ) : (
          <Input placeholder="请输入默认值" />
        )}
      </Form.Item>

      {(dataType === 8 || dataType === 9) && (
        <Form.Item name="enumValue" label="枚举值" rules={validationRules.enumValue} extra="多个枚举值请用英文逗号分隔">
          <Input.TextArea placeholder="例如: RED,GREEN,BLUE" />
        </Form.Item>
      )}

      <Form.Item name="unit" label="单位">
        <Input placeholder="请输入单位" />
      </Form.Item>

      <Form.Item name="description" label="变量描述">
        <Input.TextArea placeholder="请输入变量描述" />
      </Form.Item>

      <Form.Item name="inOutType" label="参数类型" rules={[{ required: true }]}>
        <Select placeholder="请选择参数类型">
          <Option value={0}>输入参数</Option>
          <Option value={1}>输出参数</Option>
        </Select>
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
          <Button onClick={onCancel}>取消</Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

// VariableCard 组件
const VariableCard: React.FC<{
  variable: Variable;
  onEdit: (variable: Variable) => void;
  onDelete: (id: number) => void;
}> = ({ variable, onEdit, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);

  const dataTypeLabel = useMemo(() => {
    return DATA_TYPE_OPTIONS.find((option) => option.value === variable.dataType)?.label || '未知类型';
  }, [variable.dataType]);

  if (isEditing) {
    return (
      <Card className="w-full">
        <VariableForm
          variable={variable}
          onSave={(updatedVariable) => {
            onEdit(updatedVariable);
            setIsEditing(false);
          }}
          onCancel={() => setIsEditing(false)}
        />
      </Card>
    );
  }

  return (
    <div className="w-full rounded border p-4">
      <div className="mb-4 flex items-center justify-between">
        <h3>{variable.name}</h3>
        <div className="flex">
          <Button type="link" icon={<EditOutlined />} onClick={() => setIsEditing(true)} />
          <Button type="link" icon={<DeleteOutlined />} onClick={() => onDelete(variable.id!)} />
        </div>
      </div>
      <p className="flex">
        <div>默认值：</div>
        {variable.defaultValue}
      </p>
      <p className="flex">
        <div>数据类类型：</div>
        {dataTypeLabel}
      </p>
      {variable.enumValue && (
        <p className="flex">
          <div>枚举值：</div>
          {variable.enumValue}
        </p>
      )}
      {variable.unit && (
        <p className="flex">
          <div>单位：</div>
          {variable.unit}
        </p>
      )}
      {variable.description && (
        <p className="flex">
          <div>描述：</div>
          {variable.description}
        </p>
      )}
      <p className="flex">
        <div>参数类型：</div>
        {variable.inOutType === 0 ? '输入参数' : '输出参数'}
      </p>
    </div>
  );
};

// 工具函数：防抖
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
}

// 主应用组件
function App() {
  const [loading, setLoading] = useState(false);
  const [variables, setVariables] = useState<Variable[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingVariable, setEditingVariable] = useState<Variable | undefined>();
  const [searchTerm, setSearchTerm] = useState('');
  const { setVariableList } = useVariableStore();

  // 加载变量列表
  const loadVariables = async (search?: string) => {
    try {
      setLoading(true);
      const res = await Api.queryGlobalVariable({
        variableType: DATA_TYPE,
        relevanceId: id,
        name: search || searchTerm,
      });
      if (res.code === 0) {
        setVariables(res.data);
        setVariableList(res.data);
      } else {
        message.error(res.msg || '加载失败');
      }
    } catch (error: any) {
      message.error('网络错误，请稍后重试', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadVariables();
  }, [id]);

  // 防抖搜索
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchTerm(value);
        loadVariables(value);
      }, 500),
    [],
  );

  // 处理保存
  const handleSave = async (variable: Variable) => {
    try {
      const res = await Api.saveGlobalVariable({
        ...variable,
        relevanceId: id,
      });
      if (res.code === 0) {
        message.success('保存成功');
        setModalVisible(false);
        setEditingVariable(undefined);
        loadVariables();
      } else {
        message.error(res.msg || '保存失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后无法恢复，是否继续？',
      onOk: async () => {
        try {
          const res = await Api.deleteGlobalVariable(id);
          if (res.code === 0) {
            message.success('删除成功');
            loadVariables();
          } else {
            message.error(res.msg || '删除失败');
          }
        } catch (error) {
          message.error('网络错误，请稍后重试');
        }
      },
    });
  };

  // 打开编辑弹窗
  const handleEdit = (variable: Variable) => {
    setEditingVariable(variable);
    setModalVisible(true);
  };

  // 打开新增弹窗
  const handleAdd = () => {
    setEditingVariable(undefined);
    setModalVisible(true);
  };

  return (
    <div className="w-full max-w-6xl overflow-y-auto pt-4">
      <Spin spinning={loading}>
        <Space direction="vertical" size={16} className="w-full">
          <Flex justify="space-between" align="center" style={{ gap: '6px' }}>
            <Input
              className="w-full"
              placeholder="按变量名称进行搜索"
              allowClear
              prefix={<SearchOutlined />}
              onChange={(e) => debouncedSearch(e.target.value)}
            />
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} />
          </Flex>

          {variables.map((variable) => (
            <div key={variable.id} className="w-full rounded border p-4">
              <div className="mb-4 flex items-center justify-between">
                <h3>{variable.name}</h3>
                <Space>
                  <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(variable)} />
                  <Button type="link" icon={<DeleteOutlined />} onClick={() => handleDelete(variable.id!)} />
                </Space>
              </div>
              <p className="flex">
                <div>默认值：</div>
                {variable.defaultValue}
              </p>
              <p className="flex">
                <div>数据类类型：</div>
                {DATA_TYPE_OPTIONS.find((option) => option.value === variable.dataType)?.label || '未知类型'}
              </p>
              {variable.enumValue && (
                <p className="flex">
                  <div>枚举值：</div>
                  {variable.enumValue}
                </p>
              )}
              {variable.unit && (
                <p className="flex">
                  <div>单位：</div>
                  {variable.unit}
                </p>
              )}
              {variable.description && (
                <p className="flex">
                  <div>描述：</div>
                  {variable.description}
                </p>
              )}
              <p className="flex">
                <div>参数类型：</div>
                {variable.inOutType === 0 ? '输入参数' : '输出参数'}
              </p>
            </div>
          ))}
        </Space>
      </Spin>

      {/* 新增/编辑弹窗 */}
      <Modal
        title={editingVariable ? '编辑变量' : '新增变量'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingVariable(undefined);
        }}
        footer={null}
        width={600}
      >
        <VariableForm
          variable={editingVariable}
          onSave={handleSave}
          onCancel={() => {
            setModalVisible(false);
            setEditingVariable(undefined);
          }}
        />
      </Modal>
    </div>
  );
}

export default App;
