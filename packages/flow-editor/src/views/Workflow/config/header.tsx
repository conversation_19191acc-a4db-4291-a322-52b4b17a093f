import { DownOutlined } from '@ant-design/icons';
import { getCommandHistoryLog } from '@api/editor';
import Api from '@api/flowV2';
import type { InitExecuteRecordParams } from '@api/flowV2';
import type { JSONContent } from '@tiptap/react';
import { Button, Collapse, type CollapseProps, Drawer, Dropdown, Input, message, Spin, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { pageList } from '../../../api/app';
import { emitter } from '../../../store/emitter';
import PublishAppModal from '../components/PublishAppModal';
import { HeaderBtn } from '../device/headerBtn';
import { useDeviceStore } from '../device/store';
import { NodeType } from '../editor/types';
import { HistoryTask, useExecutionStore } from './excution';
import { usePreventSwipeBack } from './usePreventSwipeBack';

const id = new URLSearchParams(window.location.search).get('id') || '';
const spaceId = new URLSearchParams(window.location.search).get('spaceId') || '';
const DATA_TYPE = (Number(new URLSearchParams(window.location.search).get('type')) as 1 | 2) || 1; //1.应用 2业务

interface ToolbarButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children: React.ReactNode;
    onClick: () => void;
}

const ToolbarButton: React.FC<ToolbarButtonProps> = ({ children, className, onClick, ...props }) => (
    <button
        className={`rounded bg-gray-100 px-3 py-1 text-xs font-medium text-gray-600 transition-colors duration-200 ease-in-out hover:bg-gray-200 ${props.disabled ? 'cursor-not-allowed opacity-50 hover:bg-gray-100' : ''} ${className} `}
        onClick={onClick}
        {...props}>
        {children}
    </button>
);

interface ToolbarButtonsProps {
    onSave: () => void;
    // onRun: () => void;
    onDebug: () => void;
    onStop?: () => void;
    onNext?: () => void;
    onContinue?: () => void;
    onCollect?: (key: 'open' | 'start' | 'end' | 'close') => void;
    onHistory?: () => void;
    onRecord?: () => void;
    disabled?: boolean;
    isDebugging?: boolean;
    showDebugControls?: boolean;
    isElectron?: boolean;
    isRecording?: boolean;
    recordBtnText?: string;
}

const ToolbarButtons: React.FC<ToolbarButtonsProps> = ({
    onSave,
    // onRun,
    onDebug,
    onStop,
    onNext,
    onContinue,
    onCollect,
    onRecord,
    onHistory,
    disabled = false,
    isDebugging = false,
    showDebugControls = false,
    isElectron = false,
    recordBtnText = '智能录制',
    isRecording = false,
}) => {
    const buttons = [
        {
            text: (
                <div className="flex items-center">
                    <i className={`aieicon ${isRecording ? 'aieicon-stop-circle-line recording-blink' : "aieicon-play-circle-fill"} mr-1`} style={{ color: '#F3682C' }}></i>
                    {recordBtnText}
                </div>
            ),
            onClick: onRecord,
            tooltip: isDebugging ? '请等待调试运行结束后启动' : '',
            disabled: isDebugging,
        },
        {
            text: (
                <div className="flex items-center">
                    <i className="aieicon aieicon-play-circle-fill mr-1" style={{ color: '#8B5CF6' }}></i>
                    元素采集
                </div>
            ),
            onClick: () => onCollect?.('open'),
            hidden: !isElectron,
        },
        {
            text: (
                <div className="flex items-center">
                    <i className="aieicon aieicon-play-circle-fill mr-1" style={{ color: '#48B853' }}></i>
                    调试运行
                </div>
            ),
            // onClick: onRun,
            onClick: onDebug,
            tooltip: isRecording ? '请等待录制结束后启动' : '',
            disabled: isRecording,
            type: 'text',
        },
        {
            text: (
                <div className="flex items-center">
                    <i className="aieicon aieicon-history-line mr-1" style={{ color: '#48B853' }}></i>
                    调试日志
                </div>
            ),
            // onClick: onRun,
            onClick: onHistory,
            disabled: disabled || isDebugging || isRecording,
            type: 'text',
        },
        // {
        //     text: (
        //         <div className="flex items-center">
        //             <i className="aieicon aieicon-bug-fill mr-1" style={{ color: '#F3682C' }}></i>
        //             调试
        //         </div>
        //     ),
        //     onClick: onDebug,
        //     disabled: disabled || isRecording,
        // },
        {
            text: (
                <div className="flex items-center">
                    <i className="aieicon aieicon-arrow-right-circle-fill mr-1" style={{ color: '#1652F7' }}></i>
                    下一步
                </div>
            ),
            onClick: onNext,
            className: 'bg-blue-500 text-white',
            hidden: !showDebugControls,
        },
        {
            text: (
                <div className="flex items-center">
                    <i className="aieicon aieicon-continue-fill mr-1" style={{ color: '#1652F7' }}></i>
                    继续
                </div>
            ),
            onClick: onContinue,
            className: 'bg-blue-500 text-white',
            hidden: !showDebugControls,
        },
        {
            text: (
                <div className="flex items-center">
                    <i className="aieicon aieicon-stop-circle-fill mr-1" style={{ color: '#DE4040' }}></i>
                    停止
                </div>
            ),
            onClick: onStop,
            className: 'bg-red-500 text-white',
            hidden: !disabled && !isDebugging,
        },
    ];

    return (
        <div className="flex items-center space-x-4">
            {buttons.map(
                (button, index) =>
                    !button.hidden && (
                        <Tooltip title={button.tooltip}>
                            <ToolbarButton
                                key={typeof button.text === 'string' ? button.text : index}
                                className={`${button.disabled ? 'cursor-not-allowed opacity-50' : ''}`}
                                onClick={button.onClick || (() => { })}
                                disabled={button.disabled}>
                                {button.text}
                            </ToolbarButton>
                        </Tooltip>
                    ),
            )}
        </div>
    );
};

// 定义应用列表项的类型
interface AppItem {
    id: string;
    name: string;
    applicationId: string;
    icon?: string;
}

interface RenderHeaderProps {
    basicinfo: any;
    onAppChange: (newId: string) => void;
    onSave: () => void;
    currentXml?: string;
    debugIdList?: number[];
    onOrchestrationUpdate?: (data: { content: string; xml: string }) => void;
    content?: {
        type: string;
        content: JSONContent;
    };
}

interface SwitchTabButtonProps {
    active: boolean;
    tabKey: string;
    label: string;
    onChange: (value: string) => void;
}

const SwitchTabButton = ({ active, tabKey, label, onChange }: SwitchTabButtonProps) => {
    return (
        <div
            className={`flex h-7 flex-1 cursor-pointer items-center justify-center text-xs ${active ? 'font-medium text-blue-500' : 'text-gray-600'} `}
            onClick={() => onChange(tabKey)}>
            {label}
        </div>
    );
};

let editorContent: JSONContent = {};
let regenerateAITaskNames: any = [];

const RenderHeader = ({ basicinfo, onAppChange, ...props }: RenderHeaderProps) => {
    const [wsConnection, setWsConnection] = React.useState<WebSocket | null>(null);

    const [currentRunNode, setCurrentRunNode] = React.useState<any>({});

    // const [, setIsExecuting] = React.useState(false);
    const [isDebugging, setIsDebugging] = React.useState(false);
    const [showDebugControls, setShowDebugControls] = React.useState(false);
    const { isExecuting, setExecutingNodeIds, setHistoryTaskList, setIsExecuting } = useExecutionStore();
    const [historyRecordInfo, setHistoryRecordInfo] = React.useState<any>({});
    const [historyRecordList, setHistoryRecordList] = React.useState<any[]>([]);
    const [historyRecordLoading, setHistoryRecordLoading] = React.useState(false);
    const [historyRecordDrawerOpen, setHistoryRecordDrawerOpen] = React.useState(false);
    const [recordId, setRecordId] = React.useState<string>('');
    const [releaseVersionOpen, setReleaseVersionOpen] = React.useState(false);

    const [apps, setApps] = useState<AppItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [activeTab, setActiveTab] = useState('myDevelop');
    const [dropdownOpen, setDropdownOpen] = useState(false);
    // 录制状态
    // 设备连接状态
    const [isRecording, setIsRecording] = useState(false);
    const [isDeviceConnected, setIsDeviceConnected] = useState(false);
    const [linkedDeviceType, setLinkedDeviceType] = useState('');
    useEffect(() => {
        const unsubcribe = useDeviceStore.subscribe((state) => {
            setIsDeviceConnected(state.isLinkedStatus);
            setIsRecording(state.isRecording);
            setLinkedDeviceType(state.linkedDeviceType);
        });
        return () => unsubcribe();
    }, []);

    const resetState = () => {
        setIsExecuting(false);
        setIsDebugging(false);
        setCurrentRunNode({});
        setWsConnection(null);
        setShowDebugControls(false);
        setExecutingNodeIds([]);
    };

    const handleStop = () => {
        if (wsConnection) {
            wsConnection.close();
            resetState();
            message.success('已停止执行');
        }
    };

    // 获取基础URL的域名部分
    const getBaseDomain = () => {
        const baseUrl = 'enginegateway.waimai.test.sankuai.com';
        try {
            const url = new URL(baseUrl);
            return url.hostname;
        } catch (e) {
            console.error('解析VITE_APP_BASE_URL失败:', e);
            return '';
        }
    };

    const handleAIEContent = () => {
        const aiNodes = editorContent?.filter((node: any) => node.type === NodeType.Ai);
        const aiNodesTagList = aiNodes?.map((node: any) => node?.attrs?.tag);
        regenerateAITaskNames = aiNodesTagList;
    };

    const initializeWebSocket = async (executeType: 1 | 2, additionalPayload: object = {}, isReGenerate: boolean ) => {
        // 处理ai节点特殊参数逻辑（liubo108）
        isReGenerate && handleAIEContent();
        try {
            // 2. 建立WebSocket连接
            const pcLinkedDevice = useDeviceStore.getState().pcLinkedDevice;
            const mobileLinkedDevice = useDeviceStore.getState().mobileLinkedDevice;
            const mobileDeviceAndVersion = useDeviceStore.getState().mobileDeviceAndVersion;
            const mobileConnectSonicToken = useDeviceStore.getState().mobileConnectSonicToken;

            if (!pcLinkedDevice && !mobileLinkedDevice) {
                message.info('未连接设备，将使用本地执行引擎');
                // return;
            }

            const params: InitExecuteRecordParams = {
                bizType: DATA_TYPE,
                bizId: id,
                deviceId: pcLinkedDevice ? pcLinkedDevice.id : mobileLinkedDevice ? mobileLinkedDevice.id : -1,
                deviceType: pcLinkedDevice ? 1 : mobileDeviceAndVersion ? 2 : 3,
                executeType
            };
            if(isReGenerate){
                params.regenerateAITaskNames = regenerateAITaskNames;
            }

            const res = await Api.initExecuteRecord(params);

            const wsPayload = {
                channel: res.data.channel,
                uuid: res.data.executeRecordId,
                execXml: props.currentXml,
                eventEnum: 5,
                logCallbackUrl: 'http://digitalgateway.waimai.test.sankuai.com/market/api/provider/v1/rpa/engine/logCallback',
                executeType,
                extendInfo: '',
                macroDefVOList: res.data.macroDefVOList || [],
                globalVariable: {
                    ...res.data.globalVarMap,
                    isRunMobile: mobileLinkedDevice ? true : false,
                },
                ...additionalPayload,
            };

            let ws: WebSocket;

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

            if (pcLinkedDevice) {
                console.log('正在连接的pc设备信息', pcLinkedDevice);
                const headers = {
                    'X-Target-Host': pcLinkedDevice?.ip,
                    'X-Target-Port': pcLinkedDevice?.port,
                };

                const wsUrl =
                    protocol === 'wss:'
                        ? `wss://enginegateway.waimai.test.sankuai.com/common/ws`
                        : `ws://${pcLinkedDevice?.ip}:${pcLinkedDevice?.port}/common/ws`;

                // ws = new WebSocket(wsUrl);

                // 添加自定义headers
                // WebSocket 不支持直接设置请求头,需要在URL中添加参数
                if (protocol === 'wss:') {
                    const urlParams = new URLSearchParams();
                    Object.entries(headers).forEach(([key, value]) => {
                        urlParams.append(key, String(value));
                    });
                    const wsUrlWithHeaders = `${wsUrl}?${urlParams.toString()}`;
                    ws = new WebSocket(wsUrlWithHeaders);
                } else {
                    ws = new WebSocket(wsUrl);
                }
            } else if (mobileLinkedDevice) {
                // 添加移动端特定参数
                wsPayload.globalVariable = {
                    ...wsPayload.globalVariable,
                    platform: mobileLinkedDevice ? mobileLinkedDevice.platform.toString() : null,
                    deviceId: mobileLinkedDevice?.udId,
                    token: mobileConnectSonicToken,
                };
                // ws = new WebSocket(`${protocol}//**************:20317/common/ws`);
                if (protocol === 'wss:') {
                    const url = `${protocol}//enginegateway.waimai.test.sankuai.com/common/ws?X-Target-Host=**************&X-Target-Port=20317`;
                    ws = new WebSocket(`${url}`);
                } else {
                    ws = new WebSocket(`${protocol}//**************:20317/common/ws`);
                }
            } else {
                ws = new WebSocket(`${protocol}//127.0.0.1:20317/common/ws`);
                // ws = new WebSocket(`wss://127.0.0.1:20318/common/ws`);
            }
            setWsConnection(ws);

            // 添加心跳间隔变量
            // let heartbeatInterval: NodeJS.Timeout;

            ws.onopen = () => {
                console.log('WebSocket连接已建立');
                setHistoryTaskList([]);
                ws.send(JSON.stringify(wsPayload));
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                // console.log('收到消息:', data);
                dealWsResMessage(data);
            };

            ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                message.error('连接出错，请确认设备状态');
            };

            ws.onclose = (event) => {
                console.log('WebSocket连接已关闭', {
                    code: event.code,
                    reason: event.reason,
                    wasClean: event.wasClean,
                    timestamp: new Date().toISOString(),
                });

                // 根据关闭代码输出具体原因
                switch (event.code) {
                    case 1000:
                        console.log('正常关闭');
                        break;
                    case 1001:
                        console.log('终端离开，可能是服务端或客户端离线');
                        break;
                    case 1002:
                        console.log('协议错误');
                        break;
                    case 1003:
                        console.log('收到不能接受的数据类型');
                        break;
                    case 1005:
                        console.log('没有状态码');
                        break;
                    case 1006:
                        console.log('异常关闭，可能是网络问题');
                        break;
                    case 1007:
                        console.log('数据格式不符合要求');
                        break;
                    case 1008:
                        console.log('违反策略');
                        break;
                    case 1009:
                        console.log('消息太大');
                        break;
                    case 1010:
                        console.log('客户端请求协商失败');
                        break;
                    case 1011:
                        console.log('服务器遇到未知问题');
                        break;
                    case 1015:
                        console.log('TLS 握手失败');
                        break;
                    default:
                        console.log(`未知关闭代码: ${event.code}`);
                }
                resetState();
            };
        } catch (error) {
            console.error('运行出错:', error);
            message.error('运行失败');
        }
    };

    const handleDebug = (isReGenerate: boolean = false) => {
        initializeWebSocket(2, { debugLines: props.debugIdList?.join(',') || '' }, isReGenerate);
    };

    const handleCollect = (key: 'open' | 'start' | 'end' | 'close') => {
        if (!key) {
            return;
        }

        window.electron?.ipcRenderer?.invoke('createElementCaptureView', {
            url: 'https://www.baidu.com',  // 需要采集的页面 不传默认google
            xcSpaceId: spaceId
        });
    };
    const headerBtnRef = useRef(null);
    const handleRecord = () => {
        if (!isRecording) {
            if (!isDeviceConnected) {
                //message.warning(`请连接设备后录制${isElectron() ? '或打开浏览器录制' : ''}`);
                headerBtnRef?.current?.setMode?.('record');
                headerBtnRef?.current?.openDeviceDrawer?.();
                return;
            }
            //分别处理
            if (linkedDeviceType === 'mobile') {
                // 手机
                // 触发录制
                headerBtnRef?.current?.triggerRecord(true, linkedDeviceType);
            } else {
                message.warning('产品设计中，敬请期待');
                return;
            }
        } else {
            headerBtnRef?.current?.triggerRecord(false, linkedDeviceType);
        }
    };

    const handleNext = () => {
        if (wsConnection) {
            wsConnection.send(
                JSON.stringify({
                    uuid: recordId,
                    taskCaseId: currentRunNode.taskCaseId,
                    eventEnum: 1, //1-下一步 2-重试 3-继续 4-终止
                }),
            );
        }
    };

    const handleContinue = () => {
        if (wsConnection) {
            wsConnection.send(
                JSON.stringify({
                    uuid: recordId,
                    taskCaseId: currentRunNode.taskCaseId,
                    eventEnum: 3, // 3-继续
                }),
            );
            setShowDebugControls(false);
        }
    };

    const fetchLatestOrchestration = useCallback(async () => {
        try {
            const response = await Api.getOrchestration({
                id,
                type: DATA_TYPE,
            });

            if (response.code === 0 && response.data) {
                // 更新节点数据
                // 这里需要通过 props 传递一个更新函数来更新父组件的状态
                if (props.onOrchestrationUpdate) {
                    props.onOrchestrationUpdate({
                        content: response.data.content,
                        xml: response.data.xml,
                    });
                }
            } else {
                console.error('获取编排信息失败:', response.msg);
            }
        } catch (error) {
            console.error('获取编排信息出错:', error);
            message.error('获取最新节点数据失败');
        }
    }, [id, DATA_TYPE]);

    const dealWsResMessage = (packet: { type: any; data: any }) => {
        function preCommand(params: {
            canExecute: any;
            taskCaseId?: string;
            recordId?: string;
            historyTaskList: HistoryTask[];
        }) {
            //   console.log('dealWsResMessage------->', params);
            if (params.taskCaseId) {
                setExecutingNodeIds([params.taskCaseId]);
            }
            if (params.recordId) {
                setRecordId(params.recordId);
            }
            // 添加 historyTaskList 的存储
            if (params.historyTaskList) {
                setHistoryTaskList(params.historyTaskList);
            }
            setCurrentRunNode(params);
            if (!params.canExecute) {
                setShowDebugControls(true);
            } else {
                setShowDebugControls(false);
            }
        }

        function allfinish(params: { status: number }) {
            message.success('执行结束');
            setExecutingNodeIds([]);
            setIsExecuting(false);
            if (params.status === 3) {
                setShowDebugControls(false);
            } else {
                handleStop();
            }
        }
        function executeFinish(params: { status: number; errorMsg: string }) {
            message.error(params.errorMsg);
            setIsExecuting(false);
        }
        switch (packet.type) {
            case 1:
                setIsExecuting(true);
                break;
            case 2:
                preCommand(packet.data);
                break;
            case 3:
                resetState();
                allfinish(packet.data);
                // 执行结束后获取最新节点数据
                fetchLatestOrchestration();
                break;
            case 4:
                executeFinish(packet.data);
                fetchLatestOrchestration(); // 执行异常后就结束，获取最新节点数据
                break;
            default:
                break;
        }
    };

    const isElectron = (): boolean => {
        // 检查 window.electron (通过 bridge.js 注入)
        if (window?.electron) return true;

        // 检查 process (仅在 Electron 环境中存在)
        // if (window?.process?.type === 'renderer') return true;

        // 检查 navigator.userAgent
        if (navigator.userAgent.toLowerCase().includes('electron/')) return true;

        return false;
    };

    const handleHistory = async () => {
        console.log('handleHistory');
        setHistoryRecordDrawerOpen(true);
        setHistoryRecordLoading(true);
        const res = await getCommandHistoryLog({
            bizType: DATA_TYPE,
            bizId: id,
            pageNum: 1,
            pageSize: 10,
        });
        if (res.code === 0) {
            setHistoryRecordInfo(res?.data?.info || {});
            setHistoryRecordList(res?.data?.logs || []);
        }
        setHistoryRecordLoading(false);
    };

    const getStatusColor = (status: number) => {
        return status === 1 ? 'processing' : status === 2 ? 'success' : 'error';
    };

    const getStatusText = (status: number) => {
        return status === 1 ? '执行中' : status === 2 ? '成功' : '失败';
    };

    const getCommandStatusText = (status: number) => {
        return status === 1 ? '成功' : status === 2 ? '执行中' : '失败';
    };

    // 递归渲染子命令
    const renderChildCommands = (commands: any[]): CollapseProps['items'] => {
        return commands.map((command) => ({
            key: command.id,
            label: (
                <div className="flex items-center justify-between gap-2">
                    <div className="flex items-center gap-2 text-[12px] leading-6">
                        {command.showText ? (
                            <Tooltip overlayInnerStyle={{ fontSize: '10px', lineHeight: '10px' }} title={command.showText}>
                                <span className="inline-block max-w-[300px] truncate">{command.showText}</span>
                            </Tooltip>
                        ) : (
                            <span>{command.showText || '默认名称'}</span>
                        )}
                        {command.commandStatus && (
                            <Tag className="text-xs" color={getStatusColor(command.commandStatus)}>
                                {getCommandStatusText(command.commandStatus)}
                            </Tag>
                        )}
                        <span className="text-[10px] text-gray-500">{command?.duration || 0}ms</span>
                        {command.errorCode && <span className="text-[10px] text-red-500">{command.errorCode}</span>}
                    </div>
                    {command.logDetail && (
                        <Tooltip
                            trigger={['hover']}
                            title={command.logDetail}
                            placement="left"
                            overlayInnerStyle={{
                                fontSize: '10px',
                                maxHeight: '500px',
                                overflowY: 'auto',
                            }}
                            autoAdjustOverflow
                            getPopupContainer={() => document.getElementById('editor-history-record-drawer') as HTMLElement}>
                            <a className="cursor-pointer text-[10px] text-blue-500 hover:underline">日志详情</a>
                        </Tooltip>
                    )}
                </div>
            ),
            children: (
                <div>
                    {command.screenshotS3Url && (
                        <img
                            style={{
                                margin: '0 auto',
                            }}
                            className={`w-[100%] ${historyRecordInfo.deviceType === 2 ? 'max-w-[500px] text-center' : ''}`}
                            src={command.screenshotS3Url}
                            alt="截图"
                        />
                    )}
                    {command.childrenCommands && command.childrenCommands.length > 0 && (
                        <Collapse className="mt-4" items={renderChildCommands(command.childrenCommands)} />
                    )}
                </div>
            ),
        }));
    };

    const items: CollapseProps['items'] = renderChildCommands(historyRecordList);

    // 获取应用列表
    const fetchApps = async (search: string, type: string) => {
        try {
            setLoading(true);
            const res = await pageList({
                pageNum: 1,
                pageSize: 20,
                name: search,
                type: type,
                source: 'rpaSpace',
            });
            setApps(res.data?.data?.list || []);
        } catch (error) {
            console.error('获取应用列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const debouncedFetch = debounce((search: string, type: string) => {
        fetchApps(search, type);
    }, 300);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchText(value);
        debouncedFetch(value, activeTab);
    };

    const handleTabChange = (type: string) => {
        setActiveTab(type);
        fetchApps(searchText, type);
    };

    //禁止当前页面的左滑 回退事件
    usePreventSwipeBack();

    const dropdownContent = (
        <div className="custom-dropdown rounded-md bg-white py-2 shadow-lg" style={{ width: 300 }}>
            <div className="mx-2">
                <div className="mb-3 flex overflow-hidden rounded-md border text-sm">
                    {[
                        { key: 'myDevelop', label: '开发的' },
                        { key: 'myFavorite', label: '收藏的' },
                    ].map(({ key, label }, index) => (
                        <React.Fragment key={key}>
                            <SwitchTabButton
                                key={key}
                                tabKey={key}
                                active={activeTab === key}
                                label={label}
                                onChange={handleTabChange}
                            />
                            {index === 0 && <div className="my-auto h-4 w-[1px] bg-gray-200" />}
                        </React.Fragment>
                    ))}
                </div>
                <Input placeholder="搜索应用" value={searchText} onChange={handleSearch} className="mb-2 w-full" />
            </div>
            <div className="max-h-[400px] overflow-y-auto">
                {loading ? (
                    <div className="flex justify-center py-4">
                        <Spin />
                    </div>
                ) : (
                    apps.map((app) => (
                        <div
                            key={app.applicationId}
                            className="flex cursor-pointer items-center px-3 py-2 hover:bg-gray-100"
                            onClick={() => {
                                onAppChange(app.applicationId);
                                setDropdownOpen(false);
                            }}>
                            {app.icon ? (
                                <img
                                    src={app.icon}
                                    alt={app.name}
                                    className="mr-2 h-5 w-5 rounded-sm object-cover"
                                    onError={(e) => {
                                        // 图片加载失败时的处理
                                        (e.target as HTMLImageElement).style.display = 'none';
                                    }}
                                />
                            ) : (
                                <div className="mr-2 flex h-5 w-5 items-center justify-center rounded-sm bg-gray-100 text-xs text-gray-500">
                                    {app.name.charAt(0)}
                                </div>
                            )}
                            <span className="flex-1 truncate">{app.name}</span>
                        </div>
                    ))
                )}
            </div>
        </div>
    );

    // 添加 mitt 事件监听
    useEffect(() => {
        if (!props.currentXml) return;
        // console.log('RenderHeader useEffect -->', props.currentXml);
        // 监听 on-run 事件
        emitter.on('on-run', (data: any) => {
            if (data.type === 'RE_GENERATE') {
                handleDebug(true);
            }
        });

        // 组件卸载时移除事件监听
        return () => {
            emitter.off('on-run');
        };
    }, [props.currentXml]); // 空依赖数组，只在组件挂载时添加监听

    useEffect(() => {
        if (!props.content) return;
        editorContent = props.content?.content;
    }, [props.content]);

    const publishApp = () => {
        setReleaseVersionOpen(true);
    };
    const ActionSuccess = () => {
        setReleaseVersionOpen(false);
    };

    return (
        <div className="flex items-center justify-between bg-white px-2">
            {/* 左侧内容 */}
            <div className="flex items-center p-2">
                <i
                    className="aieicon aieicon-arrow-left-s-line ml-1 mr-2 cursor-pointer text-2xl"
                    onClick={() => (window.location.href = '/app/workspace')}></i>
                <Dropdown
                    open={dropdownOpen}
                    onOpenChange={(open) => {
                        setDropdownOpen(open);
                        if (open) {
                            fetchApps('', activeTab);
                        }
                    }}
                    dropdownRender={() => dropdownContent}
                    trigger={['click']}>
                    <div className="group flex w-[350px] cursor-pointer items-center rounded-md px-2 py-1 transition-colors hover:bg-gray-100">
                        {basicinfo?.icon ? (
                            <img
                                src={basicinfo.icon}
                                alt={basicinfo.name}
                                className="mr-2 h-6 w-6 rounded-sm object-cover"
                                onError={(e) => {
                                    (e.target as HTMLImageElement).style.display = 'none';
                                }}
                            />
                        ) : (
                            <div className="mr-2 flex h-6 w-6 items-center justify-center rounded-sm bg-gray-100 text-sm text-gray-500">
                                {basicinfo?.name?.charAt(0)}
                            </div>
                        )}
                        <span className="mr-auto truncate text-base font-medium">
                            {basicinfo?.name}
                            <Tag className="ml-2 text-xs">{DATA_TYPE === 1 ? '应用编排' : '业务指令编排'}</Tag>
                        </span>
                        <DownOutlined className="ml-2 text-xs opacity-0 transition-opacity group-hover:opacity-100" />
                    </div>
                </Dropdown>
            </div>

            {/* 中间的工具栏按钮 - 添加 flex-1 和居中对齐 */}
            <div className="flex flex-1 justify-center">
                <ToolbarButtons
                    isElectron={isElectron()}
                    onSave={props.onSave}
                    // onRun={handleRun}
                    onDebug={() => handleDebug(false)}
                    onStop={handleStop}
                    onNext={handleNext}
                    onHistory={handleHistory}
                    onContinue={handleContinue}
                    onCollect={handleCollect}
                    onRecord={handleRecord}
                    isRecording={isRecording}
                    recordBtnText={isRecording ? '停止录制' : '智能录制'}
                    disabled={isExecuting}
                    isDebugging={isDebugging}
                    showDebugControls={showDebugControls}
                />
                <HeaderBtn ref={headerBtnRef} />
            </div>

            {/* 添加一个空的右侧div来保持对称 */}
            <div className="flex w-[400px] flex-row-reverse p-2">
                {/* 这里放置与左侧相同宽度的占位内容 */}
                {/* <Button
            className="text-xs font-medium"
            onClick={handleHistory}
            type="text"
            icon={<i className="aieicon aieicon-share-line"></i>}>
            分享
          </Button> */}
                <Button
                    className="text-xs font-medium h-6"
                    onClick={publishApp}
                    type="text"
                    icon={<i className="aieicon aieicon-send-plane-line"></i>}>
                    发版
                </Button>
            </div>

            <Drawer
                title="历史记录"
                open={historyRecordDrawerOpen}
                onClose={() => setHistoryRecordDrawerOpen(false)}
                width={800}>
                <>
                    {historyRecordList.length === 0 ? (
                        <div className="flex h-full justify-center">暂无历史记录</div>
                    ) : (
                        <>
                            <div className="mb-4">
                                <div className="mb-2 flex items-center gap-2">
                                    <div className="text-bold text-md">{historyRecordInfo?.logTitle || '执行日志'}</div>
                                    {historyRecordInfo?.recordStatus && (
                                        <Tag color={getStatusColor(historyRecordInfo.recordStatus)}>
                                            {getStatusText(historyRecordInfo.recordStatus)}
                                        </Tag>
                                    )}
                                </div>
                                <div className="flex items-center gap-2 text-[11px] text-gray-500">
                                    <span>
                                        运行时间：{dayjs(historyRecordInfo?.startTime).format('YYYY-MM-DD HH:mm:ss')} -{' '}
                                        {dayjs(historyRecordInfo?.endTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </span>
                                    <span>耗时：{historyRecordInfo?.duration || 0}ms</span>
                                    <span>运行设备：{historyRecordInfo?.deviceId || '暂无'}</span>
                                    <span>未运行指令数：{historyRecordInfo?.notRunCaseNum || 0}</span>
                                </div>
                            </div>
                            <div>
                                <Collapse items={items} defaultActiveKey={['']} />
                            </div>
                            <div className="flex justify-center">
                                <Spin spinning={historyRecordLoading} />
                            </div>
                        </>
                    )}
                </>
            </Drawer>

            <PublishAppModal
                releaseVersionOpen={releaseVersionOpen}
                setReleaseVersionOpen={setReleaseVersionOpen}
                applicationId={id}
                version={basicinfo?.version}
                ActionSuccess={ActionSuccess}
                spaceId={spaceId}></PublishAppModal>
        </div>
    );
};

export default RenderHeader;

