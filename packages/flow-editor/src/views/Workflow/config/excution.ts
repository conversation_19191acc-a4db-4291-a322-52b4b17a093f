import { create } from 'zustand'

export interface HistoryTask {
  taskId: number;
  varMap: Array<{
    curValue: any;
    dataTypeName: string;
    name: string;
  }>;
  output: {
    curValue: any;
    dataTypeName: string;
    name: string;
  };
  runSuccess: boolean;
  errorCode?: number;
  errorMsg?: string;
  stackTrace?: string;
}

interface ExecutionState {
  executingNodeIds: string[]
  taskCaseIds: Record<string, number>
  historyTaskList: HistoryTask[];
  isExecuting: boolean;

  setExecutingNodeIds: (nodeIds: string[]) => void
  setTaskCaseId: (nodeId: string, taskCaseId: number) => void
  setHistoryTaskList: (list: HistoryTask[]) => void;
  setIsExecuting: (status: boolean) => void;
  resetState: () => void
}

export const useExecutionStore = create<ExecutionState>((set) => ({
  executingNodeIds: [],
  taskCaseIds: {},
  historyTaskList: [],
  isExecuting: false,

  setHistoryTaskList: (list) => {
    // console.log('setHistoryTaskList-->', list);
    set({ historyTaskList: list })
  },
  setExecutingNodeIds: (nodeIds) => set({ executingNodeIds: nodeIds }),
  
  setTaskCaseId: (nodeId, taskCaseId) => {
    // console.log('setTaskCaseId-->', nodeId, taskCaseId);
    set((state) => ({
      taskCaseIds: {
        ...state.taskCaseIds,
        [nodeId]: taskCaseId
      }
    }))
  },
  setIsExecuting: (status) => set({ isExecuting: status }),
  resetState: () => set({
    executingNodeIds: [],
    taskCaseIds: {},
    historyTaskList: [],
    isExecuting: false
  })
}))
