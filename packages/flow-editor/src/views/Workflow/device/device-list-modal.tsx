import React, { useEffect, useState,useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Drawer, Flex, Input, Pagination, Row, Select, Space, Switch, Tooltip,Cascader,Tag,Empty,message, Tabs } from 'antd';
import { connectPc, disconnectPc, getMobileDeviceList, getPcDeviceList } from '@api/device-api';
import { useDeviceStore } from './store';
import { DeviceStatus,DeviceStatusMap,DeviceStatusOptions, OS,IDevicePcListItem,DeviceMobileStatusOptions,IDeviceMobileListItem,IDeviceParams,DeviceMobilePlatform,DeviceMobileStatus ,DeviceMobileStatusMap, IDeviceCard} from './types';
import _ from 'lodash';
import historyTag from '@assets/historyTag.png';



export const DeviceListDrawer: React.FC<{
    btnProps?:React.ComponentProps<typeof Button>,
    defaultBtnText?: string,
    drawerTitle?: string | React.ReactNode,
    extra?: React.ReactNode,
    handleLinkedBtn?:(id:string,deviceInfo:IDevicePcListItem | IDeviceMobileListItem,tabType: 'mobile' | 'pc') => void,  // 选中设备回调
    showBtn?:boolean,   //是否展示btn
    handleCloseDrawer?:() => void // 关闭抽屉回调 不展示btn时必传
    type?: 'mobile' | 'pc', //移动端or PC端
    mode?: 'record' | 'screen', // 模式 录制 还是 设备投屏 维护这个变量，以便可以进行做兼容
    customCardComponent?: React.ReactNode, // 自定义卡片组件
    customTabComponent?: React.ReactNode, // 自定义tab组件
    historyLinkedId?:string, // 历史连接的id
    currentLinkedId?:string // 当前连接的id
}> = ({btnProps,    
    defaultBtnText = '选择设备',
    drawerTitle = '添加设备',
    handleLinkedBtn,
    showBtn = true,
    handleCloseDrawer,
    type,
    mode = 'screen',
    customCardComponent,
    customTabComponent,
    historyLinkedId,
    currentLinkedId
}) => {
  const [open, setOpen] = useState(false);
  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
    handleCloseDrawer?.();
  };

  useEffect(()=>{
    if(!showBtn){
      showDrawer();
    }
  },[showBtn])

  return (
    <>
      {showBtn && <Space>
        <Button 
            type="primary" 
            onClick={showDrawer}
            {...btnProps}
            >
          {defaultBtnText}
        </Button>
      </Space>}
      <Drawer
        title={drawerTitle}
        width={'80%'}
        onClose={onClose}
        open={open}
        // closeIcon={null}
        // extra={
        //   extra
        // }
        destroyOnClose
      >
        {/* <Tabs 
            items={[
            {label: '云手机', key: 'mobile'},
            {label: '云电脑', key: 'pc'}]} 
            defaultActiveKey={tabType} 
            onChange={(key) => {
                console.log('tabType ===',key);
                setTabType(key as 'mobile' | 'pc');
            }}
            type='card'
            // tabBarExtraContent={<Space>
            //     <Button onClick={onClose}>Cancel</Button>
            //     <Button type="primary" onClick={onClose}>
            //       OK
            //     </Button>
            //   </Space>}
        /> */}
        <Devices 
        key={type} 
        handleCloseDrawer={onClose} 
        handleLinkedBtn={handleLinkedBtn} 
        type={type} 
        mode={mode} 
        customCardComponent={customCardComponent} 
        customTabComponent={customTabComponent}
        historyLinkedId={historyLinkedId}
        currentLinkedId={currentLinkedId}
        />
      </Drawer>
    </>
  );
};

const Devices:React.FC<{
    handleLinkedBtn?:(id:string,deviceInfo:IDevicePcListItem | IDeviceMobileListItem,tabType: 'mobile' | 'pc') => void,
    handleCloseDrawer?:() => void,
    type?:string,
    mode?: 'record' | 'screen',
    customCardComponent?: React.ReactNode,
    customTabComponent?: React.ReactNode,
    historyLinkedId?:string,
    currentLinkedId?:string
}> = ({handleLinkedBtn,handleCloseDrawer,type = 'mobile',mode = 'screen',customCardComponent,customTabComponent,historyLinkedId,currentLinkedId}) => {
    const [autoRefresh,setAutoRefresh] = useState(true);  //是否自动刷新的默认值
    const [allDeviceList, setAllDeviceList] = useState<(IDevicePcListItem | IDeviceMobileListItem)[]>([]); // 存储所有设备数据
    const [filteredDeviceList, setFilteredDeviceList] = useState<(IDevicePcListItem | IDeviceMobileListItem)[]>([]); // 过滤后的设备列表
    const [displayDeviceList, setDisplayDeviceList] = useState<(IDevicePcListItem | IDeviceMobileListItem)[]>([]); // 当前页显示的设备列表
    const [params,setParams] = useState<IDeviceParams>({
        status: type === 'mobile' ? [String(DeviceMobileStatus.ONLINE)] : [String(DeviceStatus.ONLINE)]
    });
    const [loading,setLoading] = useState<boolean>(false);
    const [currentPage,setCurrentPage] = useState(1);
    const [pageSize,setPageSize] = useState(16);
    // 获取所有设备数据
    const fetchAllData = useCallback(async () => {
        setLoading(true);
        try {
            if(type === 'mobile'){
                const res = await getMobileDeviceList();
                const mapDeviceStatus = (item: IDeviceMobileListItem) => ({
                    ...item,
                    status: item.status=== "ONLINE" ? DeviceMobileStatus.ONLINE : (item.status === 'TESTING' || item.status === 'DEBUGGING' ? DeviceMobileStatus.BUSY : DeviceMobileStatus.OFFLINE)
                });
                setAllDeviceList(res?.data?.data?.map(mapDeviceStatus) || []);
            } else {
                const res = await getPcDeviceList({
                    misId: 'baiyujiao',
                    isLatest: true
                });
                setAllDeviceList((res?.data?.data || []).filter((item:IDevicePcListItem) => item.status !== DeviceStatus.DirtyData));
            }
        } catch (error) {
            console.error('Failed to fetch device list:', error);
        }
        setLoading(false);
    }, [type]);

    // 过滤数据
    const filterDevices = useCallback(() => {
        let filtered = [...allDeviceList];
       // console.log('params',params);
        if (!params) {
            setFilteredDeviceList(filtered);
            return;
        }

        if (type === 'mobile') {
            // 移动设备过滤
            if (params.manufacturer && params.manufacturer.length > 0) {
                filtered = filtered.filter((device) => {
                    const mobileDevice = device as IDeviceMobileListItem;
                    return params.manufacturer?.includes(mobileDevice.manufacturer);
                });
            }

            if (params?.platform && params.platform?.length > 0) {
                filtered = filtered.filter((device) => {
                    const mobileDevice = device as IDeviceMobileListItem;
                    return params.platform?.includes(mobileDevice.platform);
                });
            }

            if (params.chiNameOrUdId) {
                const searchTerm = params.chiNameOrUdId.toLowerCase();
                filtered = filtered.filter((device) => {
                    const mobileDevice = device as IDeviceMobileListItem;
                    return mobileDevice.chiName?.toLowerCase().includes(searchTerm) || 
                           mobileDevice.udId?.toLowerCase().includes(searchTerm);
                });
            }
        } else {
            // PC设备过滤
            if (params.os !== undefined) {
                filtered = filtered.filter((device) => {
                    const pcDevice = device as IDevicePcListItem;
                    return pcDevice.os === Number(params.os);
                });
            }

            if (params.aliasOrUuid) {
                const searchTerm = params.aliasOrUuid.toLowerCase();
                filtered = filtered.filter((device) => {
                    const pcDevice = device as IDevicePcListItem;
                    return pcDevice.alias?.toLowerCase().includes(searchTerm) || 
                           pcDevice.deviceUuid?.toLowerCase().includes(searchTerm);
                });
            }
        }

        // 通用过滤条件
        if (params?.version && params?.version?.length > 0) {
            filtered = filtered.filter(device => params?.version?.includes(device.version));
        }
        // 状态过滤
        if (params?.status && params?.status?.length > 0) {
            filtered = filtered.filter(device => {
                const deviceStatus = type === 'mobile' ? (device as IDeviceMobileListItem).status : (device as IDevicePcListItem).status;
                return params.status?.includes(String(deviceStatus));
            });
        }

        setFilteredDeviceList(filtered);
    }, [allDeviceList, params, type]);

    // 分页处理
    const updateDisplayList = useCallback(() => {
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        setDisplayDeviceList(filteredDeviceList.slice(startIndex, endIndex));
    }, [currentPage, pageSize, filteredDeviceList]);

    // 初始加载和自动刷新
    useEffect(() => {
        fetchAllData();
    }, [fetchAllData]);

    // 参数变化时过滤数据
    useEffect(() => {
        filterDevices();
    }, [filterDevices, params]);

    // 过滤结果或分页变化时更新显示列表
    useEffect(() => {
        updateDisplayList();
    }, [updateDisplayList]);

    const handleAutoRefresh = (checked: boolean) => {
        let lastFetchTime = Date.now();
        const timer = checked ? setInterval(() => {
            const now = Date.now();
            if (now - lastFetchTime >= 5000) {
                fetchAllData();
                lastFetchTime = now;
            }
        }, 1000) : null;

        return () => {
            if (timer) {
                clearInterval(timer);
            }
        };
    }
    useEffect(()=>{
        const cleanup = handleAutoRefresh(autoRefresh);
        return () => {
            cleanup();
        }
    },[autoRefresh])

    return <div className="container mx-auto px-4 pb-2">
        {customCardComponent}
        <Flex justify="space-between" align="center" className="mb-2 mt-6">
            {customTabComponent}
            <Space>
                {type === 'mobile' && 
                    <Select 
                        placeholder="请选择制造商" 
                        style={{ width: '200px' }}
                        allowClear
                        mode="multiple"
                        maxTagCount="responsive"
                        options={(useDeviceStore.getState().mobileManufacturerOptions || []).map((item:string) => ({
                            value: item,
                            label: item
                        }))}
                        onChange={(value) => {
                            setParams(prev => ({...prev, manufacturer: value}))
                        }}
                    >
                    </Select>
                }
                <Cascader 
                    options={type === 'mobile' ? useDeviceStore.getState().mobileDeviceAndVersion : useDeviceStore.getState().osAndVersionOptions} 
                    onChange={(value, options) => {
                        if(value && value.length > 0) {
                            setParams(prev => ({
                                ...prev,
                                os: type === 'mobile' ? undefined : value.map(v => Number(v[0]) as OS),
                                platform: type === 'mobile' ? value.map(v => Number(v[0]) as DeviceMobilePlatform) : undefined,
                                version: value.length > 1 ? value.map(v => v[1]) : undefined
                            }))
                        }else{
                            setParams(prev => ({...prev, os: [],platform: [], version: []}))
                        }
                    }}
                    allowClear
                    maxTagCount="responsive"
                    multiple
                    placeholder="请选择系统版本" 
                    style={{ width: '200px' }}
                />
                <Select 
                    placeholder="请选择状态" 
                    options={type === 'mobile' ? [...DeviceMobileStatusOptions] : [...DeviceStatusOptions]} 
                    style={{ width: '200px' }}
                    onChange={(value) => {
                        setParams(prev => ({...prev, status: value}))
                    }}
                    defaultValue={type === 'mobile' ? [DeviceMobileStatus.ONLINE] : [String(DeviceStatus.ONLINE)]}
                    mode="multiple"
                    maxTagCount="responsive"
                    allowClear
                />
                <Input 
                    placeholder="请输入设备名称或型号" 
                    style={{ width: '200px' }}
                    onChange={_.debounce((e) => {
                        setParams(prev => ({...prev, aliasOrUuid: type === 'mobile' ? undefined : e.target.value,chiNameOrUdId: type === 'pc' ? undefined : e.target.value}))
                    }, 500)}
                    allowClear
                />
            </Space>
        </Flex>
      
        {
            filteredDeviceList.length === 0 && <Empty  description="暂无数据"  />
        } 
        <Row gutter={[16, 16]} className="mb-4">
            {displayDeviceList.map((device) => {
                const isMobileDevice = type === 'mobile';
                const deviceProps = isMobileDevice ? {
                    id: String(device.id),
                    handleLinkedBtn,
                    handleCloseDrawer,
                    brand: (device as IDeviceMobileListItem).manufacturer,
                    model: (device as IDeviceMobileListItem).chiName,
                    os: (device as IDeviceMobileListItem).platform,
                    resolution: (device as IDeviceMobileListItem).size,
                    serial: (device as IDeviceMobileListItem).udId,
                    version: device.version,
                    status: device.status,
                    item: device as IDeviceMobileListItem,
                    mode,
                    historyLinkedId,
                    currentLinkedId
                } : {
                    id: String(device.id),
                    handleLinkedBtn,
                    handleCloseDrawer,
                    deviceName: (device as IDevicePcListItem).alias,
                    ip: (device as IDevicePcListItem).ip,
                    os: (device as IDevicePcListItem).os === OS.Linux ? 'Linux' : 
                        (device as IDevicePcListItem).os === OS.Windows ? 'Windows' : 'MacOS',
                    osCode: (device as IDevicePcListItem).os,
                    engineVersion: (device as IDevicePcListItem).engineVersionNo,
                    port: (device as IDevicePcListItem).port,
                    item: device as IDevicePcListItem,
                    status: device.status as DeviceStatus,
                    mode,
                    historyLinkedId,
                    currentLinkedId
                };

                return (
                    <Col span={6} key={device.id}>
                        {isMobileDevice ? (
                            <MobileDeviceCard {...(deviceProps as React.ComponentProps<typeof MobileDeviceCard>)} />
                        ) : (
                            <PCDeviceCard {...(deviceProps as React.ComponentProps<typeof PCDeviceCard>)} />
                        )}
                    </Col>
                );
            })}
        </Row>
        {filteredDeviceList.length > 0 && <Row justify="space-between" align="middle">
            <Space>
                <span>自动刷新</span>
                <Switch defaultChecked={true}  onChange={(checked) => {
                    setAutoRefresh(checked);
                    message.success(checked ? '已开启自动刷新' : '已关闭自动刷新');
                }}/>
            </Space>
            <Space>
                <Pagination 
                    total={filteredDeviceList.length}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 个设备`}
                    pageSize={pageSize}
                    current={currentPage}
                    locale={{
                        jump_to: '跳至',
                        page: '页',
                        page_size: '页',
                        items_per_page: '/页'
                    }}
                    onChange={(page, pageSize) => {
                        setCurrentPage(page);
                        setPageSize(pageSize);
                    }}
                    pageSizeOptions={[16, 32, 64, 128]}
                />
            </Space>
        </Row>}
       
</div>
}

const PCDeviceCard: React.FC<{
    id: string | number,
    deviceName?: string,
    ip?: string,
    os?: string,
    osCode?: OS,
    engineVersion?: string,
    port?: string | number,
    handleLinkedBtn?: (id: string,deviceInfo:IDevicePcListItem,tabType: 'mobile' | 'pc') => void,
    handleCloseDrawer?: () => void,
    status: DeviceStatus,
    item?: IDevicePcListItem,
    mode?: 'record' | 'screen',
    historyLinkedId?:string,
    currentLinkedId?:string
}> = ({
    id, deviceName, ip, os, engineVersion, port, handleLinkedBtn, item, status,osCode,mode,historyLinkedId,currentLinkedId
}) => {
    return (
        <Badge.Ribbon style={{marginTop:'-6px'}} text={<span className='text-xs font-bold'>{os}</span>} placement='start' color={
            osCode === OS.Windows ? 'gray' :
            osCode === OS.MacOS ? 'blue' :
            osCode === OS.Linux ? 'orange' : ''
        }>
            <Card size="small" className="h-full w-full relative">
                {/* {String(id) === historyLinkedId && (
                    <img 
                        src={historyTag} 
                        alt="上次连接" 
                        className="absolute -top-[6px] -right-[6px] w-[60px] h-[60px]"
                    />
                )} */}
                <div className="font-bold mt-3 mb-2 flex items-center gap-2 h-8">
                    <div className="truncate">{deviceName}</div>
                </div>
                <div className="flex items-center gap-1" style={{fontWeight:400,color:'#646971',lineHeight:'24px'}}>
                    <div className='w-[80px]'>IP地址：</div>
                    <div className='truncate flex-1' style={{color:'#111925'}}>{ip}</div>
                </div>
                <div className="flex items-center gap-1" style={{fontWeight:400,color:'#646971',lineHeight:'24px'}}>
                    <div className='w-[80px]'>系统版本：</div>
                    <div className='truncate flex-1' style={{color:'#111925'}}>{os}</div>
                </div>
                <div className="flex items-center gap-1" style={{fontWeight:400,color:'#646971',lineHeight:'24px'}}>
                    <div className='w-[80px]'>引擎版本：</div>
                    <div className='truncate flex-1' style={{color:'#111925'}}>{engineVersion}</div>
                </div>
                <div className="flex items-center gap-1" style={{fontWeight:400,color:'#646971',lineHeight:'24px'}}>
                    <div className='w-[80px]'>引擎端口：</div>
                    <div className='truncate flex-1' style={{color:'#111925'}}>{port}</div>
                </div>
                <Flex gap={2} justify='space-between' align='center'  className='mt-2 h-8'>
                    <Badge key={id} color={
                        status === DeviceStatus.ONLINE ? 'green' :
                        status === DeviceStatus.BUSY ? 'yellow' :
                        status === DeviceStatus.OFFLINE ? 'red' : ''
                    } text={<span className='text-sm h-[30px]' style={{fontWeight:400,color:'#111925'}}>{DeviceStatusMap[status]}</span>    } />
                    <Space>
                        {String(status) === String(DeviceStatus.ONLINE) && <Tooltip title={mode === 'record' ? '连接设备，开始录制' : '连接设备'}>
                            <Button 
                                size='small'
                                onClick={async ()=>{
                                    const res = await connectPc({id: String(id), mis: 'zhoudong11'});
                                    if (res.data.code === 0) {
                                        //handleCloseDrawer?.(); 
                                        handleLinkedBtn?.(String(id),item as IDevicePcListItem, 'pc');
                                        useDeviceStore.setState({pcLinkedDevice: item});
                                        useDeviceStore.setState({mobileLinkedDevice: null});
                                    }else{
                                        message.error(res.data.msg || '连接失败');
                                    }
                                }}
                                type="primary"
                                ghost
                                disabled={String(status) !== String(DeviceStatus.ONLINE)}
                            >
                                {mode === 'record' ? '开始录制' : '连接设备'}
                            </Button>
                        </Tooltip>}
                        {String(status) === String(DeviceStatus.BUSY) && <Tooltip title={'断开连接'}>
                            <Button
                                size='small'
                                onClick={async ()=>{
                                    const res = await disconnectPc({id: String(id), mis: 'zhoudong11'});
                                    if (res?.data?.code === 0) {
                                        useDeviceStore.setState({isRecording: false});
                                        useDeviceStore.setState({pcLinkedDevice: null});
                                    } else {
                                        message.error(res?.data?.msg || '断开连接失败');
                                    }
                                }}
                                danger
                                disabled={String(status) !== String(DeviceStatus.BUSY)}
                            >
                                {'断开连接'}
                            </Button>
                        </Tooltip>}
                    </Space>
                </Flex>
            </Card>
        </Badge.Ribbon>
    );
};

const formatBrand = (brand:string | undefined) => {
    if(!brand) return {text: '',img: ''};
    const text = brand?.toLowerCase() === 'huawei' ? 'HUAWEI' :
    brand?.toLowerCase() === 'xiaomi' ? 'XIAOMI' :
    brand?.toLowerCase() === 'google' ? 'Google' : 
    brand?.toLowerCase() === 'samsung' ? 'SAMSUNG' :
    brand?.toLowerCase() === 'apple' ? 'APPLE' :
    brand?.toLowerCase() === 'realme' ? 'Realme' :
    brand?.toLowerCase() === 'meizu' ? 'MEIZU' : 
    brand?.toLowerCase() === 'oppo' ? 'OPPO' :
    brand?.toLowerCase() === 'vivo' ? 'VIVO' : 
    brand?.toLowerCase() === 'honor' ? 'HONOR' : "PHONE"
    //下载品牌图标

    // const img = brand?.toLowerCase() === 'huawei' ? 'https://img.ai-flow.com/huawei.png' :
    // brand?.toLowerCase() === 'xiaomi' ? 'https://img.ai-flow.com/xiaomi.png' :
    // brand?.toLowerCase() === 'google' ? 'https://img.ai-flow.com/google.png' :
    // brand?.toLowerCase() === 'samsung' ? 'https://img.ai-flow.com/samsung.png' :
    // brand?.toLowerCase() === 'apple' ? 'https://img.ai-flow.com/apple.png' :
    // brand?.toLowerCase() === 'realme' ? 'https://img.ai-flow.com/realme.png' :
    // brand?.toLowerCase() === 'meizu' ? 'https://img.ai-flow.com/meizu.png' :
    // brand?.toLowerCase() === 'oppo' ? 'https://img.ai-flow.com/oppo.png' :
    // brand?.toLowerCase() === 'vivo' ? 'https://img.ai-flow.com/vivo.png' :
    // brand?.toLowerCase() === 'honor' ? 'https://img.ai-flow.com/honor.png' : ''

    return {text,img:""}
}

const MobileDeviceCard: React.FC<{
    id: string,
    brand?: string,  //制造商
    model?: string, //名称
    os?: DeviceMobilePlatform,   //系统
    resolution?: string,  //分辨率
    serial?: string,  //序列号
    item?: IDeviceMobileListItem,
    version?: string, //版本
    handleLinkedBtn?: (id: string,deviceInfo:IDeviceMobileListItem,tabType: 'mobile' | 'pc') => void,
    handleCloseDrawer?: () => void,
    status: string,
    mode?: 'record' | 'screen',
    historyLinkedId?:string,
    currentLinkedId?:string
}> = ({
    id, brand, model, os, resolution, serial, item, version, handleLinkedBtn, status,mode,historyLinkedId,currentLinkedId
}) => {
    return (
        <Badge.Ribbon style={{marginTop:'-6px'}} text={
            <Space>
                {/* <img src={formatBrand(brand)?.img} alt={brand} className='w-4 h-4' /> */}
                <span className='text-xs font-bold'>{formatBrand(brand)?.text}</span>
            </Space>
        } placement='start'  color={
            brand?.toLowerCase() === 'huawei' ? 'red' :
            brand?.toLowerCase() === 'xiaomi' ? 'orange' :
            brand?.toLowerCase() === 'google' ? 'green' :
            brand?.toLowerCase() === 'samsung' ? 'gray' :
            brand?.toLowerCase() === 'apple' ? 'yellow' :
            brand?.toLowerCase() === 'realme' ? 'gray' :
            brand?.toLowerCase() === 'meizu' ? 'blue' :
            brand?.toLowerCase() === 'oppo' ? 'purple' :
            brand?.toLowerCase() === 'vivo' ? 'pink' :
            brand?.toLowerCase() === 'honor' ? 'orange' : ''
        }>
            <Card size="small" className="h-full w-full relative">
                {/* {String(id) === historyLinkedId && (
                    <img 
                        src={historyTag} 
                        alt="上次连接" 
                        className="absolute -top-[6px] -right-[6px] w-[60px] h-[60px]"
                    />
                )} */}
                <div className="text-[16px] mt-3 mb-2 h-8 flex items-center gap-1" style={{fontWeight:600}}>
                    <div className="truncate">{model || brand}</div>
                </div>
                <div className="h-6 flex items-center gap-1" style={{fontWeight:400,color:'#646971',lineHeight:'22px'    }}>
                    <div className='w-[100px]'>系统版本：</div>
                    <div className='truncate flex-1 text-[#111925]'>{`${DeviceMobilePlatform[os!]} ${version}`}</div>
                </div>
                <div className="h-6 flex items-center gap-1" style={{fontWeight:400,color:'#646971',lineHeight:'22px'}}>
                    <div className='w-[100px]'>屏幕分辨率：</div>
                    <div className='truncate flex-1 text-[#111925]'>{resolution}</div>
                </div>
                <div className="h-6 flex items-center gap-1" style={{fontWeight:400,color:'#646971',lineHeight:'22px'}}>
                    <div className='w-[100px]'>序列号：</div>
                    <div className='truncate flex-1 text-[#111925]'>{serial}</div>
                </div>

                <Flex justify='space-between' className='h-8 mt-2'>
                    <Badge key={id} color={
                        status === DeviceMobileStatus.ONLINE ? 'green' :
                        status === DeviceMobileStatus.OFFLINE ? '#8c8c8c' : 'yellow'
                    } text={<span className='text-sm h-[30px]' style={{fontWeight:400,color:'#111925'}}>{DeviceMobileStatusMap?.[status as DeviceMobileStatus] || ""}</span>    } />

                    <Tooltip title={mode === 'record' ? '连接设备，开始录制' : '连接设备'}>
                        <Button 
                            size='small'
                            onClick={()=>{
                                setTimeout(()=>{
                                    //handleCloseDrawer?.(); 
                                    handleLinkedBtn?.(String(id),item as IDeviceMobileListItem, 'mobile');
                                    useDeviceStore.setState({mobileLinkedDevice: item});
                                    useDeviceStore.setState({pcLinkedDevice: null});
                                },800)
                            }}
                            disabled={status !== DeviceMobileStatus.ONLINE}
                            type="primary"
                            ghost
                        >
                            {mode === 'record' ? '开始录制' : '连接设备'}
                        </Button>
                    </Tooltip>
                </Flex>
            </Card>
        </Badge.Ribbon>
    );
};

