export function executeApi(params: any, host: any): Promise<any>;
export function elementTreeApi(params: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function saveApi(params: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function getCaseEditorApi(params: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function getNextCommandApi(data: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function getNextCommandV2Api(data: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function uploadImageApi(data: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function uploadVirtualElement(data: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function recommendElement(data: any): Promise<any>;
export function recommendGlobalVariableApi(params: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function bindOrUnbindGlobalVariableApi(params: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function getCommandDetailInfoApi(params: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function saveCustomCommandApi(params: any, url: any): Promise<import("axios").AxiosResponse<any, any> | undefined>;
export function getElementPageListApi(params: any): Promise<any>;
export function getPageElementTreeList(appId: any): Promise<import("axios").AxiosResponse<any, any>>;
export function getIframeElementById(id: any): Promise<any>;
