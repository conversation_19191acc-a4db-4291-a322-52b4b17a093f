import type { FC } from 'react'
import React, { useCallback } from 'react'
import { produce } from 'immer'
import type { Authorization as AuthorizationPayloadType } from '../../types'
import { APIType, AuthorizationType } from '../../types'
import RadioGroup from './radio-group'
import { But<PERSON>, Modal } from 'antd'

type Props = {
  payload: AuthorizationPayloadType
  onChange: (payload: AuthorizationPayloadType) => void
  isShow: boolean
  onHide: () => void
}

const Field = ({ title, isRequired, children }: { title: string; isRequired?: boolean; children: JSX.Element }) => {
  return (
    <div>
      <div className='leading-8 text-[13px] font-medium text-gray-700'>
        {title}
        {isRequired && <span className='ml-0.5 text-[#D92D20]'>*</span>}
      </div>
      <div>{children}</div>
    </div>
  )
}

const Authorization: FC<Props> = ({
  payload,
  onChange,
  isShow,
  onHide,
}) => {
  const [tempPayload, setTempPayload] = React.useState<AuthorizationPayloadType>(payload)
  const handleAuthTypeChange = useCallback((type: string) => {
    const newPayload = produce(tempPayload, (draft: AuthorizationPayloadType) => {
      draft.type = type as AuthorizationType
      if (draft.type === AuthorizationType.apiKey && !draft.config) {
        draft.config = {
          type: APIType.basic,
          api_key: '',
        }
      }
    })
    setTempPayload(newPayload)
  }, [tempPayload, setTempPayload])

  const handleAuthAPITypeChange = useCallback((type: string) => {
    const newPayload = produce(tempPayload, (draft: AuthorizationPayloadType) => {
      if (!draft.config) {
        draft.config = {
          type: APIType.basic,
          api_key: '',
        }
      }
      draft.config.type = type as APIType
    })
    setTempPayload(newPayload)
  }, [tempPayload, setTempPayload])

  const handleAPIKeyOrHeaderChange = useCallback((type: 'api_key' | 'header') => {
    return (e: React.ChangeEvent<HTMLInputElement>) => {
      const newPayload = produce(tempPayload, (draft: AuthorizationPayloadType) => {
        if (!draft.config) {
          draft.config = {
            type: APIType.basic,
            api_key: '',
          }
        }
        draft.config[type] = e.target.value
      })
      setTempPayload(newPayload)
    }
  }, [tempPayload, setTempPayload])

  const handleConfirm = useCallback(() => {
    onChange(tempPayload)
    onHide()
  }, [tempPayload, onChange, onHide])
  return (
    <Modal
      title={'鉴权'}
      open={isShow}
      onCancel={onHide}
    >
      <div>
        <div className='space-y-2'>
          <Field title={'鉴权类型'}>
            <RadioGroup
              options={[
                { value: AuthorizationType.none, label: '无' },
                { value: AuthorizationType.apiKey, label: 'API-Key' },
              ]}
              value={tempPayload.type}
              onChange={handleAuthTypeChange}
            />
          </Field>

          {tempPayload.type === AuthorizationType.apiKey && (
            <>
              <Field title={'API 鉴权类型'}>
                <RadioGroup
                  options={[
                    { value: APIType.basic, label: '基础' },
                    { value: APIType.bearer, label: 'Bearer' },
                    { value: APIType.custom, label: '自定义' },
                  ]}
                  value={tempPayload.config?.type || APIType.basic}
                  onChange={handleAuthAPITypeChange}
                />
              </Field>
              {tempPayload.config?.type === APIType.custom && (
                <Field title={'Header'} isRequired>
                  <input
                    type='text'
                    className='w-full h-8 leading-8 px-2.5  rounded-lg border-0 bg-gray-100  text-gray-900 text-[13px]  placeholder:text-gray-400 focus:outline-none focus:ring-1 focus:ring-inset focus:ring-gray-200'
                    value={tempPayload.config?.header || ''}
                    onChange={handleAPIKeyOrHeaderChange('header')}
                  />
                </Field>
              )}

              <Field title={'PI Key'} isRequired>
                <input
                  type='text'
                  className='w-full h-8 leading-8 px-2.5  rounded-lg border-0 bg-gray-100  text-gray-900 text-[13px]  placeholder:text-gray-400 focus:outline-none focus:ring-1 focus:ring-inset focus:ring-gray-200'
                  value={tempPayload.config?.api_key || ''}
                  onChange={handleAPIKeyOrHeaderChange('api_key')}
                />
              </Field>
            </>
          )}
        </div>
        <div className='mt-6 flex justify-end space-x-2'>
          <Button onClick={onHide}>{'取消'}</Button>
          <Button type='primary' onClick={handleConfirm}>{'保存'}</Button>
        </div>
      </div>
    </Modal>
  )
}
export default React.memo(Authorization)
