import { Required<PERSON>abel, StyledTable } from '@components/index';
import XbotIcon, { XbotIconAddLine, XbotIconDeleteBinLine, XbotIconInfoFill } from '@components/XbotIcon';
import { InlineErrorFormItem } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';

import NoToolIcon from '@assets/no-tool.svg'
import JsonEditorParseDrawer from '@components/JsonEditorParseDrawer';
import { styled } from 'styled-components';
import { Button, Form, Input, Select, Switch, TableProps, Tooltip } from 'antd';
import { FormInstance, RuleObject } from 'antd/es/form';
import { nanoid } from 'nanoid';
import { InputVarType, ParamKey } from '../../types';
import RuleExtraPopover, { DEFAULT_EXTRA_VALUE } from './RuleExtraPopover';
import { VAR_TYPE_OPTIONS } from './constants';
import { StartNodeInputVar } from './types';

const NO_OPTION_PARAMS = [ParamKey.PC_DEVICE, ParamKey.MOBILE_DEVICE];
export interface ParamsTableProps {
  initialValue?: readonly StartNodeInputVar[];
  readonly?: boolean;
}

const noExtraTypes: InputVarType[] = [InputVarType.file, InputVarType.image, InputVarType.date];

const getDuplicatedValueValidator = (fieldName: string, from: FormInstance, message?: string) => {
  const validator: RuleObject['validator'] = (rule, value, callback) => {
    if (!value) {
      return callback();
    }
    //获取行id
    let rowId = rule.field.split('.')[0];

    let tableData = from.getFieldsValue(true);
    // rest为除了rowId行的其他行
    let { [rowId]: k, ...rest } = tableData;
    // 其他行的fieldName对应的值
    let values = Object.values(rest).map((v) => v[fieldName]);

    if (values.includes(value)) {
      return callback(message || '字段不能重复');
    } else {
      return callback();
    }
  };
  return validator;
};

const StyledInlineErrorFormItem = styled(InlineErrorFormItem)`
  & .ant-col {
    min-height: unset;
  }
`;

const READONLY_FIELDS = ['user_input', 'pc_device', 'mobile_device'];

const ParamsTable = React.forwardRef((props: ParamsTableProps, ref) => {
  const { initialValue, readonly = false } = props;
  // 编辑单元格时，dataSource不变，不会触发表格刷新操作，数据存储在form中。仅新增/删除行时需要操作dataSource
  const [dataSource, setDataSource] = useState<StartNodeInputVar[]>([]);
  const [form] = Form.useForm();

  useEffect(() => {
    const data = initialValue?.map((item) => ({ ...item, id: nanoid() })) || [];
    setDataSource(data);
    const formData = data.map((item) => ({ name: item.id, value: item }));
    form.setFields(formData);
  }, [initialValue, form]);

  const handleExtraPopoverClose = (rowId: string, values: any) => {
    const rowData = form.getFieldValue(rowId);
    const newData = { ...rowData, ...values };
    form.setFieldValue(rowId, newData);
  };

  React.useImperativeHandle(ref, () => {
    const vavalidateFieldsReturnValue = async () => {
      await form?.validateFields();
      const tableData = form.getFieldsValue(true);
      const newData = dataSource.map((item) => {
        const id = item.id;
        const rowData = tableData[id!];
        return rowData || item;
      });
      return newData;
    };

    const addUserInput = async () => {
      if (dataSource.length === 0 || dataSource[0].name !== 'user_input') {
        const id = nanoid();
        const data = {
          label: '用户的原始对话消息',
          name: 'user_input',
          required: true,
          type: InputVarType.text,
          description: '在智能体中与模型对话调用工作流时由系统带入',
        };
        setDataSource([{ id, ...data }, ...dataSource]);
        form.setFieldValue(id, data);
      }
    };

    return {
      vavalidateFieldsReturnValue,
      addUserInput,
    };
  }, [form, dataSource]);

  const handleDelete = (id: string) => {
    const newData = dataSource.filter((item) => item.id !== id);
    setDataSource(newData);
    const formData = form.getFieldsValue(true);
    delete formData[id];
    form.setFieldsValue(formData);
  };

  const handleAdd = () => {
    const rowId = nanoid();
    const newData = {
      id: rowId,
      label: '',
      name: '',
      required: false,
      type: InputVarType.text,
      description: '',
      defaultValue: undefined,
      placeholder: undefined,
    };
    setDataSource([...dataSource, newData]);
    form.setFieldValue(rowId, newData);
  };

  const isReadonly = (field: string) => {
    return READONLY_FIELDS.includes(field) || readonly;
  };

  const onTypeChange = (rowId: string, type: string) => {
    // 重置额外字段弹窗默认值
    const rowData = form.getFieldValue(rowId);
    if (type === 'select') {
      handleExtraPopoverClose(rowId, {
        ...rowData,
        ...DEFAULT_EXTRA_VALUE,
        selectMode: 'single',
        type,
      });
    } else {
      handleExtraPopoverClose(rowId, {
        ...rowData,
        ...DEFAULT_EXTRA_VALUE,
        type,
      });
    }
  };

  const columns: TableProps['columns'] = [
    {
      title: (
        <div className="flex items-center gap-1">
          <RequiredLabel>参数Key</RequiredLabel>
          <XbotIconInfoFill tip="参数Key仅允许包含字母、数字或下划线" />
        </div>
      ),
      key: 'name',
      dataIndex: 'name',
      render(value, record, index) {
        return isReadonly(record.name) ? (
          record.name
        ) : (
          <StyledInlineErrorFormItem
            errorType="popover"
            name={[record.id, 'name']}
            validateTrigger={['onBlur', 'onSubmit']}
            rules={[
              { required: true, whitespace: true, message: '此项是必填项' },
              { max: 50, message: '最长为 50 个字符' },
              { pattern: /^[A-Za-z0-9_-]+$/, message: '参数Key仅允许包含字母、数字或下划线' },
              { validator: getDuplicatedValueValidator('name', form, '名称不允许重复') },
            ]}>
            <Input placeholder="请输入" disabled={readonly} />
          </StyledInlineErrorFormItem>
        );
      },
      width: '280px',
    },
    {
      title: <RequiredLabel>参数中文名</RequiredLabel>,
      key: 'label',
      dataIndex: 'label',
      render(value, record, index) {
        return isReadonly(record.name) ? (
          record.label
        ) : (
          <StyledInlineErrorFormItem
            errorType="popover"
            name={[record.id, 'label']}
            validateTrigger={['onBlur', 'onSubmit']}
            rules={[
              { required: true, whitespace: true, message: '此项是必填项' },
              { max: 50, message: '最长为 50 个字符' },
              { validator: getDuplicatedValueValidator('label', form, '中文名不允许重复') },
            ]}>
            <Input placeholder="请输入" disabled={readonly} />
          </StyledInlineErrorFormItem>
        );
      },
      width: '240px',
    },
    {
      title: <RequiredLabel>描述</RequiredLabel>,
      dataIndex: 'description',
      render(value, record, index) {
        return isReadonly(record.name) ? (
          record.description
        ) : (
          <StyledInlineErrorFormItem
            errorType="popover"
            name={[record.id, 'description']}
            validateTrigger={['onBlur', 'onSubmit']}
            rules={[
              { required: true, whitespace: true, message: '此项是必填项' },
              { max: 200, message: '最长为 200 个字符' },
            ]}>
            <Input placeholder="请输入" disabled={readonly} />
          </StyledInlineErrorFormItem>
        );
      },
      width: '400px',
    },
    {
      title: <RequiredLabel>类型</RequiredLabel>,
      dataIndex: 'type',
      width: '160px',
      render(value, record, index) {
        return (
          <div className="flex items-center gap-1">
            {isReadonly(record.name) ? (
              record.type
            ) : (
              <>
                <StyledInlineErrorFormItem
                  className="flex-1"
                  errorType="popover"
                  name={[record.id, 'type']}
                  validateTrigger={['onBlur', 'onSubmit']}
                  rules={[
                    { required: true, whitespace: true, message: '此项是必填项' },
                    { max: 50, message: '最长为 50 个字符' },
                  ]}>
                  <Select
                    options={VAR_TYPE_OPTIONS}
                    allowClear={false}
                    placeholder="请选择"
                    className="!w-full"
                    disabled={readonly}
                    onChange={(type) => {
                      onTypeChange(record.id, type);
                    }}
                  />
                </StyledInlineErrorFormItem>
                <StyledInlineErrorFormItem dependencies={[[record.id, 'type']]}>
                  {(form: FormInstance) => {
                    const rowData = form.getFieldValue(record.id);
                    const type = rowData.type;
                    switch (type) {
                      case 'json':
                      case 'array':
                        return (
                          <JsonEditorParseDrawer
                            trigger={
                              <Tooltip title="设置参数结构">
                                <div>
                                  <XbotIcon className="xgpticon-xbotfont-node-tree" />
                                </div>
                              </Tooltip>
                            }
                            type={type}
                            value={rowData?.schema}
                            onChange={(value) => {
                              handleExtraPopoverClose(record.id, { schema: value });
                            }}
                          />
                        );
                      default:
                        return <></>;
                    }
                  }}
                </StyledInlineErrorFormItem>
              </>
            )}
          </div>
        );
      },
    },
    {
      title: '必填',
      dataIndex: 'required',
      render(value, record, index) {
        return isReadonly(record.name) ? (
          <Switch disabled value={record.required} />
        ) : (
          <StyledInlineErrorFormItem dependencies={[[record.id, 'type']]}>
            {(form: FormInstance) => {
              const type = form.getFieldValue([record.id, 'type']);
              return (
                type !== 'boolean' && (
                  <StyledInlineErrorFormItem name={[record.id, 'required']} errorType="popover">
                    <Switch disabled={isReadonly(record.name)} />
                  </StyledInlineErrorFormItem>
                )
              );
            }}
          </StyledInlineErrorFormItem>
        );
      },
      width: '64px',
    },
    {
      title: '操作',
      width: '88px',
      align: 'center',
      render: (value, record, index) => {
        if (NO_OPTION_PARAMS.includes(record.name)) {
          return <></>;
        }
        return (
          <>
            <StyledInlineErrorFormItem dependencies={[[record.id, 'type']]} className="inline-flex">
              {(form: FormInstance) => {
                const type = form.getFieldValue([record.id, 'type']);
                return (
                  <span
                    style={{
                      visibility: record.name === 'user_input' || noExtraTypes.includes(type) ? 'hidden' : undefined,
                    }}>
                    <RuleExtraPopover rowId={record.id} readonly={readonly} form={form} onTypeChange={onTypeChange} />
                  </span>
                );
              }}
            </StyledInlineErrorFormItem>

            {!readonly && <XbotIconDeleteBinLine className="ml-2" onClick={() => handleDelete(record.id)} />}
          </>
        );
      },
    },
  ];

  return (
    <>
      <Form form={form}>
        <StyledTable
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          rowKey="id"
          locale={{
            emptyText: (
              <div className="flex w-full flex-col items-center justify-center gap-3">
                <img src={NoToolIcon} className="logo" alt="Vite logo" />
                <div className="text-primary">暂无数据</div>
              </div>
            ),
          }}
          className="w-full"
        />
      </Form>

      {!readonly && (
        <Button onClick={handleAdd} className="mt-2">
          <XbotIconAddLine /> 参数
        </Button>
      )}
    </>
  );
});

export default ParamsTable;
