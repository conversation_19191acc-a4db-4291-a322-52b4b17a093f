/**
 * 切换到指定索引窗口
 */
export const SCHEMA = {
  type: "object",
  properties: {
    collapse: {
      type: "void",
      "x-component": "FormTab",
      "x-component-props": {
        formTab: "{{formTab}}",
      },
      properties: {
        tab1: {
          type: "void",
          "x-component": "FormTab.TabPane",
          "x-component-props": {
            tab: "等待页面加载",
            // style: { fontWeight: "bold", color: "black" },
          },
          properties: {
            title1: {
              type: "void",
              "x-component": "FormItem",
              "x-component-props": {
                children: "小标题",
                style: { fontWeight: "bold", marginBottom: "10px" },
              },
            },
            input1: {
              type: "string",
              title: "输入框",
              "x-decorator": "FormItem",
              "x-component": "Input",
              required: true,
              "x-component-props": {
                allowClear: true,
              },
            },
            CustomInput: {
              type: "string",
              title: "输入框",
              required: true,
              "x-decorator": "FormItem",
              "x-component": "CustomInput",
              "x-decorator-props": {
                tooltip: <b style={{ 
                  color: "purple",
                 }}>123</b>,
              },
            },
          },
        },
        tab2: {
          type: "void",
          "x-component": "FormTab.TabPane",
          "x-component-props": {
            tab: "异常处理",
          },
          properties: {
            section3: {
              type: "void",
              "x-component": "FormItem",
              "x-component-props": {
                style: { marginBottom: "10px" },
              },
              properties: {
                title3: {
                  type: "void",
                  "x-component": "div",
                  "x-component-props": {
                    children: "小标题3",
                    style: { fontWeight: "bold", marginBottom: "10px" },
                  },
                },
                name: {
                  type: "void",
                  title: "姓名",
                  "x-decorator": "FormItem",
                  "x-component": "Space",
                  "x-decorator-props": {
                    asterisk: true,
                    feedbackLayout: "none",
                  },
                  properties: {
                    firstName: {
                      type: "string",
                      "x-decorator": "FormItem",
                      "x-component": "Input",
                      required: true,
                    },
                    lastName: {
                      type: "string",
                      "x-decorator": "FormItem",
                      "x-component": "Input",
                      required: true,
                    },
                  },
                },
              },
            },
            // section4已被移除
          },
        },
      },
    },
  },
};
