import { Tabs, TabsProps, Spin } from 'antd';
import { styled } from 'styled-components';

import { XgptInput } from '@components/index';
import { SuggestionProps } from '@tiptap/suggestion';
import { nanoid } from 'nanoid';
import { useEffect, useState } from 'react';
import { BUILT_IN_NODES, BuiltInNode, aggregateNodesByKey } from './built-in-node';
import { getSiderIcon } from '../nodes/common';
import { getRpaList } from '../../../../api/workflow/WorkflowController'

const StyledTabs = styled(Tabs)`
  &.ant-tabs .ant-tabs-tab {
    padding: 8px 0;
  }
`;

const TabContent = styled.div`
  height: 360px;
  overflow-y: auto;
`;

const Root = styled.div`
  ::-webkit-scrollbar {
    /* --bar-width: 5px;
    width: var(--bar-width);
    height: var(--bar-width); */
    background: transparent;
    width: 0;
    height: 0;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    background-clip: content-box;
    border: 1px solid transparent;
  }
`;


const CategoryTitle = ({ title }: { title: string }) => {
  return <div className="flex h-8 items-center px-3 text-xs text-secondary">{title}</div>;
};

interface TabContentProps extends SuggestionProps {
  search: string;
}

const BaseNodeList = (props: TabContentProps) => {
  const { editor, range, search } = props;
	// const [baseNodeList, setBaseNodeList] = useState<BuiltInNode[]>();
  const baseNodeList = BUILT_IN_NODES
  const filteredNodes = search
    ? baseNodeList.filter(
        (node) => node.title.includes(search) || node.spelling?.includes(search.toLocaleLowerCase()),
      )
    : baseNodeList;
  const aggregateNodes = aggregateNodesByKey(filteredNodes, 'category');

	// useAsyncEffect(async () => {
	// 	const nodeList = await getWorkflowNodeTypeMap();
	// 	if (Array.isArray(nodeList)) {
	// 		setBaseNodeList(nodeList);
	// 	} else {
	// 		console.error("Error: nodeList is not an array");
	// 	}
  // }, []);

  const onClick = (item: BuiltInNode) => {
    const data = {
      type: item.type,
      attrs: {
        nodeId: nanoid(),
      },
    };
    if (['ifNode', 'elseifNode', 'elseNode', 'forNode'].includes(item.type!)) {
      Object.assign(data, {
        content: [
          {
            type: 'paragraph',
          },
        ],
      });
    }

    editor.chain().focus().deleteRange(range).insertContent(data).run();
  };

  return (
    <div>
      {aggregateNodes.map((category) => {
        return (
          <>
            <CategoryTitle key={category.category} title={category.categoryDesc} />
            {category.nodes.map((item) => {
              return (
                <div
                  key={item.title}
                  onClick={() => {
                    onClick(item);
                  }}
                  className="flex h-11 cursor-pointer items-center gap-2 px-3 font-semibold text-primary hover:bg-gray-200">
                  {getSiderIcon(item.type)}
                  {item.title}
                </div>
              );
            })}
          </>
        );
      })}
    </div>
  );
};


const RpaNodeList = (props: TabContentProps) => {
  const [baseNodeList, setBaseNodeList] = useState<BuiltInNode[]>([]);
  const { search, editor, range } = props;
  const filteredNodes = search
    ? baseNodeList.filter((node) => node.title.toLowerCase().includes(search) || node.spelling?.includes(search))
    : baseNodeList;
  const aggregateNodes = aggregateNodesByKey(filteredNodes, 'category');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchRpaNodeList = async () => {
      setIsLoading(true);
      const res = await getRpaList(1);
      const nodeList = res.data.map((item: any) => {
        return { ...item, categoryDesc: item.categoryDesc, title: item.skillName };
      });
      console.log(nodeList);
      if (Array.isArray(nodeList)) {
        setBaseNodeList(nodeList);
      } else {
        console.error('Error: nodeList is not an array');
      }
      setIsLoading(false);
    };

    fetchRpaNodeList();
  }, []);

  if (isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <Spin />
      </div>
    );
  }

  const onClick = (item: BuiltInNode) => {
     const { id, title, skillName, type, unionId, showDesc, icon, commandType } = item;
     const data = {
       type: 'rpaNode',
       attrs: {
         nodeId: nanoid(),
         tag: unionId,
         showDesc,
         data: { id, title, type, skillName, icon, commandType},
       },
     };
    
    editor.chain().focus().deleteRange(range).insertContent(data).run();
  };

 return (
   <div>
     {aggregateNodes.map((category) => {
       return (
         <>
           <CategoryTitle key={category.category} title={category.categoryDesc} />
           {category.nodes.map((item) => {
             return (
               <div
                 key={item.id}
                 onClick={() => {
                   onClick(item);
                 }}
                 className="flex h-11 cursor-pointer items-center gap-2 px-3 font-semibold text-primary hover:bg-gray-200">
                 {getSiderIcon('xbotCommand')}
                 {item.title}
               </div>
             );
           })}
         </>
       );
     })}
   </div>
 );
};

const NodeListPopover = (props: SuggestionProps) => {
  useEffect(() => {
    const navigationKeys = ['ArrowUp', 'ArrowDown', 'Enter'];
    const onKeyDown = (e: KeyboardEvent) => {
      if (navigationKeys.includes(e.key)) {
        e.preventDefault();
        return false;
      }
      if (e.key === 'Escape') {
        props.editor.chain().focus().run();
        return true;
      }
    };
    document.addEventListener('keydown', onKeyDown);
    return () => {
      document.removeEventListener('keydown', onKeyDown);
    };
  }, []);

  const [search, setSearch] = useState('');

  const tabItems: TabsProps['items'] = [
    {
      key: 'base',
      label: '基础节点',
      children: (
        <TabContent>
          <BaseNodeList {...props} search={search} />
        </TabContent>
      ),
    },
    {
      key: 'rpa',
      label: 'rpa指令',
      children: (
        <TabContent>
          <RpaNodeList {...props} search={search} />
        </TabContent>
      ),
    },
  ];

  return (
    <Root
      id="slash-command"
      className="z-50 w-[360px] rounded-md border border-stone-200 bg-white p-3 shadow-md transition-all"
      onClick={(e) => e.stopPropagation()}>
      <XgptInput
        placeholder="请输入"
        className="w-full bg-white"
        defaultValue={search}
        onChange={({ target }) => {
          setSearch(target.value);
        }}
      />
      <StyledTabs
        defaultActiveKey="base"
        items={tabItems}
        // onChange={onChange}
        tabBarStyle={{ margin: '0 0 8px 0' }}
      />
    </Root>
  );
};

export default NodeListPopover;
