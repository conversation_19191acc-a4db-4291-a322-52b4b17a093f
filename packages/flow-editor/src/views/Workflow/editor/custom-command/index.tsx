import {
  useState,
  useEffect,
  useCallback,
  useRef,
  useLayoutEffect,
} from "react";
import { Editor, Range, Extension } from "@tiptap/core";
import Suggestion, { SuggestionOptions, SuggestionProps } from "@tiptap/suggestion";
import { React<PERSON>enderer } from "@tiptap/react";
import tippy from "tippy.js";
import { CommandItemProps } from "./command";
import defaultCommands from "./default-command";
import { PluginKey } from "@tiptap/pm/state";
import CommandList from "./command-list";
import { getFilterCommandListByGroupName, getMatchScoreList } from "./util";
import { ConfigProvider } from "antd";

let customCommandList: Array<any> = [];

let keyword = "";

const getSuggestionItems = ({ query }: { query: string }) => {
  // console.log('query', `${query}`)
  keyword = query;
  const list = getMatchScoreList(query, getCustomCommandList());
  return list;
};

// const getSuggestionItemsForCommand = ({ query }: { query: string }) => {
//   keyword = query;
//   return [...customCommandList].filter((item) => {
//     if (typeof query === "string" && query.length > 0) {
//       const search = query.toLowerCase();
//       return (
//         item.title.toLowerCase().includes(search) ||
//         item.searchTerms?.includes(search)
//       );
//     }
//     return true;
//   });
// };

export const updateScrollView = (container: HTMLElement, item: HTMLElement) => {
  const containerHeight = container.offsetHeight;
  const itemHeight = item ? item.offsetHeight : 0;

  const top = item.offsetTop;
  const bottom = top + itemHeight;

  if (top < container.scrollTop) {
    container.scrollTop -= container.scrollTop - top + 5;
  } else if (bottom > containerHeight + container.scrollTop) {
    container.scrollTop += bottom - containerHeight - container.scrollTop + 5;
  }
};

export function getCustomCommandList() {
  return [...defaultCommands, ...customCommandList];
}

const CommandListPopover = ({
  items,
  editor,
  command,
}: SuggestionProps<CommandItemProps>) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [options, setOptions] = useState<CommandItemProps[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  const handleChangeSelectedIndex = (index: number) => {
    setSelectedIndex(index);
  };
  // 处理选中节点
  const selectItem = useCallback(
    (index: number) => {
      const item = options[index];
      if (item) {
        command(item);
      }
    },
    [command, options]
  );

  useEffect(() => {
    setSearchQuery(keyword);
    // const arr = getFilterCommandListByGroupName(items, editor);
    setOptions(items);
    setSelectedIndex(0);
  }, [editor, items]);

  const commandListContainer = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    const container = commandListContainer?.current;

    const item = container?.children[selectedIndex] as HTMLElement;

    if (item && container) updateScrollView(container, item);
  }, [selectedIndex]);
  return (
    <div
      id="novel-custom-command"
      ref={commandListContainer}
      style={{ display: options.length > 0 ? "block" : "none" }}
      className="z-50 h-auto max-h-[330px] w-60 overflow-y-auto rounded-md border border-stone-200 bg-white px-1 py-2 shadow-md transition-all"
    >
      <ConfigProvider
        theme={{
          token: {
            colorBgSpotlight: '#FFFFFF'
          },
        }}
      >
        <CommandList
          options={options}
          selectItem={selectItem}
          selectedIndex={selectedIndex}
          keyword={searchQuery}
          setSelectedIndex={handleChangeSelectedIndex}
        />
      </ConfigProvider>
    </div>
  );
};

const renderItems = () => {
  let component: ReactRenderer | null = null;
  let popup: any | null = null;

  return {
    onStart: (props: SuggestionProps) => {
      component = new ReactRenderer(CommandListPopover, {
        props,
        editor: props.editor,
      });

      // @ts-ignore
      popup = tippy("body", {
        getReferenceClientRect: props.clientRect,
        appendTo: () => document.body,
        content: component.element,
        showOnCreate: true,
        interactive: true,
        trigger: "manual",
        placement: "bottom-start",
        zIndex: 1
      });
    },
    onUpdate: (props: SuggestionProps) => {
      component?.updateProps(props);

      popup &&
        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        });
    },
    onKeyDown: (props: { event: KeyboardEvent }) => {
      if (props.event.key === "Escape") {
        popup?.[0].hide();

        return true;
      }

      // @ts-ignore
      return component?.ref?.onKeyDown(props);
    },
    onExit: () => {
      popup?.[0].destroy();
      component?.destroy();
    },
  };
};

const CustomCommand = (commandList?: Array<any>) => {
  customCommandList = commandList || [];
  const suggestionList = ["/", "#"];

  const suggestionObj: any = {};
  const configObj: any = {};

  suggestionList.forEach((char) => {
    // @ts-ignore
    configObj[char] = {
      items: getSuggestionItems,
      render: renderItems,
    };
    // @ts-ignore
    suggestionObj[char] = {
      char,
      allowSpaces: true,
      startOfLine: false,
      command: ({
        editor,
        range,
        props,
      }: {
        editor: Editor;
        range: Range;
        props: any;
      }) => {
        props.command({ editor, range });
      },
      allowedPrefixes: null,
    } as SuggestionOptions;
  });
  const Command = Extension.create({
    name: "custom-command",
    key: "custom-command",
    addOptions() {
      return suggestionObj;
    },
    addProseMirrorPlugins() {
      return [
        ...suggestionList.map((char) =>
          Suggestion({
            pluginKey: new PluginKey(char),
            editor: this.editor,
            // @ts-ignore
            ...this.options[char],
          })
        ),
      ];
    },
  });
  return Command.configure({
    ...configObj,
  });
};

export default CustomCommand;
