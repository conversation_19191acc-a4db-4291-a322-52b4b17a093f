
const createFormItem = (item) => ({
  label: item.label,
  props: item.paramsName,
  componentType: item.paramsName === 'selectorId' ? 'el-selector' : item.type,
  options: item.options,
});

const processInputList = (list, descShowItem, formItemConfig, params) => {
  let childFormList = [];
  list.forEach((e) => {
    params[e.paramsName] = descShowItem?.[e.paramsName] || e.defaultValue;
    formItemConfig[e.paramsName] = e;
    if (e.type === 'JSON' && e.childInput && e.childInput.length > 0) {
      const childItems = e.childInput.map((child) => {
        params[child.paramsName] = descShowItem?.[e.paramsName] || child.defaultValue;
        formItemConfig[child.paramsName] = child;
        return createFormItem(child);
      });
      childFormList = childFormList.concat(childItems);
    }
    // @ts-ignore
    childFormList.push(createFormItem(e));
  });
  return childFormList;
};

const getFormList = (props) => {
  const formItemConfig = {};
  const params = {};
  let childFormList = [];

  ['parameters', 'responseParameters', 'errorHandleParameters'].forEach((listName) => {
    const list = props[listName];
    if (list && list.length > 0) {
      const items = processInputList(list, props.$descShowItem, formItemConfig, params);
      childFormList = childFormList.concat(items);
    }
  });

  return formItemConfig;
};

export const processItem = (item) => {
  const { inputList, outputList, errorHandleList, name, label, showDesc, commandSimplifyVoMap } = item;
    
  // const inputMap = {};
  const descShow = {};
  const xmlDescShow = {};

  const processParams = (params, parentObj = xmlDescShow) => {
    if (params.type !== 'JSON') {
      descShow[params.paramsName] = params.defaultValue;
      // inputMap[params.paramsName] = params.label;
      parentObj[params.paramsName] = params.defaultValue;
    } else if (params.childInput) {
      parentObj[params.paramsName] = {};
      params.childInput.forEach((childParam) => {
        processParams(childParam, parentObj[params.paramsName]);
      });
    }
  };

  inputList?.forEach((item) => processParams(item));
  outputList?.forEach((item) => processParams(item));
  errorHandleList?.forEach((item) => processParams(item));
  return {
    ...item,
    label,
    desc: showDesc,
    $descShowItem: descShow,
    xmlDescShow,
    // inputMap,
    formItemConfig: getFormList(item),
    commandSimplifyVoMap, // TODO 每个指令对应的精简配置
  };
};
