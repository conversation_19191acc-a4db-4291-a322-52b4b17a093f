import _ from 'lodash';
import { Condition, IfNodeType } from '../nodes/if/types';
import { NodeOutPutVar, NodeType } from '../types';

import { SELECTORID_KEY } from '../common/constants';
// import { XbotCommandNodeType } from '../nodes/xbotCommand/types';
import { isEmpty, isJSON } from './index';
import { flattenNodes, flattenVars, toNodeOutputVars } from './variable';
import { JSONContent } from '@tiptap/react';
import { useExecutionStore } from '../../config/excution';

const nodeXmlKeys: { [key: string]: string[] } = {
  startNode: ['inputList'],
  endNode: ['outputList'],
  llmNode: ['systemPromptValue', 'promptValue', 'maxTokens', 'topP', 'temperature', 'model', 'outStreamKey'],
  kbNode: ['kbId', 'strategyConfigId', 'topK', 'queryStr'],
  messageNode: ['message', 'messageType','inStreamKey'],
  codeNode: ['inputList', 'script', 'language', 'outputList'],
  variableNode: ['inputList'],
  httpNode: ['methodName', 'url', 'headers', 'inputList', 'inputBody', 'timeout', 'stream'],
  rpaNode: [], // rpaNode暂不做指令参数表单field过滤
  [NodeType.Ai]: [], // aiNode暂不做指令参数表单field过滤
  workflowNode: ['workFlowId', 'inputList'],
  forNode: ['list', 'param', 'parallel', 'threadCount'],
};


// const { setTaskCaseId } = useExecutionStore();

function newElementOfNode(node: JSONContent): Element {
  const xmlStr = `<${node.attrs?.tag || node.type} />`;
  const parser = new DOMParser();
  const doc: XMLDocument = parser.parseFromString(xmlStr, 'application/xml');
  return doc.firstChild as Element;
}

function handleObject(obj: Record<string, any>) {
  const setparams: Record<string, any> = {};
  for (const i in obj) {
    if (!isEmpty(obj[i])) {
      if (isJSON(obj[i])) {
        const filteredObj = handleObject(obj[i]);
        if (isEmpty(filteredObj)) {
        } else {
          setparams[i] = filteredObj;
        }
      } else {
        setparams[i] = obj[i];
      }
    }
  }
  return setparams;
}

// 将字符串中所有的XbotVariables. 换成空,例如${XbotVariables.aaa}换成${aaa}
export const replaceXbotVariables = (str: string): string => {
  return str.replace(/xbotVariables\./g, '');
};

// 将字符串中所有的${for.xxx} 换成@{xxx} for循环要求使用变量的方式是@{xxx}
export const replaceForVariables = (str: string): string => {
  return str.replace(/\$\{for.[^}]+\}/g, (match) => {
    const key = match.slice(6, -1); // 去掉 ${for. 和 }
    return `@{${key}}`
  });
};

// 例如map为{ "${jMqobaX5Q1JzmnruzRYzH.result}": "11"} 替换str中的${jMqobaX5Q1JzmnruzRYzH.result}为11
export const replaceVariables = (str: string, map: { [key: string]: string }): string => {
  return str.replace(/\$\{[^}]+\}/g, (match) => {
    const value = map[match];
    if (value) {
      if (typeof value === 'string') {
        return value; // 已经是字符串的话，不能使用JSON.stringify，不然会多引号
      } else {
        return _.escape(JSON.stringify(value));
      }
    }
    return match;
  });
};

// 同上，替换式考虑类型，如果类型为数字，切变量两侧有引号，则去掉引号
export const replaceVariablesWithType = (str: string, map: { [key: string]: any }): string => {
  return str.replace(/&quot;\$\{[^}]+\}&quot;/g, (match) => {
    const key = match.slice(6, -6); // 去掉 &quot; 和 &quot;
    if (map.hasOwnProperty(key)) {
      const value = map[key];
      if (typeof value === 'number') {
        return value.toString();
      } else if (typeof value === 'string') {
        return _.escape(`"${value}"`);
      } else if (typeof value === 'object') {
        return _.escape(`"${JSON.stringify(value)}"`);
      }
    }
    return match;
  });
};

export const removeAllVariablesQuotes = (str: string): string => {
  // 匹配 &quot;${...}&quot; 结构，并去掉前后的 &quot;
  return str.replace(/&quot;\$\{[^}]+\}&quot;/g, (match) => {
    return match.replace(/&quot;/g, '');
  });
};

/**
 * 去掉变量两边的双引号, 例如&quot;${aaa.bbb}&quot; => ${aaa.bbb}
 */
export const removeVariablesQuotes = (str: string, varPath: string): string => {
  const target = '&quot;${'+ varPath + '}&quot;'
  return str.replaceAll(target, `$\{${varPath}}`);
};

/**
 * 给变量两边加上双引号，例如${aaa.bbb} => "${aaa.bbb}"
 */
export const addVariablesQuotes = (str: string, varPath: string): string => {
  // 匹配 &quot;${...}&quot; 结构，并去掉前后的 &quot;
  const regex = new RegExp(varPath, "g");
  return str.replace(regex, (match) => {
    return `&quot;${match}&quot;`
  });
};


export const replaceNonStringVariablesQuotes = (nodes: JSONContent[], xmlStr: string) => {
  const allNodes = flattenNodes(nodes)
  const vars: NodeOutPutVar[] = toNodeOutputVars(allNodes)
  const vars2: any[] = flattenVars(vars)
  // console.log('all vars', vars2)
  
  let resultStr = xmlStr
  // console.log('removeVariablesQuotes before', xmlStr)
  vars2.forEach((item: any) => {
    if (item.type !== 'string') {
      resultStr = removeVariablesQuotes(resultStr, item.path)
    }
  })
  // console.log('removeVariablesQuotes after', resultStr)
  return resultStr
}

// 将用户通过弹窗选择输入的变量格式{{#aaa.bbb#}}替换为标准的变量格式${aaa.bbb}
export const replaceSelectVariables = (str: string): string => {
  return str.replace(/\{\{#[^#]+#\}\}/g, (match) => {
    return `\${${match.slice(3, -3)}}`;
  });
};

const getChildrenExtraAttrs = (children: JSONContent[]) => {
  const childrenExtraAttrs: { [key: string]: object } = {};
  // 过滤非自定义的node类型，例如p节点
  const _children = children.filter((node) => node.attrs?.nodeId);

  // 如果大模型节点后是消息节点，且消息节点仅引用了大模型的输出。则给xml中大模型添加属性outStreamKey，消息节点添加属性inStreamKey，两个key保持一致。这么做是为了实现消息节点可以流式输出大模型的输出
  for (let i = 0; i < _children.length - 1; i++) {
    const currentNode = _children[i];
    const nextNode = _children[i + 1];

    if (currentNode.type === 'llmNode' && nextNode.type === 'messageNode') {
      const llmNodeId = currentNode.attrs.nodeId;
      const message = nextNode.attrs.data?.message;
      // 系统支持两套变量输入模式，一种结构是${aaa.bbb}，另一种结构是{{#aaa.bbb#}}
      const llmOutput = `\${${llmNodeId}.result}`;
      const llmOutput2 = `{{#${llmNodeId}.result#}}`;
      if (message?.trim() === llmOutput || message?.trim() === llmOutput2) {
        Object.assign(childrenExtraAttrs, {
          [llmNodeId]: { outStreamKey: llmNodeId },
          [nextNode.attrs.nodeId]: { inStreamKey: llmNodeId },
        });
      }
    }
  }
  return childrenExtraAttrs;
};

// 为xml element添加属性，如果是json，会将值序列化之后添加到属性
export const addAttrsToXMLElement = (element: Element, attrs: Record<string, any>, includeKeys?: string[]) => {
  if (!attrs) {
    return;
  }

  Object.keys(attrs).forEach((key) => {
    if (!key) {
      return;
    }
    if (includeKeys && !includeKeys.includes(key)) return;

    const value = attrs[key];
    if (!isEmpty(value)) {
      if (isJSON(attrs[key])) {
        const filteredObj = handleObject(attrs[key]);
        const str = JSON.stringify(filteredObj);
        if (!isEmpty(filteredObj)) {
          element.setAttribute(key, str);
        }
      } else {
        element.setAttribute(key, Array.isArray(value) ? JSON.stringify(value) : value);
      }
    }
  });
};

function buildConditionString(condition: Condition): string {
  return `(${condition.left} ${condition.comparisonOperator} ${condition.right})`;
}

// 为xml element添加属性，如果是json，会将值序列化之后添加到属性
export const addAttrsOfIfNode = (element: Element, attrs: IfNodeType) => {
  if (!attrs?.conditions || attrs.conditions.length === 0) {
    // element.setAttribute('condition', 'false');
    return;
  }
  const condition = attrs.conditions.map(buildConditionString).join(` ${attrs.logicalOperator.toUpperCase()} `);
  element.setAttribute('condition', condition);
};
export const addAttrsOfXbotCommandNode = (element: Element, attrs: any) => {
  if (!attrs) {
    return;
  }

  const formData = attrs.formData;
  // const isCustomBusinessTask = element.tagName.startsWith('CustomBusinessTask');

  Object.keys(formData || {}).forEach((key) => {
    if (!key) {
      return;
    }

    let value = formData[key];
    // console.log('value-->', key,value)  
    // 对CustomBusinessTask的变量值添加前缀
    if (typeof value === 'string' && value.match(/\$\{[^}]+\}/)) {
      value = value.replace(/\$\{([^}]+)\}/g, '${xbotOfficialPrivateUniquePrefix#$1}');
    }

    if (key === 'selectorId') { 
      if (value) {
        // console.log('value-->selectorId', value)
        if (value.selectorId !== value.selector) {
          element.setAttribute('selectorId', value.selectorId);
          element.setAttribute('selector', value.selector);
        }else{
          element.setAttribute('selector', value.selector);
        }
      }
    }else if (!isEmpty(value)) {
      if (isJSON(value)) {
        const filteredObj = handleObject(value);
        if (!isEmpty(filteredObj)) {
          element.setAttribute(key, JSON.stringify(filteredObj));
        }
      } else {
        
        element.setAttribute(key, value);
      }
    }
  });
};

// xbot命令添加属性逻辑需要单独处理。formData中所有字段是平铺的，丢失了json结构，通过xmlDescShow还原表单的结构。
// export const addAttrsOfXbotCommandNode = (element: Element, attrs: XbotCommandNodeType) => {
// export const addAttrsOfXbotCommandNode = (element: Element, attrs: any) => {
//   if (!attrs || !attrs.formData || !attrs.xmlDescShow) {
//     return;
//   }

//   const formData = attrs.formData!;
//   const xmlDescShow = attrs.xmlDescShow!;

//   function handleObject(obj: any) {
//     const setparams: Record<string, any> = {};
//     for (const i in obj) {
//       if (isJSON(obj[i])) {
//         const setparamsObj = handleObject(obj[i]);
//         if (isEmpty(setparamsObj)) {
//         } else {
//           setparams[i] = setparamsObj;
//         }
//       } else {
//         if (formData[i] || formData[i] === false || formData[i] === 0) {
//           setparams[i] = formData[i];
//         }
//       }
//     }
//     return setparams;
//   }

//   Object.keys(xmlDescShow).forEach((key) => {
//     if (!key) {
//       return;
//     }
//     if (isJSON(xmlDescShow![key])) {
//       const setparams = handleObject(xmlDescShow![key]);
//       let str = JSON.stringify(setparams);
//       if (!isEmpty(setparams)) {
//         element.setAttribute(key, str);
//       }
//     } else {
//       if (!isEmpty(formData[key])) {
//         if (SELECTORID_KEY.includes(key) && Number.isNaN(parseInt(formData[key]))) {
//         } else {
//           element.setAttribute(key, Array.isArray(formData[key]) ? JSON.stringify(formData[key]) : formData[key]);
//         }
//       }
//     }
//   });
// };
export const getElementOfNode = (
  node: JSONContent | undefined,
  nodeAttrs: Record<string, any>,
  extraNodeAttrs?: object,
  sort: number = 1
): Element | undefined => {
  if (!node || !node.attrs?.nodeId || !nodeAttrs || node.type === NodeType.Start || node.type === NodeType.End || node.type === NodeType.TextNode) {
    return undefined;
  }
  const element = newElementOfNode(node);
  
  const attrs = Object.assign({}, nodeAttrs, extraNodeAttrs);
  
  const isCommented = attrs.isCommented || false;
  console.log('attrs-->', isCommented)
  const nodeType: string = `${node.type}`;
  // console.log('nodeType-->', nodeType);
  const includeKeys = nodeXmlKeys[nodeType] || [];

  if (nodeType === NodeType.Start) {
    element.setAttribute('inputList', 'StartPlaceHolder');
    //通用属性
    element.setAttribute('outKey', node.attrs?.nodeId);
  } else if (node.type === 'ifNode' || node.type === 'elseifNode' || node.type === 'elseNode') {
    addAttrsOfIfNode(element, attrs as IfNodeType);
  } else if (node.type === NodeType.For) {
    addAttrsToXMLElement(element, attrs, includeKeys);
  }
  else if ((node.type === NodeType.XbotCommand || node.type === NodeType.Ai) && !isCommented) {
    addAttrsOfXbotCommandNode(element, attrs);
  }
  else if (node.type === NodeType.Variable) {
    Object.entries(attrs).forEach(([key, value]) => {
      if (key === 'inputList') {
        const newValues = value?.map((row) => {
          const newRow = { ...row }
          if (row.value && (row.type === 'json' || row.type === 'array')) {
            try {
              newRow.value = JSON.parse(row.value) // 如果用户输入的是合法的json，这里需要转换一下
            } catch (error) {
              console.warn('value不是合法的json')
            }
          }
          return newRow
        })
        attrs[key] = newValues
      }
    })
    addAttrsToXMLElement(element, attrs, includeKeys);
    element.setAttribute('outKey', 'customVariables');
    element.setAttribute('nodeId', node.attrs?.nodeId);
  } else {
    addAttrsToXMLElement(element, attrs, undefined); // rpaNode 不做key 过滤
    //通用属性
    element.setAttribute('outKey', node.attrs?.nodeId);
  }
  element.setAttribute('engineExt', `{taskCaseId:${sort}}`);

  let children = node?.content;
  if (children) {
    // 过滤非自定义的node类型，例如p节点
    children = children.filter((child) => child.attrs?.nodeId);

    const childrenExtraNodeAttrs = getChildrenExtraAttrs(children);

    for (const child of children) {
      const childNodeId: string = child.attrs.nodeId;
      const childEle = getElementOfNode(child, child.attrs?.data, childrenExtraNodeAttrs[childNodeId], sort + 1); // 递增 sort
      childEle && element.appendChild(childEle);
    }
  }

  return element;
};

export function getXmlStrOfDoc(xmlDoc: XMLDocument) {
  let xmlStr = '';
  const serializer = new XMLSerializer();
  for (const child of xmlDoc.documentElement.childNodes) {
    xmlStr += serializer.serializeToString(child);
  }
  return xmlStr;
}

// 获取工作流的xml字符串
export const getWorkFlowXml = (editorContent: JSONContent): { xml: string, breakpointsIndices: number[] } => {
  const parser = new DOMParser();
  const doc: XMLDocument = parser.parseFromString(`<doc></doc>`, 'application/xml');
  const rootEle = doc.firstChild as Element;

  let globalSort = 1;
  const breakpointsIndices: number[] = [];

  const getElementWithSort = (
    node: JSONContent | undefined,
    nodeAttrs: Record<string, any>,
    extraNodeAttrs?: object,
  ): Element | undefined => {
    if (!node || !node.attrs?.nodeId || !nodeAttrs || node.type === NodeType.Start || node.type === NodeType.End || node.type === NodeType.TextNode) {
      return undefined;
    }
    const element = newElementOfNode(node);

    const attrs = Object.assign({}, nodeAttrs, extraNodeAttrs);
    const isCommented = attrs.isCommented || false;
    if(isCommented){
      return undefined;
    }

    const nodeType: string = `${node.type}`;
    const includeKeys = nodeXmlKeys[nodeType] || [];

    // 基础属性设置
    if (nodeType === NodeType.Start) {
      element.setAttribute('inputList', 'StartPlaceHolder');
      element.setAttribute('outKey', node.attrs?.nodeId);
    } else if (node.type === 'ifNode' || node.type === 'elseifNode' || node.type === 'elseNode') {
      addAttrsOfIfNode(element, attrs as IfNodeType);
    } else if (node.type === NodeType.For) {
      addAttrsToXMLElement(element, attrs, includeKeys);
    } else if (node.type === NodeType.XbotCommand || node.type === NodeType.Ai) { 
      addAttrsOfXbotCommandNode(element, attrs);
    } else if (node.type === NodeType.Variable) {
      Object.entries(attrs).forEach(([key, value]) => {
        if (key === 'inputList') {
          const newValues = value?.map((row: any) => {
            const newRow = { ...row }
            if (row.value && (row.type === 'json' || row.type === 'array')) {
              try {
                newRow.value = JSON.parse(row.value)
              } catch (error) {
                console.warn('value不是合法的json')
              }
            }
            return newRow
          })
          attrs[key] = newValues
        }
      })
      addAttrsToXMLElement(element, attrs, includeKeys);
      element.setAttribute('outKey', 'customVariables');
      element.setAttribute('nodeId', node.attrs?.nodeId);
    } else {
      addAttrsToXMLElement(element, attrs, undefined);
      element.setAttribute('outKey', node.attrs?.nodeId);
    }

    useExecutionStore.getState().setTaskCaseId(node.attrs?.nodeId, globalSort);
    element.setAttribute('engineExt', `{taskCaseId:${globalSort},nodeId:'${node.attrs?.nodeId}'}`);
    
    if (node.attrs.breakpoints === true || node.attrs.breakpoints === 'true') {
      breakpointsIndices.push(globalSort);
    }
    
    globalSort++;

    // 只有非 AITask-1 节点才处理子节点
    if (!nodeType.startsWith('AITask') && node?.content) {
      const children = node.content.filter((child) => child.attrs?.nodeId);
      const childrenExtraNodeAttrs = getChildrenExtraAttrs(children);

      for (const child of children) {
        const childNodeId: string = child.attrs.nodeId;
        const childEle = getElementWithSort(child, child.attrs?.data, childrenExtraNodeAttrs[childNodeId]);
        childEle && element.appendChild(childEle);
      }
    }

    return element;
  };
  // console.log('editorContent-->', editorContent)
  let nodes = editorContent?.content;
  if (nodes) {
    nodes = nodes.filter((node) => node.attrs?.nodeId);
    const extraNodeAttrs = getChildrenExtraAttrs(nodes);

    for (const node of nodes) {
      const nodeId: string = node.attrs?.nodeId;
      const ele = getElementWithSort(node, node.attrs?.data, extraNodeAttrs[nodeId]);
      if (ele) {
        rootEle.appendChild(ele);
      }
    }
  }

  let xmlStr = '';
  const serializer = new XMLSerializer();
  for (const child of doc.documentElement.childNodes) {
    xmlStr += serializer.serializeToString(child);
  }
  xmlStr = replaceXbotVariables(xmlStr);
  xmlStr = replaceSelectVariables(xmlStr);
  xmlStr = replaceForVariables(xmlStr);
  xmlStr = replaceNonStringVariablesQuotes(nodes, xmlStr);

  return { xml: xmlStr, breakpointsIndices };
};

// 单步调试，获取调试节点的xml字符串
export const getXmlStrOfNode = (
  allNodes: JSONContent[],
  node: JSONContent | undefined,
  nodeData: Record<string, any>,
  extraNodeAttrs: Record<string, any>,
) => {
  if (!node) {
    return '';
  }

  // extraNodeAttrs 为用户输入的表单数据。表单的key可能是变量(以$开头)，也可能是节点的属性。如果是属性需要使用用户输入的值，如果是变量，这里不做操作，后续会使用正则统一替换变量用户输入的值
  const attrs = Object.assign({}, nodeData);
  if (extraNodeAttrs && Object.keys(extraNodeAttrs).length > 0) {
    Object.keys(extraNodeAttrs).forEach((key) => {
      if (!key.startsWith('$')) {
        attrs[key] = extraNodeAttrs[key];
      }
    });
  }

  const element = getElementOfNode(node, nodeData, extraNodeAttrs);
  if (!element) {
    return '';
  }
  const serializer = new XMLSerializer();

  let xmlStr = serializer.serializeToString(element);
  xmlStr = replaceSelectVariables(xmlStr);
  
  xmlStr = replaceNonStringVariablesQuotes(allNodes, xmlStr)

  return xmlStr;
};
