/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{html,js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        white: '#FFFFFF',
        'xgpt-main': '#1C2DCE',
        'xgpt-brand': '#282B8B',
        bander: '#1652F7',
        main: '#111925',
        title: '#111925',
        'title-2': '#646971',
      },
      textColor: {
        primary: '#111925',
        secondary: '#646971',
        tertiary: '#94979D',
        quaternary: '#b7babd',
        placeholder: '#b7babd',
        disabled: '#b7babd',
        disabled: '#b7babd',
        success: '#00A85A',
        fail: '#F50000',
        grey: '#94979D',
        right: '#48B853',
      },
      backgroundColor: {
        'hover-white': '#f2f4f7',
        'hover-gray': '#e3e7ee',
        'hover-blue': '#E8E9FA',
        'hover-gray2': '#94979D',
        'hover-gray3': '#E9ECF2',
        tag: '#f0f0f0',
        disabled: '#f6f7fa',
        card: '#F9FAFC',
        primary: '#f6f7fa',
        success: '#E5F6EE',
        fail: '#FDE5E5',
        resultShow: '#F5F7FF',
        titleGrey: '#646971',
      },
      borderColor: {
        outline: '#dcdddf',
        divider: '#e5e7ea',
        primary: '#f6f7fa',
      },
      fontSize: {
        main: '14px',
        'main-2': '12px',
        title: '16px',
        'title-2': '14px',
        xxs: '0.625rem', // 10px
      },
      fontWeight: {
        main: 600,
        title: 600,
        'title-2': 'normal',
      },
      lineHeight: {
        main: '22px',
        title: '24px',
        'title-2': '22px',
      },
      borderRadius: {
        card: '8px',
      },
    },
  },
  plugins: [],
};

