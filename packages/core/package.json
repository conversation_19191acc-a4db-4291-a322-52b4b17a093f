{"name": "@ag-ui/core", "author": "<PERSON> <<EMAIL>>", "version": "0.0.28", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"rxjs": "7.8.1", "zod": "^3.25.67"}, "devDependencies": {"@types/jest": "^30.0.0", "jest": "^30.0.1", "ts-jest": "^29.4.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}}