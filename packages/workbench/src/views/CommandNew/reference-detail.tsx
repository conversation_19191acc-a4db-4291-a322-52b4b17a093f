import React, { useEffect, useState } from 'react';
import { Card, ConfigProvider, Empty, PaginationProps, Table, Input, Select } from 'antd';
import { getAtomCommandRefList } from '@api/command-api';
import { TableUserCell } from '@common/table-user-cell';
import dayjs from 'dayjs';
import _ from 'lodash';

interface ICommandRef {
    commandName: string;
    commandType: string;
    version: string;
    manager: string;
    createTime: string;
    statusDesc: string;
    statusCode: string;
    refLineNum: string;
  }
  
 

  export const ReferenceDetail: React.FC<{
    commandId: string | number;
  }> = ({commandId}) => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<ICommandRef[]>([]);
    const [pagination, setPagination] = useState({
      current: 1,
      pageSize: 10,
      total: 0
    });
    const [userInfo, setUserInfo] = useState();
    const [cName, setCName] = useState('');
    const [typeName, setTypeName] = useState('');
  
    const columns = [
      {
        title: '名称',
        dataIndex: 'commandName',
        key: 'commandName',
        ellipsis: true,
        width: 200,
        filterDropdown: () => (
          <div style={{ padding: 8 }}>
            <Input
              placeholder="搜索名称"
              allowClear
              onChange={_.debounce(e => {
                setCName(e.target.value);
                setPagination(prev => ({ ...prev, current: 1 }));
                fetchData(1, pagination.pageSize, e.target.value, typeName);
              }, 800)}
              style={{ width: 188, marginBottom: 8 }}
            />
          </div>
        ),
        filtered: !!cName
      },
      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        width: 100,
      },
      {
        title: '类型',
        dataIndex: 'commandType', 
        key: 'commandType',
        width: 120,
        filterDropdown: () => (
          <div style={{ padding: 8 }}>
            <Select
              style={{ width: 188 }}
              onChange={value => {
                setTypeName(value);
                setPagination(prev => ({ ...prev, current: 1 }));
                fetchData(1, pagination.pageSize, cName, value);
              }}
              options={[
                { label: '应用', value: '应用' },
                { label: '业务指令', value: '业务指令' }
              ]}
              allowClear
              placeholder="选择类型"
            />
          </div>
        ),
        filtered: !!typeName
      },
      {
        title: '负责人',
        dataIndex: 'manager',
        key: 'manager',
        width: 180,
        render: (_,record) => {
            return <TableUserCell id={record.manager} userInfo={userInfo} />
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 180,
        render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      },
      {
        title: '引用行',
        dataIndex: 'refLineNum',
        key: 'refLineNum',
        width: 100,
      }
    ];
  
    const fetchData = async (page: number, pageSize: number, commandName = cName, type = typeName) => {
      setLoading(true);
      try {
        const res = await getAtomCommandRefList({
          commandId,
          pageNum: page,
          pageSize,
          cName: commandName,
          typeName: type
        });
        if(res?.data?.code === 0) {
          setData(res.data.data.list);
          setPagination({
            ...pagination,
            current: page,
            total: res.data.data.total
          });
          setUserInfo(res.data.data.userMessage);
        }
      } catch(err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
  
    useEffect(() => {
      fetchData(pagination.current, pagination.pageSize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [commandId]);
  
    const handleTableChange = (newPagination: PaginationProps) => {
      fetchData(newPagination.current, newPagination.pageSize);
    };
  
    return (
      <ConfigProvider renderEmpty={()=>{
        return <Empty description="当前指令还未被引用～" />
    }}>
      <Card>
        <div className="text-base font-medium mb-8 border-l-4 border-blue-500 pl-2">引用详情</div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: (total) => `共 ${total} 条`,
            locale: {
              items_per_page: '条/页',
            }
          }}
          loading={loading}
          onChange={handleTableChange}
          rowKey={(record) => record.commandName + record.version + record.appCommandId}
          scroll={{ x: 1250 }}
        />
      </Card>
    </ConfigProvider>
  );
};
