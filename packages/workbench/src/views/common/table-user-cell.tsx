import { Avatar } from "antd";
import { UserInfoItem } from "./product-plan-feature/type";
import dayjs from "dayjs";


export const TableUserCell:React.FC<{
    id:string,
    userInfo:{[key:string]:UserInfoItem},
    createdAt?:string
}> = ({id, userInfo,createdAt }) => {
    const user:UserInfoItem | null = userInfo?.[id] || null;
    return (
        user ? <div className="flex items-center">
            <Avatar size={32} src={user.avatar} className="mr-2" />
            <div>
                <div>{user.display_name}</div>
                <div className="text-gray-500">{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : ""}</div>
            </div>
        </div> : <div>{createdAt ? dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss') : ""}</div>
    );
};