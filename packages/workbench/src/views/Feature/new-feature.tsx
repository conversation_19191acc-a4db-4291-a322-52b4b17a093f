import React, { useState } from 'react';
import { Button, ConfigProvider, Modal,Form, Input, Select, Space, FormInstance } from 'antd';

export const NewFeature: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const onFinish = () => {
    console.log(form.getFieldsValue());
    setOpen(false);
  };
  const onReset = () => {
    form.resetFields();
    setOpen(false);
  };
  return (
    <>
      <Button type="primary" onClick={() => setOpen(true)}>
        新建特性
      </Button>
      <Modal
        title="新建特性"
        centered
        open={open}
        onOk={onFinish}
        okText="提交"
        cancelText="取消"
        onCancel={onReset}
        width={1000}
      >
        <MyForm  form={form} style={{ padding: '24px' }}/>
      </Modal>
    </>
  );
};

const unitOptions = [
    {
        label:'人',
        value:'人'
    },
    {
        label:'个',
        value:'个'
    },
    {
        label:'行',
        value:'行'
    },
    {
        label:'月',
        value:'月'
    },
    {
        label:'次',
        value:'次'
    },
    {
        label:'小时',
        value:'小时'
    },
    {
        label:'天',
        value:'天'
    },
    {
        label:'GB',
        value:'GB'
    }
]

export const MyForm:React.FC<{
    form:FormInstance,
    style?:React.CSSProperties
}> = ({form,style})=>{
    const featureType = Form.useWatch('featureType',form);
    
    const { Option } = Select;
    
    return (
        <ConfigProvider
            theme={{
                token: {
                    // colorPrimary: '#1890ff',
                    // borderRadius: 2,
                },
            }}
        >
             <div style={style}>
             <Form layout="vertical" form={form}>
                <Form.Item
                    label={<span className="text-red-500">特性名称</span>}
                    required
                >
                    <Input maxLength={10}/>
                </Form.Item>

                <Space size="large">
                    <Form.Item
                        label={<span className="text-red-500">特性类型</span>}
                        required
                    >
                        <Select style={{ width: 200 }} defaultValue="限量特性">
                            <Option value="限量特性">限量特性</Option>
                            <Option value="畅享特性">畅享特性</Option>
                        </Select>
                    </Form.Item>

                    {featureType === '限量特性' && <Form.Item
                        label={<span className="text-red-500">计量单位</span>}
                        required
                    >
                        {/* <Input style={{ width: 200 }} /> */}
                        <Select style={{ width: 200 }} defaultValue="次" options={unitOptions}>
                        </Select>
                        
                    </Form.Item>}
                </Space>

                <Form.Item label="归属应用">
                    <Select
                        style={{ width: '100%' }}
                        defaultValue="默认为全局，有明确的提供应用可选择应用"
                    >
                        <Option value="默认为全局，有明确的提供应用可选择应用">
                            默认为全局，有明确的提供应用可选择应用
                        </Option>
                    </Select>
                </Form.Item>

                <Form.Item label="消耗URL">
                    {/* <Table columns={columns} dataSource={data} pagination={false} /> */}
                </Form.Item>

                <Space size="large">
                    <Form.Item label="绑定订阅计划">
                        <Select style={{ width: 200 }} defaultValue="选择订阅计划">
                            <Option value="选择订阅计划">选择订阅计划</Option>
                        </Select>
                    </Form.Item>

                    <Form.Item label="限量值">
                        <Input style={{ width: 200 }} />
                    </Form.Item>

                    <Form.Item>
                        <Button type="link">新增绑定计划</Button>
                    </Form.Item>
                </Space>
            </Form>
              </div>
        </ConfigProvider>
    );
}



    

