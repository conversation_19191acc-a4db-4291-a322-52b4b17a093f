import React from "react";
import { Select } from "antd";
import { debounce } from "lodash";
import type { SelectProps } from "antd";

interface MisSelectComponentProps
  extends Omit<SelectProps<string | string[]>, "options"> {
  onSearch: (value: string) => void;
  options: { value: string; label: string; display_name: string; key: string, title: string }[];
}

const MisSelectComponent: React.FC<MisSelectComponentProps> = ({
  placeholder = "选择用户",
  style,
  mode,
  onChange,
  defaultValue,
  onSearch,
  options,
  maxTagCount = "responsive",
  ...restProps
}) => {
  const handleSearch = debounce(onSearch, 500);

  const optionRender = (option) => {
    const { key, title,lable } = option.data;
    return (
      <div className="flex items-center">
        {key ?
          <img
          src={key}
          className="h-6 mr-2 rounded-full"
        /> : <img
        src={`https://serverless.sankuai.com/dx-avatar/?type=img&mis=${lable}`}
        alt="avatar"
        className="h-6 mr-2 rounded-full"
      />
        }
        <span>
          {title}
        </span>
      </div>
    );
  };

  const labelRender = (props) => {
    const {title, key } = props;
    return (
      <div className='flex item-center'>
        {key && (
          <img
            src={key}
            className='h-6 rounded-full mr-1'
          />
        )}
        <span>
          {title}
        </span>
      </div>
    );
  };

  return (
    <Select
      defaultValue={defaultValue}
      allowClear
      style={{ minWidth: "300px", width: "100%", ...style }}
      optionRender={optionRender}
      labelRender={labelRender}
      showSearch
      placeholder={placeholder}
      optionFilterProp="title"
      onSearch={handleSearch}
      onChange={onChange}
      options={options}
      mode={mode}
      maxTagCount={maxTagCount}
      {...restProps}
    />
  );
};

export default MisSelectComponent;
