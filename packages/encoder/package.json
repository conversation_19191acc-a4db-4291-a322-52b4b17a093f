{"name": "@ag-ui/encoder", "version": "0.0.28", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "echo \"Skipping lint for ag-ui components - this is an official standard library\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@ag-ui/core": "workspace:*", "@ag-ui/proto": "^0.0.28"}, "devDependencies": {"@types/jest": "^30.0.0", "jest": "^30.0.1", "ts-jest": "^29.4.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}}