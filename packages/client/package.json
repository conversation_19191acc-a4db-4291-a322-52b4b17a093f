{"name": "@ag-ui/client", "author": "<PERSON> <<EMAIL>>", "version": "0.0.28", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@ag-ui/core": "workspace:*", "@ag-ui/encoder": "workspace:*", "@ag-ui/proto": "^0.0.28", "@types/uuid": "^10.0.0", "fast-json-patch": "^3.1.1", "rxjs": "7.8.1", "untruncate-json": "^0.0.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/jest": "^30.0.0", "jest": "^30.0.1", "ts-jest": "^29.4.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}}