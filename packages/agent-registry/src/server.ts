/**
 * Agent Registry - 服务器端入口
 * 包含所有可以在服务器端运行的函数
 * 不包含 React 组件和客户端代码
 */

import type { IntegrationPackage, IntegrationInfo } from '@workspace/shared';

// Integration 注册表 - 服务器端版本
class ServerIntegrationRegistry {
  private integrations = new Map<string, IntegrationInfo>();
  private initialized = false;
  private initPromise: Promise<void> | null = null;

  constructor() {
    console.log('Initializing ServerIntegrationRegistry...');
    this.initPromise = this.initializeIntegrations();
  }

  private async initializeIntegrations() {
    if (this.initialized) return;

    console.log('Registering integrations (server)...');

    try {
      // 动态导入 haiku integration - 只导入服务器端部分
      const haikuModule = await import('../../../integrations/haiku-agent/dist/server.js');
      if (haikuModule.haikuServerIntegrationPackage) {
        this.registerIntegration(haikuModule.haikuServerIntegrationPackage);
      }
    } catch (error) {
      console.warn('Failed to load haiku integration (server):', error);
    }

    try {
      // 动态导入 mario integration - 只导入服务器端部分
      const marioModule = await import('../../../integrations/mario-agent/dist/server.js');
      if (marioModule.marioServerIntegrationPackage) {
        this.registerIntegration(marioModule.marioServerIntegrationPackage);
      }
    } catch (error) {
      console.warn('Failed to load mario integration (server):', error);
    }

    this.initialized = true;
    console.log('Server integration registration completed. Total integrations:', this.integrations.size);
  }

  /**
   * 确保初始化完成
   */
  async ensureInitialized(): Promise<void> {
    if (this.initPromise) {
      await this.initPromise;
    }
  }

  /**
   * 注册 Integration
   */
  registerIntegration(integrationPackage: IntegrationPackage) {
    const info: IntegrationInfo = {
      id: integrationPackage.id,
      name: integrationPackage.name,
      description: integrationPackage.description,
      version: integrationPackage.version,
      package: integrationPackage
    };

    this.integrations.set(integrationPackage.id, info);
    console.log(`Registered integration (server): ${integrationPackage.id}`);
  }

  /**
   * 获取 Integration 信息
   */
  getIntegration(id: string): IntegrationInfo | undefined {
    return this.integrations.get(id);
  }

  /**
   * 获取所有 Integration 信息
   */
  getAllIntegrations(): IntegrationInfo[] {
    return Array.from(this.integrations.values());
  }

  /**
   * 检查 Integration 是否存在
   */
  hasIntegration(id: string): boolean {
    return this.integrations.has(id);
  }

  /**
   * 创建 Integration Agent 实例
   */
  createAgent(id: string, config?: any): any {
    const integrationInfo = this.getIntegration(id);
    if (!integrationInfo) {
      throw new Error(`Integration ${id} not found`);
    }

    const finalConfig = {
      ...integrationInfo.package.defaultConfig,
      ...config
    };

    return integrationInfo.package.createAgent(finalConfig);
  }

  /**
   * 获取简化的 Integration 列表（用于 UI 显示）
   */
  getIntegrationList(): Array<{
    id: string;
    name: string;
    description: string;
    version: string;
  }> {
    return this.getAllIntegrations().map((integration) => ({
      id: integration.id,
      name: integration.name,
      description: integration.description,
      version: integration.version
    }));
  }

  /**
   * 卸载 Integration
   */
  unregisterIntegration(id: string): boolean {
    return this.integrations.delete(id);
  }
}

// 创建服务器端单例实例
const serverRegistry = new ServerIntegrationRegistry();

// 导出服务器端便捷方法
export const getIntegration = async (id: string) => {
  await serverRegistry.ensureInitialized();
  return serverRegistry.getIntegration(id);
};

export const getAllIntegrations = async () => {
  await serverRegistry.ensureInitialized();
  return serverRegistry.getAllIntegrations();
};

export const hasIntegration = async (id: string) => {
  await serverRegistry.ensureInitialized();
  return serverRegistry.hasIntegration(id);
};

export const createAgent = async (id: string, config?: any) => {
  await serverRegistry.ensureInitialized();
  return serverRegistry.createAgent(id, config);
};

export const getIntegrationList = async () => {
  await serverRegistry.ensureInitialized();
  return serverRegistry.getIntegrationList();
};

export const registerIntegration = (pkg: IntegrationPackage) => serverRegistry.registerIntegration(pkg);

export const unregisterIntegration = (id: string) => serverRegistry.unregisterIntegration(id);

// 导出注册表实例（用于扩展）
export { serverRegistry as integrationRegistry };

// 导出类型
export type { IntegrationInfo, IntegrationPackage };
