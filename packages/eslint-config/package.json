{"name": "@workspace/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.29.0", "@next/eslint-plugin-next": "^15.3.4", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.4", "globals": "^16.2.0", "typescript-eslint": "^8.35.0"}}