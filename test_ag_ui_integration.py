#!/usr/bin/python3
# coding: utf-8
"""
AG-UI集成测试脚本
用于验证AG-UI适配器和API是否正常工作
"""

import sys
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    try:
        logger.info("测试AG-UI适配器导入...")
        from apps.endpoints.llm.ag_ui_adapter import AGUIAdapter, AGUIEventType
        logger.info("✅ AG-UI适配器导入成功")
        
        logger.info("测试AG-UI API导入...")
        from apps.endpoints.llm.ag_ui_api import router
        logger.info("✅ AG-UI API导入成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ 导入失败: {e}")
        return False

def test_adapter_functionality():
    """测试适配器功能"""
    try:
        from apps.endpoints.llm.ag_ui_adapter import AGUIAdapter, AGUIEventType
        
        logger.info("测试AG-UI适配器功能...")
        
        # 创建适配器实例
        adapter = AGUIAdapter()
        logger.info("✅ 适配器实例创建成功")
        
        # 测试事件类型
        event_types = list(AGUIEventType)
        logger.info(f"✅ 支持的事件类型: {len(event_types)} 个")
        for event_type in event_types[:5]:  # 显示前5个
            logger.info(f"   - {event_type.value}")
        
        # 测试基本事件创建
        run_started_event = adapter.create_run_started_event("test-thread", "test-run")
        logger.info(f"✅ 运行开始事件创建成功: {run_started_event['type']}")
        
        run_finished_event = adapter.create_run_finished_event("test-thread", "test-run")
        logger.info(f"✅ 运行结束事件创建成功: {run_finished_event['type']}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 适配器功能测试失败: {e}")
        return False

def test_api_routes():
    """测试API路由"""
    try:
        from apps.endpoints.llm.ag_ui_api import router
        from fastapi import FastAPI
        
        logger.info("测试AG-UI API路由...")
        
        # 创建测试应用
        app = FastAPI()
        app.include_router(router)
        
        # 检查路由
        routes = [route for route in app.routes if hasattr(route, 'path')]
        ag_ui_routes = [route for route in routes if '/ag-ui' in route.path]
        
        logger.info(f"✅ AG-UI路由注册成功: {len(ag_ui_routes)} 个路由")
        for route in ag_ui_routes:
            logger.info(f"   - {route.methods} {route.path}")
        
        return True
    except Exception as e:
        logger.error(f"❌ API路由测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("=== AG-UI集成测试开始 ===")
    
    tests = [
        ("模块导入", test_imports),
        ("适配器功能", test_adapter_functionality),
        ("API路由", test_api_routes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} 测试通过")
        else:
            logger.error(f"❌ {test_name} 测试失败")
    
    logger.info(f"\n=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        logger.info("🎉 所有测试通过！AG-UI集成准备就绪。")
        logger.info("\n📝 下一步:")
        logger.info("1. 启动服务: python main.py")
        logger.info("2. 测试AG-UI端点:")
        logger.info("   - WebSocket: ws://localhost:8080/ws/ag-ui")
        logger.info("   - HTTP: http://localhost:8080/ag-ui/health")
        logger.info("3. 运行客户端示例: python examples/ag_ui_client_example.py")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查错误信息并修复问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)