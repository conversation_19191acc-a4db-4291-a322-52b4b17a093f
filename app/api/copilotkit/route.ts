import {
    CopilotRuntime,
    OpenAIAdapter,
    copilotRuntimeNextJSAppRouterEndpoint,
} from "@copilotkit/runtime";
import { NextRequest } from "next/server";
import { MastraClient } from "@mastra/client-js";
import OpenAI from "openai";

/**
 * 验证必要的环境变量
 */
if (!process.env.OPENAI_API_KEY) {
    throw new Error("OPENAI_API_KEY environment variable is required");
}

/**
 * 创建自定义配置的OpenAI实例
 * 使用环境变量中的API密钥、基础URL和模型配置
 */
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL,
    // 添加默认查询参数以确保兼容性
    defaultQuery: process.env.OPENAI_BASE_URL?.includes('azure') ? { "api-version": "2024-04-01-preview" } : undefined,
    // 为Azure OpenAI添加必要的头部
    defaultHeaders: process.env.OPENAI_BASE_URL?.includes('azure') ? { "api-key": process.env.OPENAI_API_KEY } : undefined,
});

/**
 * 验证OpenAI实例是否正确初始化
 * 注意：在某些版本中，completions属性可能在运行时才可用
 */
try {
    // 简单的验证，确保基本属性存在
    if (!openai || typeof openai.chat === 'undefined') {
        throw new Error("OpenAI client not properly initialized");
    }
} catch (error) {
    console.error("OpenAI initialization error:", error);
    throw new Error(`Failed to initialize OpenAI client: ${error.message}`);
}

/**
 * 创建OpenAI适配器，使用自定义配置
 * 确保传入正确的OpenAI实例和模型配置
 */
const serviceAdapter = new OpenAIAdapter({
    openai,
    model: process.env.OPENAI_MODEL || "gpt-4o-2024-11-20",
    // 禁用并行工具调用以避免潜在的竞态条件
    disableParallelToolCalls: false,
});
const runtime = new CopilotRuntime({
    remoteEndpoints: [
        {
            url: process.env.REMOTE_ACTION_URL || "http://0.0.0.0:8000/copilotkit",
        }
    ]
});

export const POST = async (req: NextRequest) => {
    try {
        if (req.nextUrl.searchParams.get("isMastra")) {
            const baseUrl = process.env.NEXT_PUBLIC_REMOTE_ACTION_URL_MASTRA || "http://localhost:4111";
            const mastra = new MastraClient({
                baseUrl,
            });
            const mastraAgents = await mastra.getAGUI({
                resourceId: "TEST",
            });
            const runtime = new CopilotRuntime({
                // @ts-ignore
                agents: mastraAgents,
            });
            const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
                runtime,
                serviceAdapter, // 使用相同的自定义配置适配器
                endpoint: "/api/copilotkit",
            });

            return handleRequest(req);
        }
        else {
            const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
                runtime,
                serviceAdapter,
                endpoint: "/api/copilotkit",
            });

            return handleRequest(req);
        }
    } catch (error) {
        console.error("CopilotKit API Error:", error);
        return new Response(
            JSON.stringify({ 
                error: "Internal server error", 
                message: error.message,
                details: "Failed to process CopilotKit request"
            }), 
            { 
                status: 500, 
                headers: { "Content-Type": "application/json" } 
            }
        );
    }
};
