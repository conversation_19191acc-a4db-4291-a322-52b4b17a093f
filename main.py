#!/usr/bin/python3
# coding: utf-8

from fastapi import FastAP<PERSON>, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.middleware.cors import CORSMiddleware

from settings.settings import ORIGINS
from apps.endpoints.monitor.api import router as monitor_router
from apps.endpoints.llm.api import router as llm_router

# 创建FastAPI应用
app = FastAPI()

# 先配置所有路由和异常处理
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(exc: RequestValidationError):
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": exc.errors(), "body": exc.body}),
    )


# 配置路由器
app.include_router(monitor_router, tags=['monitor'])
app.include_router(llm_router, tags=['llm'])

# 最后将应用包装在CORS中间件中
app = CORSMiddleware(
    app=app,
    allow_origins=ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
