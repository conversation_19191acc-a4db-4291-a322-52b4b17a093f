# Mastra 内嵌代理集成指南

本指南说明如何在 Next.js 项目中使用内嵌的 Mastra 代理，无需外部依赖或单独的 Mastra 服务。

## 实现原理

### 1. 内嵌代理架构
- 在 `@ag-ui/mastra` 包中实现了 `EmbeddedMastraAgent` 类
- 直接继承自 `AbstractAgent`，无需外部 Mastra 实例
- 内置简化的对话处理逻辑和工具功能

### 2. 代理类型
- `general_assistant`: 通用助手，处理一般性问题、时间查询等
- `math_assistant`: 数学助手，专门处理数学计算和表达式求值

### 3. 内置功能
- 流式文本响应
- 基础数学计算
- 时间和日期查询
- 简单的对话交互

## 使用方法

### 1. 配置代理
在 `src/agents.ts` 中已经配置了内嵌代理：

```typescript
{
  id: "mastra-local",
  agents: async () => {
    const { MastraAgent } = await import("@ag-ui/mastra");
    
    return MastraAgent.getEmbeddedAgents({
      resourceId: "local-mastra",
    });
  },
},
```

### 2. 启动应用
```bash
cd apps/dojo
npm run dev
```

### 3. 访问代理
应用启动后，你可以在界面中选择并使用以下代理：
- `general_assistant` - 通用助手
- `math_assistant` - 数学助手

## 优势

### 1. 零外部依赖
- 不需要 `@mastra/core` 或其他 Mastra 包
- 不需要运行单独的 Mastra 服务
- 不需要配置外部 API 连接
- 完全自包含的解决方案

### 2. 即时可用
- 无需安装额外依赖
- 无需复杂的配置过程
- 启动即可使用

### 3. 轻量级实现
- 最小化的代码占用
- 快速的响应时间
- 低内存使用

### 4. 易于扩展
- 可以在 `EmbeddedMastraAgent` 类中添加新功能
- 支持自定义处理逻辑
- 灵活的代理配置

### 5. 完全兼容
- 与 AG-UI 框架完全兼容
- 支持流式响应
- 支持标准的代理接口

## 功能示例

### 数学助手功能
- 基础算术运算：`2 + 2`, `10 * 5`, `100 / 4`
- 支持括号和复杂表达式：`(5 + 3) * 2`
- 自动识别数学表达式并计算结果

### 通用助手功能
- 问候响应："hello", "hi"
- 时间查询："what time is it"
- 日期查询："what's the date"
- 天气咨询：提供引导信息
- 一般性对话支持

## 扩展指南

### 添加新的处理逻辑

在 `integrations/mastra/src/index.ts` 的 `EmbeddedMastraAgent` 类中：

```typescript
private handleGeneralQuery(query: string): string {
  const lowerQuery = query.toLowerCase();
  
  // 添加新的处理逻辑
  if (lowerQuery.includes('help')) {
    return "我可以帮助您处理各种问题。请告诉我您需要什么帮助？";
  }
  
  // 现有逻辑...
}
```

### 添加新的代理类型

在 `getEmbeddedAgents` 方法中：

```typescript
// 添加新的代理
embeddedAgents['code_assistant'] = new EmbeddedMastraAgent({
  agentId: 'code_assistant',
  name: 'Code Assistant',
  description: 'A specialized assistant for coding questions',
  resourceId,
});
```

## 重要注意事项

### 1. 功能限制
- 当前实现提供基础功能，不支持复杂的 AI 模型调用
- 数学计算使用简单的 JavaScript 求值，有安全限制
- 不支持外部 API 调用或复杂的工具集成

### 2. 安全考虑
- 数学表达式求值已进行基础的安全过滤
- 避免执行危险的 JavaScript 代码
- 输入验证和清理已内置

### 3. 性能特点
- 响应速度快，无网络延迟
- 内存占用小
- 适合开发和测试环境

### 4. 扩展性
- 可以根据需要添加更多内置功能
- 支持与外部服务集成
- 可以升级到完整的 Mastra 实现

## 与其他实现方式的对比

| 特性 | 内嵌代理 | 本地 Mastra | 远程服务 |
|------|----------|-------------|----------|
| 外部依赖 | 无 | @mastra/core | Mastra 服务 |
| 启动复杂度 | 极低 | 低 | 高 |
| 功能丰富度 | 基础 | 完整 | 完整 |
| AI 模型支持 | 无 | 有 | 有 |
| 开发便利性 | 极高 | 高 | 中 |
| 生产就绪度 | 低 | 中 | 高 |

内嵌代理适合快速原型开发和功能验证，完整的 Mastra 实现适合生产环境。

## 技术实现细节

### 代理类结构
```typescript
export class EmbeddedMastraAgent extends AbstractAgent {
  // 继承自 AbstractAgent，实现标准接口
  protected run(input: RunAgentInput): Observable<BaseEvent> {
    // 实现流式响应逻辑
  }
  
  private handleMathQuery(query: string): string {
    // 数学计算处理
  }
  
  private handleGeneralQuery(query: string): string {
    // 通用查询处理
  }
}
```

### 集成方式
```typescript
// 在 agents.ts 中使用
MastraAgent.getEmbeddedAgents({
  resourceId: "local-mastra",
})
```

这种实现方式提供了一个轻量级、即时可用的 Mastra 代理解决方案，非常适合开发和原型验证阶段使用。