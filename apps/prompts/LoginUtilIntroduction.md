# Mario 登录服务工具包

登录工具包主要用于解决使用Mario自动化测试框架过程中获取登录账号信息的问题，同时支持对已登录成功的信息进行缓存，降低对登录系统的依赖。

> **注意**：登录工具只负责获取用户信息，无法自动化将用户信息传入被测试接口，请按照被测试接口要求传递用户信息。

## 1. LoginUtil 工具类介绍

`LoginUtil` 是 Mario 框架提供的登录服务工具类，位于 `com.meituan.toolchain.mario.login` 包中。它提供了三种登录方式：

1. **基于前缀的登录**：`login(LoginType loginType, String preUserKey)`
    - 适用于从配置文件读取登录信息的场景
    - 通过用户前缀标识不同的用户配置

2. **基于前缀和应用名的登录**：`login(LoginType loginType, String preUserKey, String appName)`
    - 适用于多应用场景，需要指定应用名称
    - 可以为不同应用配置不同的登录信息

3. **基于动态参数的登录**：`login(LoginType loginType, String host, JSONObject userInfos)`
    - 适用于动态传入登录参数的场景
    - 不依赖配置文件，直接传入登录所需的全部参数

4. **登出方法**：用户对象的 `logout()` 方法
    - 用于清除缓存中的用户信息
    - 下次登录时将重新获取用户信息

## 2. 登录系统类型

Mario 框架支持多种登录系统类型，每种类型对应不同的用户类和环境域名：

| 登录系统 | 枚举类型（包路径）| 用户类（包路径）   | 环境域名 |
|----|-----------------------------------------------------------------------|-----------------------------------------------------------------------|----|
| 美团_C_端登录_V2 | MT_C_LOGIN_V2 (com.meituan.toolchain.mario.login.model.LoginType)     | MTCUser (com.meituan.toolchain.mario.login.model.MTCUser)             | 线下：http://admin-user.wpt.test.sankuai.com |
| 酒店_B_端APP登录 | HOTEL_B_APP_LOGIN (com.meituan.toolchain.mario.login.model.LoginType) | HotelAppBUser (com.meituan.toolchain.mario.login.model.HotelAppBUser) | 线下: http://fepassport.sjst.test.sankuai.com |
| 酒店_B_端PC登录 | HOTEL_B_PC_LOGIN (com.meituan.toolchain.mario.login.model.LoginType)  | HotelPcBUser (com.meituan.toolchain.mario.login.model.HotelPcBUser)   | 线下: http://fepassport.sjst.test.sankuai.com |
| 商家SSO_登录 | SSO_B_LOGIN (com.meituan.toolchain.mario.login.model.LoginType)       | SSOBUser (com.meituan.toolchain.mario.login.model.SSOBUser)           | 线下: http://fepassport.sjst.test.sankuai.com |
| 商家SSO_免密登录 | SSO_B_NO_PW_LOGIN (com.meituan.toolchain.mario.login.model.LoginType) | SSOBUser (com.meituan.toolchain.mario.login.model.SSOBUser)           | 无须配置环境域名 |
| SSO_登录 | SSO_LOGIN (com.meituan.toolchain.mario.login.model.LoginType)         | SSOUser (com.meituan.toolchain.mario.login.model.SSOUser)             | 线下: http://ssosv.it.test.sankuai.com 线上： https://sso.vip.sankuai.com |
| 美团_I版登录 | MT_I_LOGIN (com.meituan.toolchain.mario.login.model.LoginType)        | ICUser (com.meituan.toolchain.mario.login.model.ICUser)               | 无须配置环境域名 |
| SSO_登录_真实账号 | SSO_LOGIN_REALMIS (com.meituan.toolchain.mario.login.model.LoginType) | SSOUser (com.meituan.toolchain.mario.login.model.SSOUser)             | 线下: http://ssosv.it.test.sankuai.com 线上： https://sso.vip.sankuai.com |

## 3. 配置说明

每种登录方式的配置分为 HOST 和入参配置，配置文件名称为 `env.properties`（与接口测试配置文件相同），配置文件路径在`profiles`目录下。

配置规则：
- HOST 的 KEY 不能修改，但值可以根据具体的登录环境修改
- XXX 开头的字段为某种登录方式的请求参数，XXX 代表用户前缀的配置，之后的 KEY 值不能修改，但值可以根据实际情况修改
- 同一个服务配置多个用户只需要增加入参配置，修改前缀 XXX 即可

### 3.1 美团_C_端登录_V2

当前登录使用的接口文档：https://docs.sankuai.com/mt/us/user_doc/master/tools/qa-toolkit/

支持国际手机号获取登录态信息，配置格式需要使用 **\"** 将手机号包裹。

```properties
CHOSTV2=http://admin-user.wpt.test.sankuai.com
# 国内手机号配置格式
test_C_USER_NAME=手机号
# 国际手机号配置格式
test_C_USER_NAME=\"区号_手机号\"
```

### 3.2 点评_C_端登录

```properties
XXX_DP_C_USER_ID=*********   # 点评userId
```

### 3.3 酒店_B_端APP登录 & 酒店_B_端PC登录

```properties
BHOST=http://fepassport.sjst.test.sankuai.com  # B端host，根据不同环境修改值
XXX_B_APPKEY=test                 # 登录应用的appKey
XXX_B_LOGIN=zsmautotest           # 登录应用的用户名
XXX_B_PASSWORD=zhaoshimei1        # 登录应用的用户密码
XXX_B_BG_SOURCE=17                # 登录应用的bgSource（收银智能版业务线需要设置BG_SOURCE，其他业务线可不设置，若出现账号密码错误可自行设置bgSource）
XXX_B_PART_KEY=573728             # 登录账号的partKey（收银青春版业务线需要设置账号对应的PART_KEY，其他业务线可不设置）
XXX_B_PART_TYPE=1                 # 登录应用的partType（收银青春版业务线需要设置PART_TYPE，其他业务线可不设置）
```

### 3.4 商家SSO_登录

```properties
BHOST=http://fepassport.sjst.test.sankuai.com  # B端host，根据不同环境修改值
XXX_SSO_B_SERVICE=oversea                     # 登录应用的service名称
XXX_SSO_B_LOGIN=huice12                       # 登录应用的用户名
XXX_SSO_B_PASSWORD=Ceshi12                    # 登录应用的密码
XXX_SSO_B_CALLBACK=https://eo.51ping.com/overseas/poseidon/mt/settoken?target=https://eo.51ping.com/epassport/home    # 登录后的回调地址,登陆失败时，可以进行URL编码后重试
XXX_SSO_B_BG_SOURCE=1                         # 登录应用BG_SOURCE，根据具体的业务系统填写
XXX_SSO_B_PART_KEY=573728                     # 登录账号的partKey（收银青春版业务线需要设置账号对应的PART_KEY，其他业务线可不设置）
XXX_SSO_B_PART_TYPE=1                         # 登录应用的partType（收银青春版业务线需要设置PART_TYPE，其他业务线可不设置）
```

### 3.5 SSO_登录

- [SSO接入文档](https://km.sankuai.com/page/64816520)
- 测试账号请在[IAM](https://iam.sankuai.com/my-request?field=add)申请，SSO配置信息请联系 **RD** 参考[SSO资源申请](https://km.sankuai.com/page/*********)获取

```properties
SSO_HOST=http://ssosv.it.test.sankuai.com       # sso host，根据不同环境修改值
XXX_SSO_USER=hb.autotes                       # 登录用户名
XXX_SSO_PASSWORD=Hb123456                     # 登录密码
XXX_SSO_CLIENT_ID=it_sso_tes                  # sso给被测试应用颁发的clientId，请向被测试应用RD索要
XXX_SSO_CLIENT_SECRET=9b958a98e0b1612aa65964207f5d29b  # sso给被测试应用颁发的秘钥，请向被测试应用RD索要
```

### 3.6 商家SSO_免密登录

不需要密码，可以直接通过登录名称登录。只需要在配置中配置用户登录名称即可。

```properties
XXX_SSO_B_LOGIN=ntflc_api_test8                # 登录应用的用户名，value为实际的登录名称
```

## 4. 使用示例

### 4.1 基于前缀的登录（从配置文件读取）

适用于单一应用场景，通过用户前缀标识不同的用户配置。

```java
// 美团_C_端登录_V2
MTCUser mtcUser = (MTCUser) LoginUtil.login(LoginType.MT_C_LOGIN_V2, "zhangyong");
Assert.assertNotNull(mtcUser);

// 点评_C_端登录
DPCUser user = (DPCUser) LoginUtil.login(LoginType.DP_C_LOGIN, "zy");
Assert.assertNotNull(user);

// 商家SSO_免密登录
SSOBUser ssobUser = (SSOBUser) LoginUtil.login(LoginType.SSO_B_NO_PW_LOGIN, "ntflc_api_test8");
AssertUtil.assertNotNull(ssobUser);
```

### 4.2 基于前缀和应用名的登录

适用于多应用场景，需要指定应用名称，可以为不同应用配置不同的登录信息。

```java
// SSO_登录，指定应用名
SSOUser user1 = (SSOUser) LoginUtil.login(LoginType.SSO_LOGIN, "name1", "project1");
SSOUser user2 = (SSOUser) LoginUtil.login(LoginType.SSO_LOGIN, "name2", "project1");
Assert.assertNotNull(user1);
Assert.assertNotNull(user2);
```

### 4.3 基于动态参数的登录

适用于动态传入登录参数的场景，不依赖配置文件，直接传入登录所需的全部参数。

```java
// 商家SSO_登录
JSONObject loginParams = new JSONObject();
loginParams.put("SSO_B_SERVICE", "com.sankuai.meishi.fe.ecom");
loginParams.put("SSO_B_LOGIN", "ntflc_api_test8");
loginParams.put("SSO_B_PASSWORD", "android1994");
loginParams.put("SSO_B_CALLBACK", "http%3A%2F%2Fe.meishi.test.meituan.com%2Fmeishi%2F");
loginParams.put("SSO_B_BG_SOURCE", "1");
String host = "http://fepassport.sjst.test.sankuai.com";
SSOBUser user = (SSOBUser) LoginUtil.login(LoginType.SSO_B_LOGIN, host, loginParams);
AssertUtil.assertNotNull(user);
AssertUtil.assertNotNull(user.getBsid());
        user.logout();

// 美团_C_端登录_V2
JSONObject loginParams = new JSONObject();
loginParams.put("C_USER_NAME", "手机号码");
String host = "http://admin-user.wpt.test.sankuai.com";
MTCUser user = (MTCUser) LoginUtil.login(LoginType.MT_C_LOGIN_V2, host, loginParams);
AssertUtil.assertNotNull(user);
AssertUtil.assertNotNull(user.getId());
        user.logout();
```

### 4.4 删除缓存用户

使用 `user.logout()` 方法可以删除缓存的用户信息，下次登录时将重新获取用户信息。

```java
// 获取缓存用户
SSOUser user = (SSOUser) LoginUtil.login(LoginType.SSO_LOGIN_REALMIS, "virgo");
SSOUser user2 = (SSOUser) LoginUtil.login(LoginType.SSO_LOGIN_REALMIS, "virgo");
Assert.assertEquals(user, user2);  // 相同用户，从缓存获取

// 删除缓存用户
user.logout();

// 重新登录获取新用户
user = (SSOUser) LoginUtil.login(LoginType.SSO_LOGIN_REALMIS, "virgo");
Assert.assertNotEquals(user, user2);  // 不同用户，重新登录获取
```

## 5. 用户信息缓存策略

Mario 登录工具提供了智能的用户信息缓存策略：

1. 每次登录时先从 cache 中读取用户，如果用户不存在，返回 null 进行登录
2. 如果用户存在，判断用户是否缓存超过 3600 秒
3. 如果超过 3600 秒，则执行登录；如果登录失败，再去读缓存用户并返回
4. 如果未超过 3600 秒，则直接返回用户信息
5. 如果上述获取到用户信息，则缓存用户

### 缓存配置详情

- **登录间隔配置**（缓存配置不过期）
    - key: {登录类型名称}-登录间隔，例如: 美团_C_端登录_V2-登录间隔
    - value: 数字，单位秒（如果未配置缓存值，系统默认值 3600）

- **用户信息缓存过期时间配置**（缓存配置不过期）
    - key: {登录类型名称}，例如: 美团_C_端登录_V2
    - value: 数字，单位秒（如果未配置缓存值，系统默认值 3600）

- **用户信息缓存**
    - key: hbqalogin-{登录类型}-{实际登录名称}-{登录的host}
    - value: 登录后的用户对象（如果未配置缓存值，系统默认值 3600*24）

## 6. 登录失败重试机制

登录失败时会自动重试：

1. 最大重试次数为 3 次
2. 如果登录失败，会根据错误类型决定是否继续重试：
    - 密码错误、账户被锁定、SSO特定错误（"smsCode与OtpCode不能同时为空"）等情况不会重试
    - 其他错误会在间隔 1 秒后重试，最多重试 3 次
3. 如果所有重试都失败，会尝试从缓存中获取过期的用户信息

## 7. 常见问题解答

1. **SSO登录出现 `{"code":10001,"data":{"attach":null,"msg":"smsCode与OtpCode不能同时为空"}`**
    - 解决方案：找 @SSO小助手 增加白名单，或前往 [IAM](https://iam.sankuai.com/my-request?field=add) 申请测试账号进行测试

2. **本地使用中出现 `java.lang.NoSuchMethodError: org.reflections.util.ClasspathHelper.forPackage(Ljava/lang/String;[Ljava/lang/ClassLoader;)Ljava/util/Set;`**
    - 解决方案：修改本地的 ./m2 目录下的 settings.xml 为 [settings.xml](http://s3plus.vip.sankuai.com/v1/mss_8b80ba092e4145088a62d10f25a14f36/resource/maven/local/settings.xml)

3. **更多问题排查**
    - 参考 [登录工具问题排查SOP](https://km.sankuai.com/collabpage/2292714820)
