# Mario Thrift 测试用例编写步骤

## 一、创建文件结构
编写 Thrift 协议测试用例需要创建以下文件：
1. 测试类：`{ServiceName}Test.java`
2. 数据文件：`{ServiceName}Test.json`
3. Schema文件：`{ServiceName}Test.json`

注意：与 HTTP 协议不同，Thrift 协议不需要创建接口类。

文件结构应为：
```
src/test/java/{用户输入}/{service}/
├── testsuites/              # 测试类目录
│   └── PaymentServiceTest.java  # 测试类，命名格式为{ServiceName}Test.java
├── data/                    # 数据文件目录
│   └── PaymentServiceTest.json
└── schema/                   #schema文件目录
    └── PaymentServiceTest.json
```

## 二、编写测试类
Thrift 测试有两种模式：泛化调用和 Jar 包调用。请先明确本次是使用Jar包调用还是泛化调用。 如果不确定，请向用户确认。  
Jar包调用时用户会提供Jar包信息(groupId,artifactId,version)和接口信息(appkey,interfaceName,methodName)，泛化调用时用户需要提供接口信息(appkey,interfaceName,methodName)。


### 1. 泛化调用方式
使用 `@ThriftAPI` 注解标注服务信息，通过 `ThriftProcessor.invoke()` 进行调用,`ThriftProcessor`位于`com.meituan.toolchain.mario.AnnotationProcessor`包中：
测试方法注解会覆盖率测试类上配置，多接口公共配置可以定义在测试类上
`ThriftProcessor.invoke(request...)`参数的数量必须和被测试接口参数数量一致，普通类型可以直接传，自定义类可以使用JsonObject
```java
import com.meituan.toolchain.mario.annotation.ThriftAPI;
import com.meituan.toolchain.mario.framework.JsonFileDataProvider;
import org.testng.annotations.Test;
import com.alibaba.fastjson.JSONObject;
import com.meituan.toolchain.mario.AnnotationProcessor.ThriftProcessor;
import lombok.extern.slf4j.Slf4j;
import com.meituan.toolchain.mario.util.AssertUtil;

@ThriftAPI(
    appkey = "com.sankuai.hotel.cbs.priceapi",
    interfaceName = "com.meituan.hotel.price.server.client.thrift.service.PriceCalcService",
    desc = "价格计算服务")
@Slf4j
public class PaymentServiceTest {
   @ThriftAPI(
        desc = "queryPoiLowestPrice接口测试", 
        methodName = "queryPoiLowestPrice"
    )
   @Test(description = "XXX 测试", dataProvider = "data",dataProviderClass = JsonFileDataProvider.class)  //一个测试类一条数据时使用
   //@Test(description = "XXX 测试", dataProvider = "dataMethod",dataProviderClass = JsonFileDataProvider.class) 一个测试类多条数据时使用
    public void processPaymentTest(JSONObject request, JSONObject expect,String comments) throws Exception {
        String response = ThriftProcessor.invoke(request);
        AssertUtil.assertJsonSchemaByMethodName(response);
    }
}
```

### 2. Jar 包调用方式
直接注入接口实例，通过实际对象进行调用：

```java
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.hotel.price.server.client.thrift.model.QueryPoiLowestPriceParam;
import com.meituan.hotel.price.server.client.thrift.model.QueryPoiLowestPriceResult;
import com.meituan.hotel.price.server.client.thrift.service.PriceCalcService;
import com.meituan.toolchain.mario.annotation.ThriftAPI;
import com.meituan.toolchain.mario.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.Test;

@Slf4j
public class PriceCalcServiceTest {
   @ThriftAPI(appkey = "com.sankuai.hotel.cbs.priceapi", port = 8420)
   PriceCalcService.Iface priceCalcService;

   @Test(description = "XXX 测试", dataProvider = "data",dataProviderClass = JsonFileDataProvider.class)  //一个测试类一条数据时使用
   //@Test(description = "XXX 测试", dataProvider = "dataMethod",dataProviderClass = JsonFileDataProvider.class) 一个测试类多条数据时使用
   public void test(JSONObject request,JSONObject expect,String comments) throws Exception {
      QueryPoiLowestPriceParam queryPoiLowestPriceParam = new QueryPoiLowestPriceParam();
      log.info(JSON.toJSONString(queryPoiLowestPriceParam));
      QueryPoiLowestPriceResult queryPoiLowestPriceResult = priceCalcService.queryPoiLowestPrice(queryPoiLowestPriceParam);
      Assert.assertNotNull(queryPoiLowestPriceResult);
      log.info(queryPoiLowestPriceResult.toString());
      AssertUtil.assertJsonSchema(JSON.toJSONString(queryPoiLowestPriceResult));
   }
}
```

## 三、准备数据文件
根据测试方法数量选择合适的数据格式：

**单方法测试**（使用`dataProvider = "data"`）:
```json
[
  {
    "comments": "测试场景1 - 查询最低价格",
    "request": {
      "poiId": 123456,
      "checkInDate": "2023-10-01",
      "checkOutDate": "2023-10-02"
    },
    "response": {
      "code": 200,
      "data": {
        "lowestPrice": 100,
        "currency": "CNY"
      }
    }
  }
]
```

**多方法测试**（使用`dataProvider = "dataMethod"`）:
```json
{
  "processPaymentTest": [
    {
      "comments": "测试场景1 - 处理支付",
      "request": {
        "orderId": "ORD123456",
        "amount": 100.00,
        "currency": "CNY"
      },
      "response": {
        "code": 200,
        "data": {
          "transactionId": "T12345",
          "status": "SUCCESS"
        }
      }
    }
  ],
  "refundPaymentTest": [
    {
      "comments": "测试场景1 - 退款处理",
      "request": {
        "transactionId": "T12345"
      },
      "response": {
        "code": 200,
        "data": {
          "refundId": "R12345",
          "status": "REFUNDED"
        }
      }
    }
  ]
}
```

## 四、创建Schema文件
定义 Thrift 接口返回的 JSON 结构验证规则：

**单方法校验**（使用单独的 Schema 文件）:
```json
{
  "$schema": "http://json-schema.org/draft-04/schema#",
  "type": "object",
  "required": ["code", "data"],
  "properties": {
    "code": { "type": "number" },
    "data": {
      "type": "object",
      "required": ["transactionId", "status"],
      "properties": {
        "transactionId": { "type": "string" },
        "status": { "type": "string" }
      }
    }
  }
}
```

**多方法校验**（在一个 Schema 文件中定义多个方法）:
```json
{
  "processPaymentTest": {
    "$schema": "http://json-schema.org/draft-04/schema#",
    "type": "object",
    "required": ["code", "data"],
    "properties": {
      "code": { "type": "number" },
      "data": {
        "type": "object",
        "required": ["transactionId", "status"],
        "properties": {
          "transactionId": { "type": "string" },
          "status": { "type": "string" }
        }
      }
    }
  },
  "refundPaymentTest": {
    "$schema": "http://json-schema.org/draft-04/schema#",
    "type": "object",
    "required": ["code", "data"],
    "properties": {
      "code": { "type": "number" },
      "data": {
        "type": "object",
        "required": ["refundId", "status"],
        "properties": {
          "refundId": { "type": "string" },
          "status": { "type": "string" }
        }
      }
    }
  }
}
```

## 五、结果校验
Thrift 测试需要进行 Schema 校验：

```java
// 方法1：基于方法名自动匹配 Schema
AssertUtil.assertJsonSchemaByMethodName(JSON.parseObject(response));

// 方法2：指定 Schema 文件路径
AssertUtil.assertJsonSchema(response, "schema/PaymentSchema.json#processPayment");
```

与 HTTP 协议一样，Thrift 协议要求强制进行 Schema 校验。

## 六、注意事项

1. **文件要求差异**：
    - Thrift 协议不需要创建接口类
    - Thrift 协议需要创建 Schema 文件
    - 需要测试类、数据文件和 Schema 文件

2. **包路径管理**：必须严格按照示例代码中提供的各个类的包路径来导入

3. **测试类型选择**：
    - 泛化调用：适用于不依赖具体实现的场景，通过 JSON 映射传参
    - Jar 包调用：需要依赖具体实现 jar 包，可直接操作对象

4. **数据提供方式**：
    - 单测试方法：使用 `dataProvider = "data"`
    - 多测试方法：使用 `dataProvider = "dataMethod"`
    - 一个测试类中所有测试方法必须使用相同的 `dataProvider` 方式
    - 使用了`dataProvider`的测试方法的参数必须是 request，expect，comments
    - 参数`request`对应数据文件中的`request`,参数`expect`对应数据文件中的`response`,参数`comments`对应数据文件中的`comments`

5. **ThriftAPI 注解**：
    - 泛化调用类级别注解：需指定 `appkey`、`interfaceName` 和 `desc`
    - 泛化调用方法级别注解：需指定 `methodName` 和 `desc`
    - Jar包调用：在字段上使用注解，仅需指定 `appkey` 和可选的 `port`

6. **Schema 校验**：
    - Thrift 协议强制要求 Schema 校验
    - 单方法可使用 `AssertUtil.assertJsonSchema()`
    - 多方法可使用 `AssertUtil.assertJsonSchemaByMethodName()`
    - 调用 `MarioFrameworkToolsIntroduction`工具获取 Mario 自动化框架提供的完整校验工具使用方法

7. **配置管理**：
    - RPC 直连配置（如需直连）：在 config.properties 中设置 `${appkey}=${IP}`，直连测试时服务定义注解ThriftAPI中必须设置被测服务端口
    - 命令行直连配置：设置环境变量 `DIRECT_IP_{appkey}` (将`.`转为`_`),直连测试时服务定义注解ThriftAPI中必须设置被测服务端口
    - RPC 超时配置：通过 `RPC_TIMEOUT` 设置（默认 5 秒）
    - 配置管理可以调用`MarioConfigManage`工具查看详细内容

8. **路径管理**：
    - 如果用户指定路径，生成路径为 `src/test/java/` + 用户路径
    - 默认生成路径为 `src/test/java/thrift/`

9. **异常处理**：
    - Jar包调用方式需要处理可能的异常（方法签名包含 throws Exception）
    - 可使用 Assert 断言确保服务实例和响应结果不为空