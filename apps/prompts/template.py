import os
import re

from langchain_core.prompts import PromptTemplate
from langgraph.prebuilt.chat_agent_executor import Agent<PERSON>tate


def get_prompt_template(prompt_name: str) -> str:
    template = open(os.path.join(os.path.dirname(__file__), f"{prompt_name}.md")).read()
    template = template.replace("{", "{{").replace("}", "}}")
    # Replace `<<VAR>>` with `{VAR}`
    template = re.sub(r"<<([^>>]+)>>", r"{\1}", template)
    return template


def apply_prompt_template(prompt_name: str, state: AgentState) -> list:
    system_prompt = PromptTemplate(
        template=get_prompt_template(prompt_name),
    ).format(**state)
    return [{"role": "system", "content": system_prompt}] + state["messages"]


def prompt_origin_template(prompt_name: str) -> list:
    system_prompt = PromptTemplate(template=get_prompt_template(prompt_name)).format()
    return [{"role": "system", "content": system_prompt}]

