# 接口类型分析器

## 任务描述
请分析以下文件内容，判断它涉及哪种接口类型，并仅返回对应的模板文件名。

**重要说明：** 只返回模板文件名，不要有其他任何输出。

## 判断规则

### 1. HTTP接口
**关键词识别：**
- 接口URL
- HTTP、RestController
- URL路径
- REST API相关术语

**返回值：** `MarioHttpCaseDev.md`

### 2. MapiHttp接口
**关键词识别：**
- MapiHttp
- Mapi相关字眼

**返回值：** `MarioMapiHttpCaseDev.md`

### 3. Thrift接口
**关键词识别：**
- Thrift
- Thrift泛化调用
- 服务名称
- 方法名称

**返回值：** `MarioThriftCaseDev.md`

### 4. Pigeon接口
**关键词识别：**
- Pigeon
- Pigeon泛化调用
- 服务名称
- 方法名称

**返回值：** `MarioPigeonCaseDev.md`