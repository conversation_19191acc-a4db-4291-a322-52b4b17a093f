
# 测试接口类以及测试类生成

只允许包含接口类和测试类，不允许自定义数据

创建接口类：`{ServiceName}.java`（必须继承HttpRequestService）
创建测试类：`{ServiceName}Test.java`

## HOST配置【重要】

在编写HTTP接口测试前，**必须**确认接口的正确HOST：

1. **默认HOST**：如果接口使用项目默认HOST，在env.properties中已有`HOST=xxx`配置
   ```java
   // 使用默认HOST
   ServiceInterface service = retrofit().create(ServiceInterface.class);
   ```

2. **自定义HOST**：针对不同服务的接口，需要添加自定义HOST
   ```
   # 在src/test/profiles/环境/env.properties中添加自定义HOST
   SERVICE_HOST=https://service.example.com/
   ```
   ```java
   // 在接口类中使用自定义HOST
   ServiceInterface service = retrofit("SERVICE_HOST").create(ServiceInterface.class);
   ```

3. **HOST配置注意事项**：
   - 确保env.properties中HOST末尾已添加"/"（避免URL拼接问题）
   - 开发前推荐使用Postman等工具验证接口是否可访问


## 编写接口类
接口类必须继承`HttpRequestService`，定义HTTP接口的请求方法：
```java
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.toolchain.mario.model.ResponseMap;
import com.meituan.toolchain.mario.protocol.http.HttpRequestService;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.*;
// 接口定义
public class {ServiceName}Api extends HttpRequestService {
    public interface MdoAPIServices {
        @POST("mdo/v1")
        Call<ResponseBody> postMdoData(@QueryMap JSONObject jsonQuery);
    }

    public ResponseMap postMdoData(JSONObject request) throws Exception {
        MdoAPIServices service = retrofit().create(MdoAPIServices.class);//使用配置文件的HOST初始化服务
        MdoAPIServices service = retrofit("hostA").create(MdoAPIServices.class);//// 根据接口所属服务选择合适的HOST
        Call<ResponseBody> call = service.postMdoData(request);
        return sendRequest(call);
    }
}
```

## 编写测试类
根据测试方法数量选择合适的数据提供方式：
```java
import com.alibaba.fastjson.JSONObject;
import com.meituan.toolchain.mario.framework.JsonFileDataProvider;
import com.meituan.toolchain.mario.model.ResponseMap;
import com.meituan.toolchain.mario.util.AssertUtil;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import lombok.extern.slf4j.Slf4j;
@Slf4j
public class {Service}ApiTest {
    private {ServiceName}Api {serviceName}Api;
    
    @BeforeClass
    public void setup() {
        {serviceName} = new {ServiceName}Api();
    }
    
    // 单个测试方法使用 dataProvider = "data"
    @Test(dataProvider = "data", dataProviderClass = JsonFileDataProvider.class)
    // 多个测试方法使用 dataProvider = "dataMethod",如下注解，请勿将这部分注释生成在代码中
    // @Test(dataProvider = "dataMethod", dataProviderClass = JsonFileDataProvider.class)
    public void getUserInfoTest(JSONObject request, JSONObject expect, String comments) throws Exception {
        log.info("Test comments: {}", comments);
        //调用接口接收结果
        ResponseMap response = {serviceName}.getUserInfo(request);
        //调用 AssertUtil 进行校验
        // 1. 断言HTTP状态码
        AssertUtil.assertHttp200(response);
        // 2.断言JSON Schema
        AssertUtil.assertJsonSchemaByMethodName(response);
        
        // 解析响应JSON
        String responseBody = response.getResponseBody();
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        // 进行其他必要验证
        // ...
    }
  
}
```

## 格式规范
1. **生成的接口类标题名称必须为**
   - 接口类以及接口类名称`{ServiceName}.java`, 例如：接口类`{ServiceName}.java`
   - 后面紧跟生成的java代码块
   
2. **生成的测试类类标题名称必须为**
   - 测试类以及测试类名称`{ServiceName}Test.java`, 例如：测试类`{ServiceName}Test.java`
   - 后面紧跟生成的java代码块
   
## 注意事项
1. **接口类必选**：HTTP协议测试必须创建接口类，且必须继承`HttpRequestService`
2. **配置管理**：
    - 环境配置在`src/test/profiles/xxx/env.properties`
    - 基础配置如`HOST`、`PROXY`等在properties文件中设置
    - 配置加载顺序：`config.properties` -> `env.properties` -> 环境变量
    - 配置管理可以调用`MarioConfigManage`工具查看详细内容
3.  **Schema校验**：
    - HTTP协议强制要求Schema校验
    - 调用`MarioFrameworkToolsIntroDuction`工具获取 Mario 框架提供的校验工具方法，并根据提供的工具方法来正确使用
    - 使用`AssertUtil.assertHttp200(response)`验证HTTP状态码
    - 根据方法数量选择适当的校验方式