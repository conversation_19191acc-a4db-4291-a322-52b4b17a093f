
# 测试数据以及Schema数据生成

输出测试数据，不要编写测试方法,必须根据提供的内容生成测试数据

创建数据文件：
`{ServiceName}Test.json`

创建Schema文件：
`{ServiceName}SchemaTest.json`

## 测试数据准备

根据测试方法数量选择合适的数据格式：

### 单方法测试

使用 `dataProvider = "data"`：

```json
[
  {
    "comments": "测试场景1 - 获取用户信息",
    "request": {
      "userId": 12345
    },
    "response": {
      "code": 200,
      "data": {
        "username": "test_user",
        "age": 25
      }
    }
  }
]
```
### 多方法测试

使用`dataProvider = "dataMethod"`

```json
{
  "getUserInfoTest": [
    {
      "comments": "测试场景1 - 获取用户信息",
      "request": {
        "userId": 12345
      },
      "response": {
        "code": 200,
        "data": {
          "username": "test_user",
          "age": 25
        }
      }
    }
  ],
  "getUserListTest": [
    // 其他方法的测试数据
  ]
}
```

## 创建Schema文件
定义接口返回的JSON结构验证规则：

**单方法或多方法共用校验**（通常使用`AssertUtil.assertJsonSchema()`）:
```json
{
  "$schema": "http://json-schema.org/draft-04/schema#",
  "type": "object",
  "required": ["code", "data"],
  "properties": {
    "code": { "type": "number" },
    "data": {
      "type": "object",
      "required": ["username", "age"],
      "properties": {
        "username": { "type": "string" },
        "age": { "type": "number" }
      }
    }
  }
}
```

**多方法单独校验**（通常使用`AssertUtil.assertJsonSchemaByMethodName(response)`）:
```json
{
  "getUserInfoTest": {
    "$schema": "http://json-schema.org/draft-04/schema#",
    "type": "object",
    "required": ["code", "data"],
    "properties": {
      "code": { "type": "number" },
      "data": {
        "type": "object",
        "required": ["username", "age"],
        "properties": {
          "username": { "type": "string" },
          "age": { "type": "number" }
        }
      }
    }
  },
  "getUserListTest": {
    // 其他方法的Schema定义
  }
}
```

## 格式规范
1. **生成的数据文件标题名称必须为**
   - 数据文件以及数据文件名称`{ServiceName}Test.json`, 例如：数据文件`{ServiceName}Test.json`，标题内容必须严格遵守，不允许有一个字不一样
   - 后面紧跟生成的数据代码块

2. **生成的Schema文件标题名称必须为**
   - Schema文件以及Schema文件名称`{ServiceName}Test.json`, 例如：Schema文件`{ServiceName}Test.json`，标题内容必须严格遵守，不允许有一个字不一样
   - 后面紧跟生成的数据代码块

## 注意事项

### 1. **数据提供方式**

- **单测试方法**：使用 `dataProvider = "data"`
- **多测试方法**：使用 `dataProvider = "dataMethod"`
- **使用规范**：
  - 一个测试类中所有测试方法必须使用相同的 `dataProvider` 方式
  - 使用了 `dataProvider` 的测试方法的参数必须是：`request`、`expect`、`comments`
  - 参数映射关系：
    - 参数 `request` 对应数据文件中的 `request`
    - 参数 `expect` 对应数据文件中的 `response`
    - 参数 `comments` 对应数据文件中的 `comments`

### 2. **测试数据格式**

- **格式要求**：JSON 格式
- **必需字段**：
  - `comments`：测试用例说明
  - `request`：请求数据
  - `response`：期望响应数据
- **多方法测试**：需要以方法名作为键组织数据
