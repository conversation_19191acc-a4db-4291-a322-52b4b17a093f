# 用例生成指南

请生成完整的用例文件并以指定格式输出。根据不同服务类型，需要提供相应的文件集合。

## Http/MapiHttp 服务格式

```json
{
    "{ServiceName}.java": "// 接口类代码（普通Http接口必须继承HttpRequestService，MapiHttp接口要继承MApiRequestService）",
    "{ServiceName}Test.java": "// 测试类代码",
    "{ServiceName}Test.json": "// 测试数据",
    "{ServiceName}Schema.json": "// 数据结构定义"
}
```

## Thrift/Pigeon 服务格式

```json
{
    "{ServiceName}.java": "// 接口类代码",
    "{ServiceName}Test.java": "// 测试类代码",
    "{ServiceName}Test.json": "// 测试数据"
}
```

## 重要格式要求

**必须严格遵守以下规则：**

1. **返回格式必须是 JSON 对象（`{}`），绝对不能是 JSON 数组（`[]`）**
2. **JSON 对象的键必须是文件名（字符串），值必须是文件内容（字符串）**
3. **返回结果只能包含一个 JSON 对象，不能有任何其他内容**
4. **不要添加任何解释、说明或额外文本**
5. **确保 JSON 格式完全正确，可以被标准 JSON 解析器解析**
6. **严禁在 JSON 对象之外添加任何汉字、英文或其他语言的描述信息**
7. **输出必须是纯 JSON 格式，不允许有任何前缀、后缀或说明文字**
8. **禁止使用 Markdown 代码块包装 JSON 输出**

### 正确示例

```json
{
    "UserService.java": "public class UserService extends HttpRequestService { ... }",
    "UserServiceTest.java": "public class UserServiceTest { ... }"
}
```

### 错误示例（禁止）

```json
[
    {"file": "UserService.java", "content": "..."}
]
```

---

**注意事项：**

- 请确保所有代码完整可用，各文件之间保持一致性
- 返回结果必须严格按照上述 JSON 对象格式返回
- 如果遇到 JSON 格式有问题，需要你自行修正为正确的 JSON 格式
- **绝对禁止在输出中包含任何解释性文字、说明文本或描述信息**
- **输出内容必须且只能是一个有效的 JSON 对象，不允许有任何其他内容**
- **违反格式要求的输出将被视为无效响应**
