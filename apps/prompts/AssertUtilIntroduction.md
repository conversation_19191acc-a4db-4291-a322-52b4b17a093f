# Mario 测试框架断言工具指南

> 本文档提供了 Mario 测试框架中所有断言工具的详细说明和使用方法，帮助您高效地验证测试结果。
## 1. AssertUtil 断言工具

AssertUtil 是 Mario 框架中最常用的断言工具类，位于`com.meituan.toolchain.mario.util`包中，提供了丰富的校验方法。

### 1.1 ResponseMap 类介绍

ResponseMap 是 Mario 框架中用于封装 HTTP 响应的核心类。它提供了一系列方法来访问和操作响应数据，使得测试过程中的断言和数据提取变得更加便捷。

#### ResponseMap 的主要属性：

- `statusCode`: HTTP 响应状态码
- `responseBody`: 响应体内容
- `url`: 请求的 URL
- `headers`: HTTP 响应头
- `logMsg`: 日志信息
- `traceId`: 追踪 ID

#### ResponseMap 的主要方法：

| 方法 | 描述 | 返回值 | 使用场景 |
|-----|------|-------|---------|
| `getStatusCode()` | 获取 HTTP 状态码 | int | 验证请求是否成功 |
| `getResponseBody()` | 获取响应体内容 | String | 获取完整的响应数据 |
| `getUrl()` | 获取请求的 URL | String | 确认请求的目标地址 |
| `getHeader(String name)` | 获取指定名称的响应头 | String | 获取特定的 HTTP 头信息 |
| `getHeaders()` | 获取所有响应头 | Headers | 需要检查多个头信息时使用 |
| `getLogMsg()` | 获取日志信息 | String | 调试和日志分析 |
| `getTraceId()` | 获取追踪 ID | String | 跟踪请求在系统中的流转 |
| `getValueByJsonPath(String jsonPath)` | 通过 JSONPath 获取响应体中的值 | T (泛型) | 提取响应中的特定字段 |
| `getJSONObjectByJsonPath(String jsonPath)` | 通过 JSONPath 获取 JSONObject | JSONObject | 提取响应中的 JSON 对象 |
| `getJSONArrayByJsonPath(String jsonPath)` | 通过 JSONPath 获取 JSONArray | JSONArray | 提取响应中的 JSON 数组 |
| `getArraySizeByJsonPath(String jsonPath)` | 获取 JSONPath 指定的数组大小 | int | 验证返回的数组元素数量 |

#### 示例：

```java
// 获取 HTTP 状态码
int statusCode = response.getStatusCode();
// 获取响应体中的特定值
String username = response.getValueByJsonPath("$.data.user.name");
// 获取 JSON 对象
JSONObject userObject = response.getJSONObjectByJsonPath("$.data.user");
// 获取 JSON 数组
JSONArray itemsArray = response.getJSONArrayByJsonPath("$.data.items");
// 获取数组大小
int itemsCount = response.getArraySizeByJsonPath("$.data.items");
// 获取特定响应头
String contentType = response.getHeader("Content-Type");
// 获取追踪 ID
String traceId = response.getTraceId();
```

使用 ResponseMap 类可以大大简化测试代码，提高测试效率。它与 AssertUtil 中的其他断言方法配合使用，可以构建出强大而灵活的测试用例。

### 1.2 JSON Schema 校验

JSON Schema 校验用于验证 JSON 数据结构是否符合预期的模式定义。每个测试类对应一个 Schema 文件，文件名与类名相同。

#### Schema 文件结构

Schema 文件的结构取决于使用的断言方法：

1. **使用 assertJsonSchema 方法**：
   - Schema 文件直接定义整个测试类的 JSON 结构。
   - 适用于测试类中所有方法共用一个 schema 的情况。

2. **使用 assertJsonSchemaByMethodName 方法**：
   - Schema 文件使用 key-value 形式定义每个测试方法的 Schema。
   - key 为方法名，value 为该方法对应的 Schema。

#### JSON Schema 校验方法

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `assertJsonSchema(ResponseMap responseMap)` | JSON Schema 校验，使用整个类的 Schema | 当测试类中所有方法共用一个 Schema 时使用 |
| `assertJsonSchema(ResponseMap responseMap, AssertLevel level)` | JSON Schema 校验，使用整个类的 Schema，并设置校验等级 | 当需要控制校验严格程度，且所有方法共用一个 Schema 时使用 |
| `assertJsonSchema(ResponseMap responseMap, String fileName)` | JSON Schema 校验，使用指定文件名的 Schema | 当需要使用自定义命名的 Schema 文件进行校验时使用 |
| `assertJsonSchema(ResponseMap responseMap, String fileName, AssertLevel level)` | JSON Schema 校验，使用指定文件名的 Schema，并设置校验等级 | 当需要使用自定义命名的 Schema 文件，并控制校验严格程度时使用 |
| `assertJsonSchemaByMethodName(ResponseMap responseMap)` | JSON Schema 校验，使用当前方法名对应的 Schema | 当测试类中每个方法有独立的 Schema 时使用 |
| `assertJsonSchemaByMethodName(ResponseMap responseMap, AssertLevel level)` | JSON Schema 校验，使用当前方法名对应的 Schema，并设置校验等级 | 当需要控制校验严格程度，且每个方法有独立的 Schema 时使用 |
| `assertJsonSchemaByMethodName(ResponseMap responseMap, String methodName)` | JSON Schema 校验，使用指定方法名的 Schema | 当需要使用特定方法名的 Schema 进行校验时使用 |
| `assertJsonSchemaByMethodName(ResponseMap responseMap, String methodName, AssertLevel level)` | JSON Schema 校验，使用指定方法名的 Schema，并设置校验等级 | 当需要指定方法名并控制校验严格程度时使用 |
| `assertJsonSchemaByMethodName(ResponseMap responseMap, String methodName, String fileName)` | JSON Schema 校验，使用指定方法名和文件名的 Schema | 当需要在指定文件中查找特定方法名的 Schema 时使用 |
| `assertJsonSchemaByMethodName(ResponseMap responseMap, String methodName, String fileName, AssertLevel level)` | JSON Schema 校验，使用指定方法名和文件名的 Schema，并设置校验等级 | 当需要在指定文件中查找特定方法名的 Schema，并控制校验严格程度时使用 |
| `assertJsonSchemaWithTag(ResponseMap responseMap, String tagToCollect)` | JSON Schema 校验，带自定义上传信息 | 当需要在校验过程中收集额外信息时使用 |
| `assertJsonSchemaWithTag(ResponseMap responseMap, String tagToCollect, AssertLevel level)` | JSON Schema 校验，带自定义上传信息，并设置校验等级 | 当需要在校验过程中收集额外信息，并控制校验严格程度时使用 |
| `assertJsonSchemaWithTag(ResponseMap responseMap, String fileName, String tagToCollect)` | JSON Schema 校验，使用指定文件名的 Schema，带自定义上传信息 | 当需要使用自定义命名的 Schema 文件，并收集额外信息时使用 |
| `assertJsonSchemaWithTag(ResponseMap responseMap, String fileName, String tagToCollect, AssertLevel level)` | JSON Schema 校验，使用指定文件名的 Schema，带自定义上传信息，并设置校验等级 | 当需要使用自定义命名的 Schema 文件，收集额外信息，并控制校验严格程度时使用 |

此外，AssertUtil 还提供了针对 String 类型响应的 JSON Schema 校验方法：

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `assertJsonSchema(String response)` | JSON Schema 校验，使用整个类的 Schema | 当响应为字符串，且测试类中所有方法共用一个 Schema 时使用 |
| `assertJsonSchema(String response, AssertLevel level)` | JSON Schema 校验，使用整个类的 Schema，并设置校验等级 | 当响应为字符串，需要控制校验严格程度，且所有方法共用一个 Schema 时使用 |
| `assertJsonSchema(String response, String fileName, String msg)` | JSON Schema 校验，使用指定文件名的 Schema，带自定义错误消息 | 当响应为字符串，需要使用自定义命名的 Schema 文件，并自定义错误消息时使用 |
| `assertJsonSchema(String response, String fileName, String msg, AssertLevel level)` | JSON Schema 校验，使用指定文件名的 Schema，带自定义错误消息，并设置校验等级 | 当响应为字符串，需要使用自定义命名的 Schema 文件，自定义错误消息，并控制校验严格程度时使用 |
| `assertJsonSchemaByMethodName(String response)` | JSON Schema 校验，使用当前方法名对应的 Schema | 当响应为字符串，且测试类中每个方法有独立的 Schema 时使用 |
| `assertJsonSchemaByMethodName(String response, AssertLevel level)` | JSON Schema 校验，使用当前方法名对应的 Schema，并设置校验等级 | 当响应为字符串，需要控制校验严格程度，且每个方法有独立的 Schema 时使用 |
| `assertJsonSchemaByMethodName(String response, String methodName)` | JSON Schema 校验，使用指定方法名的 Schema | 当响应为字符串，需要使用特定方法名的 Schema 进行校验时使用 |
| `assertJsonSchemaByMethodName(String response, String methodName, AssertLevel level)` | JSON Schema 校验，使用指定方法名的 Schema，并设置校验等级 | 当响应为字符串，需要指定方法名并控制校验严格程度时使用 |

#### AssertLevel 说明

Mario 框架中的 AssertLevel 枚举值包含：**P0**，**P1**，**P2**

#### 示例：

```java
import com.dianping.rhino.cluster.common.util.AssertUtil;

public class UserServiceTest {
   // 当测试类中所有方法共用一个 Schema 时
   @Test
   public void testGetUser() {
      ResponseMap response = userService.getUser(1);
      AssertUtil.assertJsonSchema (response);
   }

   @Test
   public void testCreateUser() {
      ResponseMap response = userService.createUser(userData);
      AssertUtil.assertJsonSchema(response);
   }
}

public class OrderServiceTest {
   // 当测试类中每个方法有独立的 Schema 时
   @Test
   public void testCreateOrder() {
      ResponseMap response = orderService.createOrder(orderData);
      AssertUtil.assertJsonSchemaByMethodName(response);
   }

   @Test
   public void testUpdateOrder() {
      ResponseMap response = orderService.updateOrder(1, updateData);
      AssertUtil.assertJsonSchemaByMethodName(response);
   }

   // 使用指定方法名的 Schema
   @Test
   public void testCustomMethod() {
      ResponseMap response = orderService.customMethod();
      AssertUtil.assertJsonSchemaByMethodName(response, "testCustomMethod");
   }

   // 设置校验等级
   @Test
   public void testCriticalOperation() {
      ResponseMap response = orderService.criticalOperation();
      AssertUtil.assertJsonSchemaByMethodName(response, "testCriticalOperation", AssertLevel.P0);
   }
}
```

#### Schema 文件示例：

1. 使用 assertJsonSchema 的 Schema 文件 (`UserServiceTest.json`):

```json
{
   "$schema": "http://json-schema.org/draft-07/schema#",
   "type": "object",
   "properties": {
      "id": { "type": "integer" },
      "name": { "type": "string" },
      "email": { "type": "string", "format": "email" }
   },
   "required": ["id", "name", "email"]
}
```

2. 使用 assertJsonSchemaByMethodName 的 Schema 文件 (`OrderServiceTest.json`):

```json
{
   "testCreateOrder": {
      "$schema": "http://json-schema.org/draft-07/schema#",
      "type": "object",
      "properties": {
         "orderId": { "type": "string" },
         "status": { "type": "string", "enum": ["created"] },
         "items": { "type": "array", "items": { "type": "object" } }
      },
      "required": ["orderId", "status", "items"]
   },
   "testUpdateOrder": {
      "$schema": "http://json-schema.org/draft-07/schema#",
      "type": "object",
      "properties": {
         "orderId": { "type": "string" },
         "status": { "type": "string", "enum": ["updated", "cancelled"] },
         "updatedAt": { "type": "string", "format": "date-time" }
      },
      "required": ["orderId", "status", "updatedAt"]
   },
   "testCustomMethod": {
      "$schema": "http://json-schema.org/draft-07/schema#",
      "type": "object",
      "properties": {
         "result": { "type": "string" }
      },
      "required": ["result"]
   },
   "testCriticalOperation": {
      "$schema": "http://json-schema.org/draft-07/schema#",
      "type": "object",
      "properties": {
         "success": { "type": "boolean" },
         "operationId": { "type": "string" }
      },
      "required": ["success", "operationId"]
   }
}
```
使用这种方式，您可以根据测试类的需求选择合适的断言方法和对应的 Schema 文件格式。当使用 `assertJsonSchema` 时，所有测试方法共用一个 Schema；而使用 `assertJsonSchemaByMethodName` 时，每个测试方法可以有独立的 Schema 定义。这样可以提供更大的灵活性，同时保持 Schema 管理的简洁性。

### 1.3 HTTP 返回码校验
用于验证 HTTP 请求的返回状态码是否符合预期。

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `assertHttp200(ResponseMap responseMap)` | 校验 HTTP 返回状态码为 200 | 验证请求是否成功 |
| `assertHttpCode(ResponseMap responseMap, int expectCode)` | 校验 HTTP 返回状态码为指定值 | 验证特定状态码（如 201、404 等） |
| `assertHttp200(ResponseMap responseMap, AssertLevel level)` | 校验 HTTP 返回状态码为 200，可设置校验点等级 | 需要控制校验严格程度时使用 |
| `assertHttpCode(ResponseMap responseMap, int expectCode, AssertLevel level)` | 校验 HTTP 返回状态码为指定值，可设置校验点等级 | 验证特定状态码并控制校验严格程度 |

**示例：**

```java
// 验证返回码为 200
assertHttp200(response);

// 验证返回码为 201（创建成功）
assertHttpCode(response, 201);

// 验证返回码为 404，并设置为必要校验点
assertHttpCode(response, 404, AssertLevel.P0);
```

### 1.4 JSONPath 相关校验

使用 JSONPath 表达式对 JSON 数据进行精确校验。

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `assertJSONPathExists(ResponseMap responseMap, String JSONPath)` | 校验 JSONPath 在 responseMap.body 中存在 | 验证特定字段是否存在 |
| `assertJsonPathValueEquals(ResponseMap responseMap, JSONObject expect, String jsonPath)` | 校验 ResponseMap.body 和期望的 JSON 中相同 JSONPath 值相等 | 比较两个 JSON 中特定路径的值 |
| `assertJsonPathValueEquals(ResponseMap responseMap, String expect, String jsonPath)` | 校验 ResponseMap.body 中的 JSONPath 值和期望的字符串相等 | 验证特定路径的值是否等于预期字符串 |
| `assertJsonPathValuesSizeEquals(ResponseMap responseMap, JSONObject expect, String jsonPath)` | 校验 ResponseMap.body 和期望的 JSON 中相同 JSONPath 返回的数组大小相等 | 比较两个 JSON 中特定路径的数组大小 |
| `assertJsonPathValuesSizeEquals(ResponseMap responseMap, int expectSize, String jsonPath)` | 校验 ResponseMap.body 中的 JSONPath 返回数组的大小等于指定的大小 | 验证特定路径的数组大小是否符合预期 |
| `assertJSONPathExists(ResponseMap responseMap, String JSONPath, AssertLevel level)` | 校验 JSONPath 在 responseMap.body 中存在，可设置校验点等级 | 验证特定字段是否存在，并控制校验严格程度 |
| `assertJsonPathValueEquals(ResponseMap responseMap, JSONObject expect, String jsonPath, AssertLevel level)` | 校验 ResponseMap.body 和期望的 JSON 中相同 JSONPath 值相等，可设置校验点等级 | 比较两个 JSON 中特定路径的值，并控制校验严格程度 |
| `assertJsonPathValuesSizeEquals(ResponseMap responseMap, JSONObject expect, String jsonPath, AssertLevel level)` | 校验 ResponseMap.body 和期望的 JSON 中相同 JSONPath 返回的数组大小相等，可设置校验点等级 | 比较两个 JSON 中特定路径的数组大小，并控制校验严格程度 |
| `assertJsonPathValuesSizeEquals(ResponseMap responseMap, int expectSize, String jsonPath, AssertLevel level)` | 校验 ResponseMap.body 中的 JSONPath 返回数组的大小等于指定的大小，可设置校验点等级 | 验证特定路径的数组大小是否符合预期，并控制校验严格程度 |

**示例：**

```java
// 验证 JSONPath 是否存在
assertJSONPathExists(response, "$.data.items");

// 验证 JSONPath 值是否等于预期字符串
assertJsonPathValueEquals(response, "success", "$.status");

// 验证 JSONPath 数组大小
assertJsonPathValuesSizeEquals(response, 5, "$.data.items");

// 验证 JSONPath 值是否等于预期，并设置为必要校验点
assertJsonPathValueEquals(response, expectedJson, "$.data", AssertLevel.MUST);
```

### 1.5 JSONObject 比较校验

用于比较两个 JSON 对象是否相等，支持多种比较模式和排除特定字段。

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `assertNewJsonEquals(JSONObject srcJson, JSONObject desJson, JSONCompareMode mode, List<String> excludePaths)` | 比较两个 JSONObject 相等，可以指定比较模式，指定排除项 | 需要灵活控制 JSON 比较方式时使用 |
| `assertNewJsonEquals(ResponseMap responseMap, JSONObject desJson, JSONCompareMode mode, List<String> excludePaths)` | 校验 ResponseMap.body 和 desJson 对象相等，可以指定比较模式，指定排除项 | 比较响应体与预期 JSON，并排除特定字段 |
| `assertNewJsonEquals(Object actual, Object expect, JSONCompareMode mode, List<String> excludePaths)` | 校验两个对象值相等，当对象为自定义类型时通过 JSON 比较，可以指定比较模式，指定排除项 | 比较两个自定义对象，通过 JSON 序列化后比较 |
| `assertNewJsonEquals(JSONObject srcJson, JSONObject desJson, JSONCompareMode mode, List<String> excludePaths, AssertLevel level)` | 比较两个 JSONObject 相等，可以指定比较模式，指定排除项，可设置校验点等级 | 需要灵活控制 JSON 比较方式并设置校验严格程度 |
| `assertNewJsonEquals(ResponseMap responseMap, JSONObject desJson, JSONCompareMode mode, List<String> excludePaths, AssertLevel level)` | 校验 ResponseMap.body 和 desJson 对象相等，可以指定比较模式，指定排除项，可设置校验点等级 | 比较响应体与预期 JSON，排除特定字段，并设置校验严格程度 |
| `assertNewJsonEquals(Object actual, Object expect, JSONCompareMode mode, List<String> excludePaths, AssertLevel level)` | 校验两个对象值相等，当对象为自定义类型时通过 JSON 比较，可以指定比较模式，指定排除项，可设置校验点等级 | 比较两个自定义对象，通过 JSON 序列化后比较，并设置校验严格程度 |

**示例：**

```java
// 比较两个 JSON 对象，忽略数组顺序，排除时间戳字段
List<String> excludePaths = Arrays.asList("$.timestamp", "$.data.items[*].createTime");
assertNewJsonEquals(actualJson, expectedJson, JSONCompareMode.LENIENT, excludePaths);

// 比较响应体与预期 JSON，严格模式，必要校验点
assertNewJsonEquals(response, expectedJson, JSONCompareMode.STRICT, null, AssertLevel.P0);

// 比较两个自定义对象
User actualUser = response.getValueByJsonPath("$.data.user", User.class);
assertNewJsonEquals(actualUser, expectedUser, JSONCompareMode.LENIENT, excludePaths);
```

### 1.6 其他校验方法

Mario 框架集成了 TestNG 的断言方法，可以使用 TestNG 提供的所有断言功能。

详细信息请参考 [TestNG Assert 文档](http://static.javadoc.io/org.testng/testng/6.8.17/org/testng/Assert.html)。

## 2. JsonUnit 校验工具

[JsonUnit](https://github.com/lukas-krecan/JsonUnit) 是一个强大的 JSON 断言库，提供了更灵活的 JSON 比较功能。

**主要特性：**

- 支持忽略特定字段
- 支持正则表达式匹配
- 支持数组顺序无关比较
- 支持数值类型宽松比较

**相关资源：**
- [JsonUnit 断言框架用法总结](https://cloud.tencent.com/developer/article/1589202)
- [JsonUnit GitHub 仓库](https://github.com/lukas-krecan/JsonUnit)

**示例：**

```java
// 使用 JsonUnit 进行断言
assertThatJson(actualJson)
    .when(IGNORING_ARRAY_ORDER)
    .isEqualTo(expectedJson);

// 忽略特定字段
assertThatJson(actualJson)
    .whenIgnoringPaths("$.timestamp", "$.data.items[*].createTime")
    .isEqualTo(expectedJson);

// 使用正则表达式匹配
assertThatJson(actualJson)
    .inPath("$.id")
    .matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}");
```



## 3. JsonPathUtil 工具方法
JsonPathUtil 提供了操作 JSON 数据的工具方法，用于在测试过程中动态获取和修改 JSON 数据。

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `getJsonPathValue(Object json, String jsonPath)` | 获取 JSON 中指定路径的值 | 从 JSON 对象或字符串中提取特定字段值 |
| `getJsonPathValue(ResponseMap responseMap, String jsonPath)` | 从 ResponseMap 中获取指定路径的值 | 从 HTTP 响应中提取特定字段值 |
| `setJsonPathVaule(Object json, String jsonPath, Object value)` | 修改 JSON 中指定路径的值 | 在测试过程中动态修改 JSON 数据 |
| `deleteJsonPathKey(String jsonString, String jsonPath)` | 删除 JSON 字符串中指定路径的字段 | 在比较 JSON 时排除特定字段 |
| `deleteJsonPathKey(JSONObject jsonObject, String jsonPath)` | 删除 JSONObject 中指定路径的字段 | 在比较 JSON 对象时排除特定字段 |

**示例：**

```java
// 获取 JSON 中的特定字段值
Object userId = JsonPathUtil.getJsonPathValue(jsonObject, "$.user.id");

// 从响应中获取特定字段值
String status = JsonPathUtil.getJsonPathValue(response, "$.status");

// 修改 JSON 中的特定字段值
JSONObject json = new JSONObject();
json.put("name", "oldName");
json.put("age", 20);
JsonPathUtil.setJsonPathVaule(json, "$.name", "newName");

// 删除 JSON 中的特定字段
String jsonString = "{\"id\":123,\"timestamp\":1615897825,\"data\":{\"name\":\"test\"}}";
String cleanedJson = JsonPathUtil.deleteJsonPathKey(jsonString, "$.timestamp");

// 删除 JSONObject 中的特定字段
JSONObject jsonObject = new JSONObject();
jsonObject.put("id", 123);
jsonObject.put("timestamp", 1615897825);
JsonPathUtil.deleteJsonPathKey(jsonObject, "$.timestamp");
```
## 4. 基础断言方法

AssertUtil 类继承了 TestNG 的 Assert 类，并对其进行了扩展，提供了更多适用于 API 测试的断言方法。以下是 AssertUtil 中在 Assert 基础上对外提供的额外基础断言方法：

### 4.1 基本断言方法

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `assertEquals(Object actual, Object expected, String message)` | 断言两个对象相等 | 比较任意两个对象是否相等 |
| `assertEquals(ResponseMap responseMap, Object actual, Object expected, String message)` | 断言两个对象相等，并在失败时提供响应信息 | 在 API 测试中比较对象并提供详细错误信息 |
| `assertEquals(ResponseMap responseMap, Object actual, Object expected, String message, AssertLevel level)` | 断言两个对象相等，并在失败时提供响应信息和断言级别 | 在 API 测试中比较对象并提供详细错误信息，同时控制断言严格程度 |
| `assertTrue(boolean condition, String message)` | 断言条件为真 | 验证条件是否满足 |
| `assertTrue(ResponseMap responseMap, boolean condition, String message)` | 断言条件为真，并在失败时提供响应信息 | 在 API 测试中验证条件并提供详细错误信息 |
| `assertTrue(ResponseMap responseMap, boolean condition, String message, AssertLevel level)` | 断言条件为真，并在失败时提供响应信息和断言级别 | 在 API 测试中验证条件并提供详细错误信息，同时控制断言严格程度 |
| `assertFalse(boolean condition, String message)` | 断言条件为假 | 验证条件是否不满足 |
| `assertFalse(ResponseMap responseMap, boolean condition, String message)` | 断言条件为假，并在失败时提供响应信息 | 在 API 测试中验证条件不满足并提供详细错误信息 |
| `assertFalse(ResponseMap responseMap, boolean condition, String message, AssertLevel level)` | 断言条件为假，并在失败时提供响应信息和断言级别 | 在 API 测试中验证条件不满足并提供详细错误信息，同时控制断言严格程度 |
| `assertNotNull(Object object, String message)` | 断言对象不为 null | 验证对象是否存在 |
| `assertNotNull(ResponseMap responseMap, Object object, String message)` | 断言对象不为 null，并在失败时提供响应信息 | 在 API 测试中验证对象存在并提供详细错误信息 |
| `assertNotNull(ResponseMap responseMap, Object object, String message, AssertLevel level)` | 断言对象不为 null，并在失败时提供响应信息和断言级别 | 在 API 测试中验证对象存在并提供详细错误信息，同时控制断言严格程度 |
| `assertNotEquals(Object actual, Object expected, String message)` | 断言两个对象不相等 | 验证两个对象是否不同 |
| `assertNotEquals(ResponseMap responseMap, Object actual, Object expected, String message)` | 断言两个对象不相等，并在失败时提供响应信息 | 在 API 测试中验证对象不同并提供详细错误信息 |
| `assertNotEquals(ResponseMap responseMap, Object actual, Object expected, String message, AssertLevel level)` | 断言两个对象不相等，并在失败时提供响应信息和断言级别 | 在 API 测试中验证对象不同并提供详细错误信息，同时控制断言严格程度 |

### 4.2 字符串包含断言

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `assertContain(Object object, String message)` | 断言对象包含指定字符串 | 验证响应中是否包含特定文本 |
| `assertContain(Object object, String message, AssertLevel level)` | 断言对象包含指定字符串，并设置断言级别 | 验证响应中是否包含特定文本，同时控制断言严格程度 |
| `assertNotContain(Object object, String message)` | 断言对象不包含指定字符串 | 验证响应中是否不包含特定文本 |
| `assertNotContain(Object object, String message, AssertLevel level)` | 断言对象不包含指定字符串，并设置断言级别 | 验证响应中是否不包含特定文本，同时控制断言严格程度 |

### 4.3 JSON 比较方法

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `compareJSON(String expectWanted, String actualWanted, JSONCompareMode compareMode, String message, AssertLevel level)` | 比较两个 JSON 字符串是否相等 | 直接比较两个 JSON 字符串，不做任何预处理 |
| `assertBaseJsonEquals(Object actual, Object expect, JSONCompareMode mode, List<String> excludePaths, String message)` | 比较两个对象的 JSON 表示是否相等，可排除特定路径 | 比较两个对象的 JSON 表示，排除动态字段 |
| `assertBaseJsonEquals(Object actual, Object expect, JSONCompareMode mode, List<String> excludePaths, String message, AssertLevel level)` | 比较两个对象的 JSON 表示是否相等，可排除特定路径，并设置断言级别 | 比较两个对象的 JSON 表示，排除动态字段，同时控制断言严格程度 |
| `assertBaseJsonEquals(JSONObject actual, JSONObject expect, JSONCompareMode mode, List<String> fields, boolean isInclude, String message, AssertLevel level)` | 比较两个 JSONObject 是否相等，可选择只比较或排除特定字段 | 灵活控制 JSON 比较的范围和方式 |

**示例：**

```java
// 基本断言
assertEquals(actualValue, expectedValue, "值应该相等");
assertTrue(condition, "条件应该为真");
assertFalse(condition, "条件应该为假");
assertNotNull(object, "对象不应为空");

// 带响应信息的断言
assertEquals(response, actualValue, expectedValue, "值应该相等");
assertTrue(response, condition, "条件应该为真");
assertNotNull(response, object, "对象不应为空");

// 包含断言
assertContain(response.getResponseBody(), "success", "响应应包含成功信息");
assertNotContain(response.getResponseBody(), "error", "响应不应包含错误信息");

// JSON 比较
compareJSON(expectedJson, actualJson, JSONCompareMode.LENIENT, "JSON 应该相等", AssertLevel.P0);

// 排除特定字段的 JSON 比较
List<String> excludePaths = Arrays.asList("$.timestamp", "$.requestId");
assertBaseJsonEquals(actualObject, expectedObject, JSONCompareMode.LENIENT, excludePaths, "对象应该相等");

// 只比较特定字段的 JSON 比较
List<String> includeFields = Arrays.asList("$.id", "$.name");
assertBaseJsonEquals(actualJson, expectedJson, JSONCompareMode.STRICT, includeFields, true, "只比较指定字段", AssertLevel.P0);
```

## 5. TestX 链路校验工具

CompareInvokeParamsUtil 是用于校验 TestX 用例中间链路的工具类。

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `compareTypeofSpecialInvokeParams(CaseRequestModel request, List<RecordModel> recordModels, InvokeType invokeType, List<String> excludePaths)` | 比较 TestX 用例中间调用链路，支持 Mybatis、Squirrel、Thrift、Mafka 类型校验 | 验证特定类型的中间调用是否符合预期 |
| `compareInvokeParaWithDynamicField(CaseRequestModel request, List<RecordModel> recordModels, List<InvokeType> providedTypes, List<String> excludePaths)` | 比较 TestX 用例中间调用链路，支持动态字段排除校验 | 需要排除动态字段时的中间调用链路校验 |

**示例：**

```java
// 校验 Thrift 调用链路
List<String> excludePaths = Arrays.asList("$.timestamp", "$.requestId");
compareTypeofSpecialInvokeParams(request, recordModels, InvokeType.THRIFT, excludePaths);

// 校验多种类型的调用链路，并排除动态字段
List<InvokeType> types = Arrays.asList(InvokeType.MYBATIS, InvokeType.THRIFT);
compareInvokeParaWithDynamicField(request, recordModels, types, excludePaths);
```

## 6. TestX 链路数据获取工具

RecordModelUtil 提供了从 TestX 链路中获取数据的工具方法。

| 方法                                                                                                                                      | 描述                                                          |
|-----------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------|
| `getJSONArrayByJsonPathRecordModelFirstRequest(List<RecordModel> recordModelsList, String appkey, String methodName, String jsonPath)`  | 获取（符合appkey、methodName第一个请求）中jsonPath对应请求列表返回JSONArray      |
| `getJSONArrayByJsonPathRecordModelAllRequest(List<RecordModel> recordModelsList, String appkey, String methodName, String jsonPath)`    | 获取（符合appkey、methodName所有请求）中jsonPath对应请求列表返回List<JSONArray> |
| `getJSONObjectByJsonPathRecordModelFirstRequest(List<RecordModel> recordModelsList, String appkey, String methodName, String jsonPath)` | 获取（符合appkey、methodName第一个请求）中jsonPath对应JSONObject           |
| `getJSONObjectByJsonPathRecordModelAllRequest(List<RecordModel> recordModelsList, String appkey, String methodName, String jsonPath)`   | 获取（符合appkey、methodName所有请求）中jsonPath对应List<JSONObject>      |
| `getValueByJsonPathRecordModelFirstRequest(List<RecordModel> recordModelsList, String appkey, String methodName, String jsonPath)`      | 获取（符合appkey、methodName第一个请求）中jsonPath对应对应结果                 |
| `getValueByJsonPathRecordModelAllRequest(List<RecordModel> recordModelsList, String appkey, String methodName, String jsonPath)`        | 获取（符合appkey、methodName所有请求）中jsonPath对应对应结果                  |
| `getTargetAppkeyMethodRequestAllRequest(List<RecordModel> recordModelsList, String appkey, String methodName)`                          | 获取链路中符合Appkey、MethodName的所有请求，支持Pigeon、Thrift类型             |
| `getTargetAppkeyMethodFirstRequest(List<RecordModel> recordModelsList, String appkey, String methodName)`                               | 获取链路中符合Appkey、MethodName的第一个请求，支持Pigeon、Thrift类型            |
| `getTargetHttpRequestBodyAndHeaderAllRequest(List<RecordModel> recordModelsList, String requestURI)`                                    | 获取链路中符合requestURI的所有请求的body值和header值，支持Http类型               |
| `getTargetHttpRequestBodyAndHeaderFirstRequest(List<RecordModel> recordModelsList, String requestURI)`                                  | 获取链路中符合requestURI的第一个请求的body值和header值，支持Http类型              |
| `getTargetMqTopicFirstRequestId(List<RecordModel> recordModelsList, String topic)`                                                      | 获取链路中符合MQ topic的第一个请求的消息id，支持Mafka类型                        |
| `getTargetMqTopicFirstRequestContent(List<RecordModel> recordModelsList, String topic)`                                                 | 获取链路中符合MQ topic的第一个请求的消息内容，支持Mafka类型                        |
| `getTargetMqTopicAllRequestContent(List<RecordModel> recordModelsList, String topic)`                                                   | 获取链路中符合MQ topic的所有请求的消息内容，支持Mafka类型                         |
| `getTargetMybatisAllRequestContent(List<RecordModel> recordModelsList, String sql)`                                                     | 获取符合Mybatis sql的所有请求的消息内容                                   |
| `getTargetHttpRequestContainsExpectedUriAllRequest(List<RecordModel> recordModelsList, String requestURI)`                              | 获取包含预期url的Http RequestURI 所有请求中的body和header值                |

**示例：**

```java
// 获取特定服务第一次调用的数据
JSONObject userInfo = RecordModelUtil.getJSONObjectByJsonPathRecordModelFirstRequest(
                recordModels, "com.example.user-service", "getUserInfo", "$.data");

// 获取特定 HTTP 接口的所有调用
List<Map<String, Object>> allRequests = RecordModelUtil.getTargetHttpRequestBodyAndHeaderAllRequest(
        recordModels, "/api/v1/users");

// 获取特定 MQ 主题的消息内容
String messageContent = RecordModelUtil.getTargetMqTopicFirstRequestContent(
        recordModels, "user-event-topic");

// 获取特定 SQL 的执行内容
List<Object> sqlResults = RecordModelUtil.getTargetMybatisAllRequestContent(
        recordModels, "SELECT * FROM users WHERE id = ?");
```

## 7. 单接口幂等测试

提供了验证接口幂等性的工具方法。

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `interfaceIdempotentCheck(List<RecordModel> recordModels, String method, List<String> excludePaths)` | 接口幂等验证，发起两次调用，比较返回值和中间调用链路 | 验证接口的幂等性 |

**示例：**

```java
// 验证接口幂等性，排除时间戳和请求 ID 字段
List<String> excludePaths = Arrays.asList("$.timestamp", "$.requestId");
boolean isIdempotent = interfaceIdempotentCheck(recordModels, "createOrder", excludePaths);

// 断言接口是幂等的
Assert.assertTrue(isIdempotent, "接口应该是幂等的");
```

详细使用说明请参考：[单接口幂等测试使用文档](https://km.sankuai.com/page/1292470902)

## 8. 单接口异常注入

提供了接口异常注入的工具方法，用于测试系统在异常情况下的行为。

| 方法 | 描述 | 使用场景 |
|-----|------|---------|
| `interfaceFaultInject(List<RecordModel> recordModels, String entrance, InvokeType invokeType, String throwableClass, String subIdentityURI)` | 接口一级链路异常注入 | 测试系统在特定异常情况下的行为 |

**示例：**

```java
// 注入数据库异常
interfaceFaultInject(recordModels, "getUserInfo", InvokeType.MYBATIS, 
interfaceFaultInject(recordModels, "getUserInfo", InvokeType.MYBATIS, 
    "java.sql.SQLException", "SELECT * FROM users");

// 注入 Thrift 调用超时异常
interfaceFaultInject(recordModels, "createOrder", InvokeType.THRIFT,
    "com.meituan.service.TimeoutException", "com.example.order-service");
```

详细使用说明请参考：[单接口异常注入使用文档](https://km.sankuai.com/page/1311274462)

## 9. 常见使用场景和最佳实践

### 9.1 基本断言场景

```java
// 1. 验证 HTTP 状态码
assertHttp200(response);

// 2. 验证 JSON Schema
assertJsonSchema(response);

// 3. 验证特定字段值
assertJsonPathValueEquals(response, "success", "$.status");
assertJsonPathValueEquals(response, 200, "$.code");

// 4. 验证数组大小
assertJsonPathValuesSizeEquals(response, 5, "$.data.items");
```

### 9.2 复杂 JSON 比较

```java
// 1. 准备排除字段列表（动态生成的字段）
List<String> excludePaths = Arrays.asList(
                "$.timestamp",
                "$.requestId",
                "$.data.items[*].createTime",
                "$.data.items[*].updateTime"
        );

// 2. 使用宽松模式比较（忽略数组顺序）
assertNewJsonEquals(response, expectedJson, JSONCompareMode.LENIENT, excludePaths);

// 3. 使用 JsonUnit 进行更灵活的比较
assertThatJson(response.getBody())
        .whenIgnoringPaths(excludePaths.toArray(new String[0]))
        .when(IGNORING_ARRAY_ORDER)
    .isEqualTo(expectedJson);
```

### 9.3 链路数据验证

```java
// 1. 获取中间调用数据
JSONObject dbRequest = RecordModelUtil.getJSONObjectByJsonPathRecordModelFirstRequest(
                recordModels, "com.example.user-dao", "getUserById", "$.params[0]");

// 2. 验证中间调用参数
assertEquals(userId, dbRequest.getString("id"));

// 3. 验证整个调用链路
compareTypeofSpecialInvokeParams(request, recordModels, InvokeType.THRIFT, excludePaths);
```

### 9.4 异常场景测试

```java
// 1. 注入异常并验证系统行为
interfaceFaultInject(recordModels, "getUserInfo", InvokeType.MYBATIS, 
interfaceFaultInject(recordModels, "getUserInfo", InvokeType.MYBATIS, 
    "java.sql.SQLException", "SELECT * FROM users");

// 2. 验证系统返回了正确的错误响应
assertHttpCode(response, 500);
assertJsonPathValueEquals(response, "database_error", "$.error.code");
```

### 9.5 幂等性测试

```java
// 1. 执行幂等性测试
boolean isIdempotent = interfaceIdempotentCheck(recordModels, "createOrder", excludePaths);

// 2. 断言接口是幂等的
Assert.assertTrue(isIdempotent, "创建订单接口应该是幂等的");
```

---

> 注意：使用断言工具时，请根据实际测试需求选择合适的方法和参数，合理设置断言等级（AssertLevel）以控制测试的严格程度。

## 10. AssertUtil 使用注意事项

在使用 Mario 测试框架的 AssertUtil 断言工具时，需要注意以下几点：

### 10.1 断言等级选择

- AssertUtil 支持设置断言等级（AssertLevel），包括 P0、P1、P2 三个级别
- 应根据测试需求合理设置断言等级，以控制测试的严格程度
- 关键功能点应使用 P0 级别，确保必要校验点不被忽略

### 10.2 JSON Schema 校验注意事项

- 每个测试类对应一个 Schema 文件，文件名与类名相同
- 根据使用的断言方法选择合适的 Schema 文件结构：
  - `assertJsonSchema` 方法：Schema 文件直接定义整个测试类的 JSON 结构
  - `assertJsonSchemaByMethodName` 方法：Schema 文件使用 key-value 形式定义每个测试方法的 Schema

### 10.3 JSON 比较时的灵活性

- 使用 `excludePaths` 参数排除动态生成的字段（如时间戳、请求ID等）
- 根据需要选择合适的比较模式（JSONCompareMode）：
  - STRICT：严格模式，要求完全匹配
  - LENIENT：宽松模式，忽略数组顺序等

### 10.4 ResponseMap 的正确使用

- ResponseMap 是封装 HTTP 响应的核心类，提供了丰富的方法访问响应数据
- 使用 `getValueByJsonPath` 等方法从响应中提取特定字段值
- 使用 `getJSONObjectByJsonPath` 和 `getJSONArrayByJsonPath` 提取 JSON 对象和数组

### 10.5 链路数据验证

- 使用 RecordModelUtil 从 TestX 链路中获取数据
- 使用 CompareInvokeParamsUtil 校验中间调用链路
- 验证链路数据时需要指定正确的 appkey、methodName 等参数

### 10.6 异常场景和幂等性测试

- 使用 `interfaceFaultInject` 进行异常注入测试
- 使用 `interfaceIdempotentCheck` 验证接口幂等性
- 异常测试时需要指定正确的异常类型和调用类型

### 10.7 断言方法的选择

- 根据实际测试需求选择合适的断言方法
- 基本断言（如 assertEquals、assertTrue）适用于简单场景
- 复杂 JSON 比较需要使用 assertNewJsonEquals 或 JsonUnit
- HTTP 状态码校验使用 assertHttp200 或 assertHttpCode
- 选择使用的断言方法必须准确符合测试要求，避免使用功能相似但不完全匹配的方法
- 严格按照选择的校验方法的参数类型来填写参数，避免类型不匹配导致的运行时异常
- 对于重载方法，确保选择最适合当前测试场景的版本，不要混淆不同参数列表的方法

### 10.8 错误信息的提供

- 在断言方法中提供清晰的错误信息，便于定位问题
- 可以将 ResponseMap 作为参数传入断言方法，以便在失败时提供更详细的错误信息

正确使用这些断言工具可以提高测试效率，确保测试的全面性和准确性。
