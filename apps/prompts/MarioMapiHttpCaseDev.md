# Mario MAPI Http 测试用例编写步骤

## 一、创建文件结构
需要创建的文件包括：
1. 接口类：`{ServiceName}.java`（必须继承MApiRequestService）
2. 测试类：`{ServiceName}Test.java`
3. 数据文件：`{ServiceName}Test.json`

文件结构应为：
```
src/test/java/{用户输入}/{service}/
├── {ServiceName}.java       # 接口定义类文件
├── testsuites/              # 测试用例类目录
│   └── {ServiceName}Test.java
├── data/                     # 测试数据目录
    └── {ServiceName}Test.json # 测试数据文件
```
## 二、HOST配置【重要】

在编写HTTP接口测试前，**必须**确认接口的正确HOST：

1. **默认HOST**：如果接口使用项目默认HOST，在env.properties中已有`HOST=xxx`配置
   ```java
   // 使用默认HOST
   ServiceInterface service = retrofit().create(ServiceInterface.class);
   ```

2. **自定义HOST**：针对不同服务的接口，需要添加自定义HOST
   ```
   # 在src/test/profiles/环境/env.properties中添加自定义HOST
   SERVICE_HOST=https://service.example.com/
   ```
   ```java
   // 在接口类中使用自定义HOST
   ServiceInterface service = retrofit("SERVICE_HOST").create(ServiceInterface.class);
   ```

3. **HOST配置注意事项**：
   - 确保env.properties中HOST末尾已添加"/"（避免URL拼接问题）
   - 开发前推荐使用Postman等工具验证接口是否可访问


## 三、编写接口类
接口类必须继承`HttpRequestService`，定义HTTP接口的请求方法：
```java
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.toolchain.mario.model.ResponseMap;
import com.meituan.toolchain.mario.protocol.http.MApiRequestService;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.*;
// 接口定义
public class {ServiceName}Api extends MApiRequestService {
    public interface MdoAPIServices {
        @POST("mdo/v1")
        Call<ResponseBody> postMdoData(@QueryMap JSONObject jsonQuery);
    }

    public ResponseMap postMdoData(JSONObject request) throws Exception {
        MdoAPIServices service = retrofit().create(MdoAPIServices.class);//使用配置文件的HOST初始化服务
        MdoAPIServices service = retrofit("hostA").create(MdoAPIServices.class);//// 根据接口所属服务选择合适的HOST
        Call<ResponseBody> call = service.postMdoData(request);
        return sendRequest(call);
    }
}
```

## 四、编写测试类
根据测试方法数量选择合适的数据提供方式：
```java
import com.alibaba.fastjson.JSONObject;
import com.meituan.toolchain.mario.framework.JsonFileDataProvider;
import com.meituan.toolchain.mario.model.ResponseMap;
import com.meituan.toolchain.mario.util.AssertUtil;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import lombok.extern.slf4j.Slf4j;
@Slf4j
public class {Service}ApiTest {
    private {ServiceName}Api {serviceName}Api;
    
    @BeforeClass
    public void setup() {
        {serviceName} = new {ServiceName}Api();
    }
    
    // 单个测试方法使用 dataProvider = "data"
    @Test(dataProvider = "data", dataProviderClass = JsonFileDataProvider.class)
    // 多个测试方法使用 dataProvider = "dataMethod",如下注解，请勿将这部分注释生成在代码中
    // @Test(dataProvider = "dataMethod", dataProviderClass = JsonFileDataProvider.class)
    public void getUserInfoTest(JSONObject request, JSONObject expect, String comments) throws Exception {
        log.info("Test comments: {}", comments);
        //调用接口接收结果
        ResponseMap response = {serviceName}.getUserInfo(request);
        //调用 AssertUtil 进行校验
        // 1. 断言HTTP状态码
        AssertUtil.assertHttp200(response);
        // 解析响应JSON
        String responseBody = response.getResponseBody();
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        // 进行其他必要验证
        // ...
    }
  
}
```

## 五、测试数据准备
根据测试方法数量选择合适的数据格式：
**单方法测试**（使用`dataProvider = "data"`）:
```json
[
  {
    "comments": "测试场景1 - 获取用户信息",
    "request": {
      "userId": 12345
    },
    "response": {
      "code": 200,
      "data": {
        "username": "test_user",
        "age": 25
      }
    }
  }
]
```

**多方法测试**（使用`dataProvider = "dataMethod"`）:
```json
{
  "getUserInfoTest": [
    {
      "comments": "测试场景1 - 获取用户信息",
      "request": {
        "userId": 12345
      },
      "response": {
        "code": 200,
        "data": {
          "username": "test_user",
          "age": 25
        }
      }
    }
  ],
  "getUserListTest": [
    // 其他方法的测试数据
  ]
}
```

## 六、注意事项

1. **接口类必选**：MapiHTTP协议测试必须创建接口类，且必须继承`MApiRequestService`。

2. **包路径管理**：必须严格按照示例代码中提供的各个类的包路径来导入

3. **数据提供方式**：
   - 单测试方法：使用`dataProvider = "data"`
   - 多测试方法：使用`dataProvider = "dataMethod"`
   - 一个测试类中所有测试方法必须使用相同的`dataProvider`方式
   - 使用了`dataProvider`的测试方法的参数必须是 request，expect，comments
   - 参数`request`对应数据文件中的`request`,参数`expect`对应数据文件中的`response`,参数`comments`对应数据文件中的`comments`

4. **校验**：
   - 使用`AssertUtil.assertHttp200(response)`验证HTTP状态码
   - 根据方法数量选择适当的校验方式

5. **配置管理**：
   - 环境配置在`src/test/profiles/xxx/env.properties`
   - 基础配置如`HOST`、`PROXY`等在properties文件中设置
   - 配置加载顺序：`config.properties` -> `env.properties` -> 环境变量
   - 配置管理可以调用`MarioConfigManage`工具查看详细内容

6. **路径管理**：
   - 如果用户指定路径，生成路径为`src/test/java/` + 用户路径
   - 默认生成路径为`src/test/java/` + 协议类型 + `/`

7. **测试数据格式**：
   - JSON格式，必须包含`comments`、`request`和`response`字段
   - 多方法测试需要以方法名作为键组织数据


   