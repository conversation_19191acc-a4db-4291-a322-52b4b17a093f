# Mario Pigeon 测试用例编写步骤

## 一、创建文件结构
编写 Pigeon 协议测试用例需要创建以下文件：
1. 测试类：`{ServiceName}Test.java`
2. 数据文件：`{ServiceName}Test.json`
3. Schema文件：`{ServiceName}Test.json`
注意：与 HTTP 协议不同，Pigeon 协议不需要创建接口类。

文件结构应为：
```
src/test/java/{用户输入}/{service}/
├── testsuites/              # 测试类目录
│   └── PaymentServiceTest.java  # 测试类，命名格式为{ServiceName}Test.java
├── data/                    # 数据文件目录
│   └── PaymentServiceTest.json
└── schema/                   #schema文件目录
    └── PaymentServiceTest.json
```

## 二、编写测试类
Pigeon 测试类使用 `@PigeonAPI` 注解定义服务信息：  
Pigeon 测试有两种模式：泛化调用和 Jar 包调用。请先明确本次是使用Jar包调用还是泛化调用。 如果不确定，请向用户确认。  
Jar包调用时用户会提供Jar包信息(groupId,artifactId,version)和接口信息(url,interfaceName,methodName)，泛化调用时用户需要提供接口信息(url,interfaceName,methodName,paramTypes)。

Mario PigeonAPI注解
```java
public @interface PigeonAPI {
    String name() default ""; //注解名称

    String url() default ""; //接口路径

    String desc() default ""; //接口描述 

    String methodName() default ""; //接口方法名

    String callType() default "sync";

    int timeout() default 0; //请求超时时间，单位为毫秒，未设置默认读取 RPC_TIMEOUT*1000

    String[] paramTypes() default {}; //接口参数类型，泛化调用时必须指定

    String localAppkey() default "com.sankuai.toolchain.mario"; //调用端appkey
}
```
###1. 泛化调用方式
使用 `@PigeonAPI` 注解标注服务信息，通过 `PigeonProcessor.invoke()` 进行调用,`PigeonProcessor`位于`com.meituan.toolchain.mario.AnnotationProcessor`包中：

```java
import com.alibaba.fastjson.JSONObject;
import com.meituan.toolchain.mario.annotation.PigeonAPI;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;
import com.meituan.toolchain.mario.AnnotationProcessor.PigeonProcesser;
import com.meituan.toolchain.mario.framework.JsonFileDataProvider;
import com.meituan.toolchain.mario.util.AssertUtil;

@Slf4j
public class OrderServiceTest {
   @PigeonAPI(
           url = "com.meituan.order.service",
           methodName = "createOrder",
           paramTypes = {"OrderRequest"}
   )
   @Test(description = "创建订单", dataProvider = "data", dataProviderClass = JsonFileDataProvider.class)
   public void createOrderTest(JSONObject request, JSONObject expect, String comments) {
      String response = PigeonProcessor.invoke(request);
      AssertUtil.assertJsonSchemaByMethodName(response);
      AssertUtil.assertJsonEquals(JSON.parseObject(response), expect);
      // 业务校验
   }
}
```

在 `@PigeonAPI` 注解中需要指定：
- `url`：Pigeon 服务的 URL
- `methodName`：调用的方法名
- `paramTypes`：方法参数类型（可选）

###2.jar包调用方式
直接注入接口实例，通过实际对象进行调用：
```java
import com.alibaba.fastjson.JSONObject;
import com.dianping.receipt.query.api.ReceiptQueryService;
import com.dianping.receipt.query.dto.ReceiptDTO;
import com.meituan.toolchain.mario.AnnotationProcessor.PigeonProcessor;
import com.meituan.toolchain.mario.annotation.PigeonAPI;
import com.meituan.toolchain.mario.framework.JsonFileDataProvider;
import com.meituan.toolchain.mario.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;
import org.testng.annotations.Test;
// 注意要把对应调用的服务import进来，比如
import com.sankuai.dztheme.deal.DealProductService;

@Slf4j
public class PigeonServiceTest {
    @PigeonAPI(url = "com.sankuai.dztheme.deal.DealProductService")
    DealProductService dealProductService;
    
    // 单个测试方法使用 dataProvider = "data"
    @Test(dataProvider = "data", dataProviderClass = JsonFileDataProvider.class)
    // 多个测试方法使用 dataProvider = "dataMethod",如下注解，请勿将这部分注释生成在代码中
    // @Test(dataProvider = "dataMethod", dataProviderClass = JsonFileDataProvider.class)
    public void getReceiptTtest(JSONObject request, JSONObject expect, String comments) throws Exception {
        Long userId = request.getLong("userId");
        Long receiptId = request.getLong("receiptId");
        // 对应的方法名假设为getReceipt，这是一个例子
        ReceiptDTO a = dealProductService.getReceipt(userId, receiptId);
        AssertUtil.assertNotNull(a);
        AssertUtil.assertJsonSchemaByMethodName(JSON.toJSONString(a));
        // 其他业务自定义校验
    }
}
```
jar包调用方式需要：
1.pom中引入要测试的 jar
2.被测试的服务实例需要定义

## 三、准备数据文件
根据测试方法数量选择合适的数据格式：

**单方法测试**（使用`dataProvider = "data"`）:
```json
[
  {
    "comments": "测试场景1 - 创建订单",
    "request": {
      "userId": 12345,
      "productId": 54321,
      "quantity": 1
    },
    "response": {
      "code": 200,
      "data": {
        "orderId": "ORD123456",
        "status": "CREATED"
      }
    }
  }
]
```

**多方法测试**（使用`dataProvider = "dataMethod"`）:
```json
{
  "createOrderTest": [
    {
      "comments": "测试场景1 - 创建订单",
      "request": {
        "userId": 12345,
        "productId": 54321,
        "quantity": 1
      },
      "response": {
        "code": 200,
        "data": {
          "orderId": "ORD123456",
          "status": "CREATED"
        }
      }
    }
  ],
  "cancelOrderTest": [
    {
      "comments": "测试场景1 - 取消订单",
      "request": {
        "orderId": "ORD123456"
      },
      "response": {
        "code": 200,
        "data": {
          "status": "CANCELLED"
        }
      }
    }
  ]
}
```

## 四、结果校验
Pigeon 测试通常使用 `AssertUtil`提供的方法进行结果校验，例如：
- 进行JsonSchema校验时：
```java
AssertUtil.assertJsonSchema(response);
```


## 五、注意事项

1. **文件要求差异**：
   - Pigeon 协议不需要创建接口类
   - Pigeon 协议需要创建 Schema 文件
   - 需要测试类、数据文件和 Schema 文件
2. **包路径管理**：必须严格按照示例代码中提供的各个类的包路径来导入

3. **数据提供方式**：
    - 单测试方法：使用 `dataProvider = "data"`
    - 多测试方法：使用 `dataProvider = "dataMethod"`
    - 一个测试类中所有测试方法必须使用相同的 `dataProvider` 方式
    - 使用了`dataProvider`的测试方法的参数必须是 request，expect，comments
    - 参数`request`对应数据文件中的`request`,参数`expect`对应数据文件中的`response`,参数`comments`对应数据文件中的`comments`

4. **PigeonAPI 注解**：
    - 必须指定 `url` 参数（Pigeon 服务的标识）
    - 必须指定 `methodName` 参数（要调用的方法名）
    - 可选指定 `paramTypes` 参数（方法参数类型列表）

5. **结果校验**：
    - 建议使用 `AssertUtil`提供的方法进行结果验证
    - 调用 `MarioFrameworkToolsIntroduction`工具获取 Mario 自动化框架提供的完整校验工具使用方法

6. **配置管理**：
    - RPC 直连配置（如需直连）：在 config.properties 中设置 `${appkey}=${IP}`
    - 命令行直连配置：设置环境变量 `DIRECT_IP_{appkey}` (将`.`转为`_`)
    - RPC 超时配置：通过 `RPC_TIMEOUT` 设置（默认 5 秒）
    - 配置管理可以调用`MarioConfigManage`工具查看详细内容

7. **路径管理**：
    - 如果用户指定路径，生成路径为 `src/test/java/` + 用户路径
    - 默认生成路径为 `src/test/java/pigeon/`

8. **测试数据格式**：
    - JSON 格式，包含 `comments`、`request` 和 `response` 字段
    - 多方法测试需要以方法名作为键组织数据 