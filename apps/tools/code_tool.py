#!/usr/bin/python3
# coding: utf-8

import datetime
from langchain_core.tools import tool

from apps.service.code_service import process_main
from settings.settings import logger


@tool(return_direct=True, description="从用户的输入中的获取repo，uuid_dir，port，以及case_type做为参数")
def code_executor(repo, uuid_dir, port, case_type):
    logger.info(f"repo: {repo}, uuid_dir: {uuid_dir}, port: {port}")
    logger.info(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    result = process_main(repo, uuid_dir, port, case_type)
    logger.info(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    return result
