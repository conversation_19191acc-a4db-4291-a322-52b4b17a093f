from apps.service.teminal_service import download_file_s3
from settings.settings import logger
from langchain_core.tools import tool


@tool(return_direct=True, description="输入参数repo为仓库地址，输入参数uuid_dir为文件夹路径，输入参数port为端口号")
def terminal_execute(repo, uuid_dir, port):
    logger.info(f"repo: {repo}, uuid_dir: {uuid_dir}, port: {port}")
    result = download_file_s3(repo, uuid_dir, port)
    return result

