

import datetime
from langchain_core.tools import tool

from apps.service.code_service import markdown_generate
from settings.settings import logger


@tool(return_direct=True, description="输入的内容是文件夹路径")
def markdown_executor(uuid_dir):
    # logger.info(f"test_case_id: {test_case_id}, user_mis: {user_mis}")
    logger.info(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print(f"这是文件夹路径{uuid_dir}")
    result, interface_md = markdown_generate(uuid_dir)
    logger.info(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    return {
        "folder_path": result,
        "case_type": interface_md
    }
