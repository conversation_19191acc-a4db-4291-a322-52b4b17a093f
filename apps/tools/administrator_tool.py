#!/usr/bin/python3
# coding: utf-8

from langchain_core.tools import tool
from settings.settings import logger
from apps.service.repo_service import git_permission


# 不加可能会导致多次重复调用，因为react会自主决策
@tool(
    return_direct=True,
    description="检查用户是否拥有指定 Git 仓库的访问权限。输入参数：repository (仓库地址), user_mis (用户名)。"
                "如果用户有权限访问，则返回 有权限，如果用户无权访问则返回 无权限。如果出现异常则返回 检查权限失败"
)
def judge_admin(repository: str, user_mis: str):
    logger.info(f"repository: {repository}, user_mis: {user_mis}")
    return git_permission(repository, user_mis)
