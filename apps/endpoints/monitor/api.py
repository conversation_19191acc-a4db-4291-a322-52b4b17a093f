#!/usr/bin/python3
# coding: utf-8

from fastapi import APIRouter, Request
router = APIRouter()


@router.get('/monitor/alive')
def alive():
    return {
        'status': 'OK',
        'name': 'toolchain-muse-app',
    }


@router.get('/health')
@router.head('/health')
def health_check(request: Request):
    """健康检查端点，支持GET和HEAD方法"""
    return {
        'status': 'OK',
        'name': 'toolchain-muse-app',
    }
