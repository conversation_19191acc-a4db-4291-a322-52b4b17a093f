#!/usr/bin/python3
# coding: utf-8
"""
AG-UI协议适配器
实现AG-UI协议规范，将LangGraph工作流事件转换为AG-UI标准事件
"""

import uuid
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
from enum import Enum
from pydantic import BaseModel
from fastapi import WebSocket
import logging
logger = logging.getLogger(__name__)
from apps.service.work_flow import run_agent_workflow


class EventType(str, Enum):
    """AG-UI事件类型枚举"""
    # 生命周期事件
    RUN_STARTED = "run_started"
    RUN_FINISHED = "run_finished"
    
    # 文本消息事件
    TEXT_MESSAGE_START = "text_message_start"
    TEXT_MESSAGE_DELTA = "text_message_delta"
    TEXT_MESSAGE_END = "text_message_end"
    
    # 工具调用事件
    TOOL_CALLS_START = "tool_calls_start"
    TOOL_CALLS_ARGS = "tool_calls_args"
    TOOL_CALLS_END = "tool_calls_end"
    
    # 状态管理事件
    STATE_SNAPSHOT = "state_snapshot"
    STATE_DELTA = "state_delta"
    MESSAGES_SNAPSHOT = "messages_snapshot"
    
    # 特殊事件
    ERROR = "error"
    INTERRUPT = "interrupt"


class BaseEvent(BaseModel):
    """AG-UI基础事件模型"""
    type: EventType
    timestamp: str = None
    thread_id: str = None
    run_id: str = None
    
    def __init__(self, **data):
        if 'timestamp' not in data:
            data['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        super().__init__(**data)


class RunStartedEvent(BaseEvent):
    """运行开始事件"""
    type: EventType = EventType.RUN_STARTED


class RunFinishedEvent(BaseEvent):
    """运行结束事件"""
    type: EventType = EventType.RUN_FINISHED


class TextMessageStartEvent(BaseEvent):
    """文本消息开始事件"""
    type: EventType = EventType.TEXT_MESSAGE_START
    message_id: str
    role: str = "assistant"
    name: Optional[str] = None


class TextMessageDeltaEvent(BaseEvent):
    """文本消息增量事件"""
    type: EventType = EventType.TEXT_MESSAGE_DELTA
    message_id: str
    delta: str


class TextMessageEndEvent(BaseEvent):
    """文本消息结束事件"""
    type: EventType = EventType.TEXT_MESSAGE_END
    message_id: str


class ToolCallsStartEvent(BaseEvent):
    """工具调用开始事件"""
    type: EventType = EventType.TOOL_CALLS_START
    tool_calls: List[Dict[str, Any]]


class ToolCallsArgsEvent(BaseEvent):
    """工具调用参数事件"""
    type: EventType = EventType.TOOL_CALLS_ARGS
    tool_call_id: str
    args_delta: str


class ToolCallsEndEvent(BaseEvent):
    """工具调用结束事件"""
    type: EventType = EventType.TOOL_CALLS_END
    tool_call_results: List[Dict[str, Any]]


class StateSnapshotEvent(BaseEvent):
    """状态快照事件"""
    type: EventType = EventType.STATE_SNAPSHOT
    snapshot: Dict[str, Any]


class StateDeltaEvent(BaseEvent):
    """状态增量事件"""
    type: EventType = EventType.STATE_DELTA
    delta: List[Dict[str, Any]]  # JSON Patch格式


class MessagesSnapshotEvent(BaseEvent):
    """消息快照事件"""
    type: EventType = EventType.MESSAGES_SNAPSHOT
    messages: List[Dict[str, Any]]


class ErrorEvent(BaseEvent):
    """错误事件"""
    type: EventType = EventType.ERROR
    error: str
    error_code: Optional[str] = None


class InterruptEvent(BaseEvent):
    """中断事件"""
    type: EventType = EventType.INTERRUPT
    message: str
    interrupt_type: str
    options: Optional[List[str]] = None


class AGUIAdapter:
    """AG-UI协议适配器类"""
    
    def __init__(self, thread_id: str = None, run_id: str = None):
        """
        初始化AG-UI适配器
        
        Args:
            thread_id: 线程ID，用于标识会话
            run_id: 运行ID，用于标识单次执行
        """
        self.thread_id = thread_id or str(uuid.uuid4())
        self.run_id = run_id or str(uuid.uuid4())
        self.messages: List[Dict[str, Any]] = []
        self.state: Dict[str, Any] = {
            "workflow_status": "idle",
            "active_nodes": [],
            "completed_nodes": [],
            "current_node": None,
            "node_outputs": {}
        }
        self.current_message_id: Optional[str] = None
        self.message_buffer: str = ""
    
    def _create_base_event_data(self) -> Dict[str, Any]:
        """创建基础事件数据"""
        return {
            "thread_id": self.thread_id,
            "run_id": self.run_id,
            "timestamp": datetime.utcnow().isoformat() + 'Z'
        }
    
    def _add_message(self, role: str, content: str, name: str = None) -> str:
        """添加消息到消息列表"""
        message_id = str(uuid.uuid4())
        message = {
            "id": message_id,
            "role": role,
            "content": content
        }
        if name:
            message["name"] = name
        
        self.messages.append(message)
        return message_id
    
    def _update_state(self, updates: Dict[str, Any]):
        """更新状态"""
        self.state.update(updates)
    
    async def convert_workflow_events_to_agui(
        self, 
        user_input_messages: List[Dict[str, Any]], 
        websocket: WebSocket = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        将LangGraph工作流事件转换为AG-UI事件
        
        Args:
            user_input_messages: 用户输入消息列表
            websocket: WebSocket连接（可选）
            
        Yields:
            AG-UI格式的事件字典
        """
        try:
            # 发送运行开始事件
            yield RunStartedEvent(**self._create_base_event_data()).dict()
            
            # 添加用户消息到消息列表
            for msg in user_input_messages:
                self._add_message(msg.get("role", "user"), msg.get("content", ""))
            
            # 发送初始消息快照
            yield MessagesSnapshotEvent(
                messages=self.messages,
                **self._create_base_event_data()
            ).dict()
            
            # 处理工作流事件
            async for event in run_agent_workflow(
                user_input_messages=user_input_messages,
                deep_thinking_mode=True,
                websocket=None  # 不直接传递WebSocket，由适配器处理
            ):
                # 转换工作流事件为AG-UI事件
                agui_events = await self._convert_single_event(event, websocket)
                for agui_event in agui_events:
                    yield agui_event
            
            # 发送运行结束事件
            yield RunFinishedEvent(**self._create_base_event_data()).dict()
            
        except Exception as e:
            logger.error(f"AG-UI适配器错误: {e}", exc_info=True)
            yield ErrorEvent(
                error=str(e),
                error_code="ADAPTER_ERROR",
                **self._create_base_event_data()
            ).dict()
    
    async def _convert_single_event(
        self, 
        event: Dict[str, Any], 
        websocket: WebSocket = None
    ) -> List[Dict[str, Any]]:
        """
        转换单个工作流事件为AG-UI事件
        
        Args:
            event: 工作流事件
            websocket: WebSocket连接（用于处理中断）
            
        Returns:
            AG-UI事件列表
        """
        agui_events = []
        event_type = event.get("event")
        
        if event_type == "workflow_status":
            status = event.get("status")
            if status == "started":
                self._update_state({"workflow_status": "running"})
            elif status == "completed":
                self._update_state({"workflow_status": "completed"})
            
            # 发送状态快照
            agui_events.append(StateSnapshotEvent(
                snapshot=self.state.copy(),
                **self._create_base_event_data()
            ).dict())
        
        elif event_type == "node_status":
            node = event.get("node")
            status = event.get("status")
            
            if status == "started":
                if node not in self.state["active_nodes"]:
                    self.state["active_nodes"].append(node)
                self.state["current_node"] = node
            
            # 发送状态增量更新
            agui_events.append(StateDeltaEvent(
                delta=[
                    {"op": "replace", "path": "/current_node", "value": node},
                    {"op": "replace", "path": "/active_nodes", "value": self.state["active_nodes"]}
                ],
                **self._create_base_event_data()
            ).dict())
        
        elif event_type == "model_response":
            node = event.get("node")
            content = event.get("content", "")
            
            # 开始新的文本消息
            message_id = str(uuid.uuid4())
            self.current_message_id = message_id
            
            agui_events.append(TextMessageStartEvent(
                message_id=message_id,
                role="assistant",
                name=node,
                **self._create_base_event_data()
            ).dict())
            
            # 发送文本内容（分块发送以模拟流式输出）
            chunk_size = 50  # 每次发送50个字符
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i + chunk_size]
                agui_events.append(TextMessageDeltaEvent(
                    message_id=message_id,
                    delta=chunk,
                    **self._create_base_event_data()
                ).dict())
            
            # 结束文本消息
            agui_events.append(TextMessageEndEvent(
                message_id=message_id,
                **self._create_base_event_data()
            ).dict())
            
            # 添加到消息列表
            self._add_message("assistant", content, node)
            
            # 发送消息快照更新
            agui_events.append(MessagesSnapshotEvent(
                messages=self.messages,
                **self._create_base_event_data()
            ).dict())
        
        elif event_type == "tool_call_result":
            node = event.get("node")
            status = event.get("status")
            tool_result = event.get("tool_result")
            
            if status == "started":
                # 工具调用开始
                tool_calls = [{
                    "id": str(uuid.uuid4()),
                    "type": "function",
                    "function": {
                        "name": f"{node}_tool",
                        "arguments": json.dumps(tool_result) if tool_result else "{}"
                    }
                }]
                
                agui_events.append(ToolCallsStartEvent(
                    tool_calls=tool_calls,
                    **self._create_base_event_data()
                ).dict())
            
            elif status == "completed":
                # 工具调用结束
                tool_call_results = [{
                    "tool_call_id": str(uuid.uuid4()),
                    "result": tool_result
                }]
                
                agui_events.append(ToolCallsEndEvent(
                    tool_call_results=tool_call_results,
                    **self._create_base_event_data()
                ).dict())
        
        elif event_type == "node_chain_end":
            node = event.get("node")
            
            # 更新节点状态
            if node in self.state["active_nodes"]:
                self.state["active_nodes"].remove(node)
            if node not in self.state["completed_nodes"]:
                self.state["completed_nodes"].append(node)
            
            # 存储节点输出
            self.state["node_outputs"][node] = {
                "input_data": event.get("input_data", {}),
                "output_data": event.get("output_data", {})
            }
            
            # 发送状态更新
            agui_events.append(StateSnapshotEvent(
                snapshot=self.state.copy(),
                **self._create_base_event_data()
            ).dict())
        
        return agui_events
    
    async def handle_interrupt(
        self, 
        message: str, 
        interrupt_type: str, 
        websocket: WebSocket
    ) -> str:
        """
        处理中断事件
        
        Args:
            message: 中断消息
            interrupt_type: 中断类型
            websocket: WebSocket连接
            
        Returns:
            用户响应
        """
        # 发送中断事件
        interrupt_event = InterruptEvent(
            message=message,
            interrupt_type=interrupt_type,
            **self._create_base_event_data()
        )
        
        await websocket.send_text(json.dumps(interrupt_event.dict()))
        
        # 等待用户响应
        while True:
            response = await websocket.receive_json()
            if response.get("type") != "heartbeat":
                return response.get("input", "")