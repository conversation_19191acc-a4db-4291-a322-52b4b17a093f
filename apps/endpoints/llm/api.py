#!/usr/bin/python3
# coding: utf-8

from starlette.websockets import WebSocketDisconnect

from apps.model.api_request import LLMAnalysisRequest
from apps.service.api_service import get_llm_response_for_muse
from apps.service.format_content_service import ModelResponseParser
from apps.service.repo_service import run_mario_test_command, muse_repo_pr_command
from settings.settings import logger
from apps.service.work_flow import run_agent_workflow
from fastapi import APIRouter, HTTPException
from fastapi import WebSocket
from .ag_ui_api import router as ag_ui_router


router = APIRouter()

# 包含AG-UI路由
router.include_router(ag_ui_router, tags=['ag-ui'])

parser = ModelResponseParser()


@router.websocket("/ws/chat")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket 端点，处理实时聊天连接
    """
    await websocket.accept()

    try:
        while True:
            # 接收用户消息
            data = await websocket.receive_json()
            logger.info(f"Received message: {data}")
            user_input = data.get("input", "")

            if not user_input:
                logger.info(f"这是心跳信息，直接返回: {data}")
                await websocket.send_json({"error": "输入不能为空"})
                continue

            # 处理用户请求并发送响应
            try:
                # 传入websocket参数，以便工作流可以通过WebSocket获取用户输入
                async for event in run_agent_workflow(
                        user_input_messages=[{"role": "user", "content": user_input}],
                        deep_thinking_mode=True,
                        websocket=websocket  # 传入WebSocket连接
                ):
                    event_type = event.get("event")
                    if event_type == "node_status":
                        status = event.get("status")
                        node = event.get("node")
                        if node == "planner" and status == "started":
                            await websocket.send_json({
                                'type': 'node_status',
                                'status': status,
                                'node': node,
                                'message': "正在分析您的任务：我正在为您制定详细的任务规划方案，请稍候片刻。我会尽快为您提供一个全面而有效的计划"
                            })
                        if node == "coder" and status == "started":
                            message = "正在为您导入Mario代码到右侧编译器中，请稍后..."
                            await websocket.send_json({
                               'type': 'node_status',
                               'status': status,
                               'node': node,
                               'message': message
                            })
                    elif event_type == "workflow_status":
                        status = event.get("status")
                        message = "已接收到你的任务，我将立即开始处理..." if status == 'started' else "用户本次任务已结束"
                        await websocket.send_json({
                            'type': 'workflow_status',
                            'status': status,
                            'message': message
                        })
                    elif event_type == "tool_call_result":
                        node = event.get("node")
                        if node == "administrator" and event.get("status") == "started":
                            tool_result = event.get("tool_result")
                            # 格式化权限校验消息
                            formatted_message = parser.format_permission_check_message(tool_result)
                            await websocket.send_json({
                                'type': 'tool_call_result',
                                'node': node,
                                'content': formatted_message,
                                'message': f"节点 {node} 工具调用结果已返回"
                            })
                        if node == "administrator" and event.get("status") == "completed":
                            logger.info("节点administrator工具调用结束")
                            await websocket.send_json({
                                'type': 'tool_call_result',
                                'node': node,
                                'content': event.get("tool_result"),
                                'message': f"节点 {node} 工具调用结果已返回"
                            })
                        if node == "markdownor" and event.get("status") == "started":
                            await websocket.send_json({
                                'type': 'tool_call_result',
                                'node': node,
                                'status': "started",
                                'content': "正在为您生成完整的Markdown代码文件，请稍后...",
                                'message': f"节点 {node} 工具调用结果已返回"
                            })
                        if node == "markdownor" and event.get("status") == "completed":
                            logger.info("节点markdownor工具调用结束")
                            tool_result = event.get("tool_result")
                            md_result = parser.get_markdown_file_content(tool_result)
                            response = {
                                'type': 'tool_call_result',
                                'node': node,
                                'status': "completed",
                                'content': tool_result,
                                'message': f"节点 {node} 工具调用结果已返回"
                            }
                            if md_result["success"]:
                                response["content"] = md_result["content"]
                            elif md_result["error"]:
                                response["content"] = md_result["error"]
                            await websocket.send_json(response)
                    elif event_type == "model_response":
                        node = event.get("node")
                        model_name = event.get("model_name")
                        model_response = {
                            'type': 'model_response',
                            'node': node,
                            'content': event.get("content"),
                            'model_name': model_name,
                            'tokens': event.get("tokens"),
                            'message': f"节点 {node} 的模型 {model_name} 返回了回复"
                        }
                        if node == "planner":
                            # 格式化回复内容
                            content = parser.get_formatted_content(model_response, "full")
                            model_response['content'] = content
                        await websocket.send_json(model_response)
                    elif event_type == "node_chain_end":
                        node = event.get("node")
                        if node == "reporter":
                            input_data = event.get("input_data")
                            muse_input = parser.format_reporter_for_muse(input_data)
                            await websocket.send_json({
                                'type': 'reporter_node_chain_end',
                                'node': node,
                                "muse_input": muse_input,
                                # 'content': event.get("output_data"),
                                # 'message': f"节点 {node} 已完成"
                            })
            except Exception as e:
                logger.error(f"处理请求失败: {e}", exc_info=True)
                await websocket.send_json({'type': 'error', 'error': str(e)})

    except WebSocketDisconnect:
        logger.info("WebSocket 连接已关闭")
    except Exception as e:
        logger.error(f"WebSocket 错误: {e}", exc_info=True)
        await websocket.close()


@router.post("/llm/analysis")
async def llm_analysis_endpoint(request: LLMAnalysisRequest):
    """
    语意分析接口，封装api_service中的get_llm_response_for_muse方法
    """
    try:
        result = get_llm_response_for_muse(
            interrupt_type=request.interrupt_type,
            input_content=request.input_content
        )

        if result is None:
            return "大模型分析用户语意出现异常"

        return result

    except Exception as e:
        logger.error(f"语意分析失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"语意分析失败: {str(e)}")


@router.post("/mario/test")
async def mario_test_endpoint(request: dict):
    """
    Mario测试接口
    """
    try:
        # 从字典中获取参数
        mario_case = request.get("mario_case", "")
        remote_port = request.get("remote_port", 0)

        # 尝试将remote_port转换为整数
        try:
            remote_port = int(remote_port)
        except (ValueError, TypeError):
            logger.warning(f"remote_port转换为整数失败: {remote_port}")

        logger.info(
            f"MarioTestRequest 入参: mario_case={mario_case}, remote_port={remote_port}, 类型: {type(remote_port)}")

        result = run_mario_test_command(
            mario_case=mario_case,
            remote_port=remote_port
        )
        return result
    except Exception as e:
        logger.error(f"Mario测试执行失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Mario测试执行失败: {str(e)}")


@router.post("/mario/pr")
async def run_agent_endpoint(request: dict):
    """
    发起MarioPr接口
    """
    try:
        # 从字典中获取参数
        repo = request.get("repo", "")
        remote_port = request.get("remote_port", "")
        user_mis = request.get("user_mis", "")

        # 记录入参信息
        logger.info(f"MarioPrRequest 入参: repo={repo}, remote_port={remote_port}, user_mis={user_mis}")

        result = muse_repo_pr_command(
            repo=repo,
            remote_port=remote_port,
            user_mis=user_mis
        )
        return result
    except Exception as e:
        logger.error(f"发起Mario用例PR失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"发起Mario用例PR失败: {str(e)}")


async def main():
    import uvicorn
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware

    app = FastAPI()

    # 确保路由正确注册
    app.include_router(router, prefix="")  # 确保没有额外的前缀

    # CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=8123,
        log_level="info"
    )
    server = uvicorn.Server(config)
    await server.serve()

if __name__ == "__main__":
    import logging
    import asyncio
    logger = logging.getLogger(__name__)
    """
    当直接运行此脚本时，启动API服务
    """
    try:
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        # 运行异步主函数
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断了程序")
    except Exception as e:
        logger.error(f"运行过程中发生错误: {e}", exc_info=True)