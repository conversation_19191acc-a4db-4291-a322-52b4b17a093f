from typing import Literal
from typing_extensions import TypedDict
from langgraph.graph import MessagesState
from pydantic import BaseModel

# Define routing options
OPTIONS = [] + ["FINISH"]


class Router(TypedDict):
    """Worker to route to next. If no workers needed, route to FINISH."""

    next: Literal[*OPTIONS]


class UserInfo(BaseModel):
    repository: str = ''
    user_mis: str = ''
    test_case_id: str = ''
    branch: str = ''

    def to_dict(self):
        return self.model_dump()


class State(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Constants
    TEAM_MEMBERS: list[str]
    TEAM_MEMBER_CONFIGRATIONS: dict[str, dict]

    # Runtime Variables
    next: str
    full_plan: str
    deep_thinking_mode: bool
    search_before_planning: bool
    user_input: str  # 添加用户输入字段
    user_info: UserInfo
    uuid_str: str
    port: str
    mario_case: str
    mode: str
    is_full: bool
    corrector_attempt_count: int
    markdown_content: str
    case_type: str




