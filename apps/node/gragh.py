
from langgraph.checkpoint.memory import InMemorySaver

from langgraph.graph import StateGraph, START

from apps.node.node import planner_node, administrator_node, coder_node, \
    reporter_node, human_node, markdown_node, generator_node, judge_node, corrector_node, repo_detail_node
from apps.node.state_types import State

checkpointer = InMemorySaver()


def build_graph():
    graph = StateGraph(State)
    graph.add_edge(START, 'judge')
    graph.add_node("judge", judge_node)
    graph.add_node("generator", generator_node)
    graph.add_node("corrector", corrector_node)
    graph.add_node("repo_corrector", repo_detail_node)
    graph.add_node("planner", planner_node)
    graph.add_node("administrator", administrator_node)
    graph.add_node("human_node", human_node)
    graph.add_node("markdownor", markdown_node)
    graph.add_node("coder", coder_node)
    graph.add_node("reporter", reporter_node)


    graph.add_edge("markdownor", "human_node")
    graph.add_edge("coder", "reporter")
    # 添加中断点，要求用户确认
    return graph.compile(checkpointer=checkpointer)






