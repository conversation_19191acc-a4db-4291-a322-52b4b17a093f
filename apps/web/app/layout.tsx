import type { Metadata } from 'next';
import { Providers } from '@/components/providers';
import localFont from 'next/font/local';
import '@workspace/ui/globals.css';

// 使用本地字体
const fontSans = localFont({
    src: [
        {
            path: './fonts/Geist-Regular.woff2',
            weight: '400',
            style: 'normal'
        },
        {
            path: './fonts/Geist-Medium.woff2',
            weight: '500',
            style: 'normal'
        },
        {
            path: './fonts/Geist-Bold.woff2',
            weight: '700',
            style: 'normal'
        }
    ],
    variable: '--font-sans'
});

// 等宽字体
const fontMono = localFont({
    src: './fonts/GeistMono-Regular.woff2',
    variable: '--font-mono'
});

export const metadata: Metadata = {
    title: 'Muse Studio',
    description: 'Muse Studio',
    icons: [{ rel: 'icon', url: '/favicon.svg' }]
};

export default function RootLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang='en' suppressHydrationWarning>
            <body className={`${fontSans.variable} ${fontMono.variable} font-sans antialiased`}>
                <Providers>{children}</Providers>
            </body>
        </html>
    );
}
