import {CopilotRuntime, copilotRuntimeNextJSAppRouterEndpoint, OpenAIAdapter,} from "@copilotkit/runtime";
import {NextRequest} from "next/server";
import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL,
});

const serviceAdapter = new OpenAIAdapter({
  openai,
  model: process.env.OPENAI_MODEL || "gpt-4o-2024-11-20"
});

const runtime = new CopilotRuntime();

export const POST = async (req: NextRequest) => {
const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter,
    endpoint: req.nextUrl.pathname,
});

return handleRequest(req);
};
