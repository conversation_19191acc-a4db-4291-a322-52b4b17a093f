import { NextRequest } from 'next/server';
import { CopilotRuntime, copilotRuntimeNextJSAppRouterEndpoint, OpenAIAdapter } from '@copilotkit/runtime';
import OpenAI from 'openai';
import { mastra } from '../../../lib/mastra';

/**
 * 创建 OpenAI 客户端实例
 * 使用自定义的 API 配置
 */
const openai = new OpenAI({
  apiKey: '1914304559263223873',
  baseURL: 'https://aigc.sankuai.com/v1/openai/native/',
});

/**
 * 处理本地 Mastra 集成的 API 路由
 * 通过 CopilotKit 运行时处理代理请求
 */
const runtime = new CopilotRuntime({
  agents: [
    {
      name: 'agentic_chat',
      description: '智能天气助手，提供准确的天气信息查询服务',
      agent: mastra.getAgent('agentic_chat')
    }
  ]
});

/**
 * 创建 CopilotKit 端点处理器
 */
const handler = copilotRuntimeNextJSAppRouterEndpoint({
  runtime,
  serviceAdapter: new OpenAIAdapter({ openai }),
  endpoint: '/api/mastra',
});

/**
 * 导出各个 HTTP 方法处理器
 */
export const GET = handler.GET;
export const POST = handler.POST;
export const OPTIONS = handler.OPTIONS;