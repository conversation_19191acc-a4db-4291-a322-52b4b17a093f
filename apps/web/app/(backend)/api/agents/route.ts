/**
 * Agent 列表 API 路由
 * 提供可用 agents 的列表信息
 */

import { NextResponse } from 'next/server';
import { getAllIntegrations } from '@workspace/agent-registry/server';

// Agent ID 到路由路径的映射规则
const getAgentRoute = (agentId: string): string => {
  // 使用新的参数化路由：直接使用 agent ID
  return `/${agentId}`;
};

/**
 * 获取可用的 agents 列表
 * @param req - 请求对象
 * @returns agents 列表
 */
export async function GET() {
  try {
    // 从integration注册中心获取所有integrations
    const allIntegrations = await getAllIntegrations();

    return NextResponse.json({
      agents: allIntegrations.map((integration) => {
        return {
          id: integration.id,
          name: integration.name,
          description: integration.description,
          route: getAgentRoute(integration.id),
          type: 'integration',
          originalId: integration.id,
          version: integration.version
        };
      })
    });
  } catch (error) {
    console.error('Error getting agents list:', error);

    // 提供fallback数据
    const fallbackAgents = [
      {
        id: 'haiku',
        name: 'Haiku Integration',
        description: '专业的诗歌生成助手，能够创作优美的中英双语诗歌',
        route: '/haiku',
        type: 'integration',
        originalId: 'haiku',
        version: '0.0.1'
      },
      {
        id: 'mario',
        name: 'Mario Integration',
        description: 'Mario是基于TestNG的Java自动化测试框架，可以自动生成EC和Thrift泛化的自动化测试用例',
        route: '/mario',
        type: 'integration',
        originalId: 'mario',
        version: '0.0.1'
      }
    ];

    return NextResponse.json({
      agents: fallbackAgents
    });
  }
}
