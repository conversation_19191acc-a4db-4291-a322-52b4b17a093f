import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Docker from 'dockerode';
import dockerConfig from '@/config/docker';

const docker = new Docker({
  host: dockerConfig.host,
  port: dockerConfig.port,
  version: dockerConfig.version
});

/**
 * 停止容器（不删除）
 * POST /webapi/docker/stop?containerId=xxx
 */
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const containerId = searchParams.get('containerId');

    if (!containerId) {
      return NextResponse.json(
        {
          success: false,
          error: '未提供容器ID'
        },
        { status: 400 }
      );
    }

    console.log(`开始停止容器: ${containerId}`);

    try {
      const container = docker.getContainer(containerId);

      // 检查容器状态
      let containerInfo;
      try {
        containerInfo = await container.inspect();
        console.log(`容器当前状态: ${containerInfo.State.Status}`);
      } catch (inspectError) {
        const errorMessage = inspectError instanceof Error ? inspectError.message : String(inspectError);
        console.log('容器检查错误:', errorMessage);

        if (errorMessage.includes('no such container') || errorMessage.includes('404')) {
          console.log(`容器 ${containerId} 不存在，无需停止`);
          return NextResponse.json({
            success: true,
            message: '容器不存在，无需停止'
          });
        }

        throw inspectError;
      }

      // 如果容器已经停止，直接返回成功
      if (!containerInfo.State.Running) {
        console.log(`容器 ${containerId} 已经停止`);
        return NextResponse.json({
          success: true,
          message: '容器已经停止'
        });
      }

      // 停止容器
      console.log(`正在停止运行中的容器: ${containerId}`);
      await container.stop({ t: 10 }); // 给容器10秒时间优雅关闭

      // 再次检查状态确认已停止
      const updatedInfo = await container.inspect();
      console.log(`容器停止后状态: ${updatedInfo.State.Status}`);

      return NextResponse.json({
        success: true,
        message: '容器已成功停止',
        container: {
          id: updatedInfo.Id,
          name: updatedInfo.Name.replace('/', ''),
          status: updatedInfo.State.Status,
          running: updatedInfo.State.Running
        }
      });
    } catch (containerError) {
      const errorMessage = containerError instanceof Error ? containerError.message : String(containerError);
      console.error('容器操作错误:', errorMessage);

      // 处理容器不存在的情况
      if (errorMessage.includes('no such container') || errorMessage.includes('404')) {
        return NextResponse.json({
          success: true,
          message: '容器不存在，无需停止'
        });
      }

      // 处理容器已经停止的情况
      if (errorMessage.includes('already stopped') || errorMessage.includes('not running')) {
        return NextResponse.json({
          success: true,
          message: '容器已经停止'
        });
      }

      throw containerError;
    }
  } catch (error: unknown) {
    console.error('停止容器失败:', error);
    const errorMessage = error instanceof Error ? error.message : '停止容器失败';

    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      { status: 500 }
    );
  }
}
