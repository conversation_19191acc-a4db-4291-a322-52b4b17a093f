import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import dockerConfig from '@/config/docker';
import https from 'https';

/**
 * 检查容器健康状态
 * 通过访问容器的/health端点来验证nginx和内部服务是否正常运行
 */
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const webPort = searchParams.get('webPort');

        if (!webPort) {
            return NextResponse.json({ success: false, error: '未提供端口号' }, { status: 400 });
        }

        const healthUrl = `https://${dockerConfig.host}:${webPort}/health`;
        console.log(`检查容器健康状态: ${healthUrl}`);

        try {
            // 使用Node.js原生https模块进行请求，避免fetch的SSL问题
            const response = await new Promise<{ ok: boolean; status: number; data: string }>((resolve, reject) => {
                const url = new URL(healthUrl);
                const options = {
                    hostname: url.hostname,
                    port: url.port,
                    path: url.pathname,
                    method: 'GET',
                    rejectUnauthorized: false, // 忽略SSL证书验证
                    timeout: 5000
                };

                console.log(`发起健康检查请求: ${url.hostname}:${url.port}${url.pathname}`);

                const req = https.request(options, (res) => {
                    console.log(`健康检查响应状态: ${res.statusCode} ${res.statusMessage}`);
                    let data = '';
                    res.on('data', (chunk) => {
                        data += chunk;
                    });
                    res.on('end', () => {
                        console.log(`健康检查响应数据: "${data}"`);
                        resolve({
                            ok: res.statusCode! >= 200 && res.statusCode! < 300,
                            status: res.statusCode!,
                            data: data
                        });
                    });
                });

                req.on('error', (error) => {
                    console.log(`健康检查请求错误: ${error.message}`);
                    reject(error);
                });

                req.on('timeout', () => {
                    console.log('健康检查请求超时');
                    req.destroy();
                    reject(new Error('Request timeout'));
                });

                req.end();
            });

            if (response.ok) {
                const healthData = response.data.trim();
                console.log(`容器健康检查成功: ${healthData}`);

                // 检查响应内容是否包含 "healthy"
                const isHealthy = healthData.toLowerCase().includes('healthy');

                return NextResponse.json({
                    success: true,
                    status: isHealthy ? 'healthy' : 'unhealthy',
                    message: healthData,
                    timestamp: new Date().toISOString()
                });
            } else {
                console.log(`容器健康检查失败: HTTP ${response.status}`);
                return NextResponse.json({
                    success: false,
                    status: 'unhealthy',
                    error: `HTTP ${response.status}`,
                    timestamp: new Date().toISOString()
                });
            }
        } catch (fetchError) {
            const errorMessage = fetchError instanceof Error ? fetchError.message : String(fetchError);
            console.log(`容器健康检查连接失败: ${errorMessage}`);

            // 检查是否是超时错误
            if (errorMessage.includes('aborted')) {
                return NextResponse.json({
                    success: false,
                    status: 'timeout',
                    error: '健康检查超时',
                    timestamp: new Date().toISOString()
                });
            }

            return NextResponse.json({
                success: false,
                status: 'unreachable',
                error: errorMessage,
                timestamp: new Date().toISOString()
            });
        }
    } catch (error: unknown) {
        console.error('健康检查失败:', error);
        const errorMessage = error instanceof Error ? error.message : '健康检查失败';

        return NextResponse.json(
            {
                success: false,
                status: 'error',
                error: errorMessage,
                timestamp: new Date().toISOString()
            },
            { status: 500 }
        );
    }
}
