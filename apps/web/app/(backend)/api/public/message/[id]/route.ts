import { NextRequest, NextResponse } from 'next/server';
import { getPublicMessageById } from '@/lib/db/queries';
import { ChatSDKError } from '@/lib/errors';

/**
 * 获取公开消息详情
 * 任何人都可以通过有效的UUID访问消息
 * GET /api/public/message/[id]
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // 验证UUID格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return NextResponse.json({ error: '无效的消息ID格式' }, { status: 400 });
    }

    // 获取消息记录
    const message = await getPublicMessageById({ id });
    if (!message) {
      return NextResponse.json({ error: '消息不存在' }, { status: 404 });
    }

    return NextResponse.json({
      message,
      meta: {
        accessedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Public message API error:', error);

    if (error instanceof ChatSDKError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
