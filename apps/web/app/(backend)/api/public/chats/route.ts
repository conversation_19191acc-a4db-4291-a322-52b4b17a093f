import { NextRequest, NextResponse } from 'next/server';
import { getRecentPublicChats } from '@/lib/db/queries';
import { ChatSDKError } from '@/lib/errors';

/**
 * 获取最近的公开聊天记录列表
 * GET /api/public/chats?limit=20
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');
    
    // 解析limit参数，默认20，最大100
    let limit = 20;
    if (limitParam) {
      const parsedLimit = parseInt(limitParam, 10);
      if (!isNaN(parsedLimit) && parsedLimit > 0 && parsedLimit <= 100) {
        limit = parsedLimit;
      }
    }

    // 获取最近的公开聊天记录
    const chats = await getRecentPublicChats({ limit });

    return NextResponse.json({
      chats,
      meta: {
        count: chats.length,
        limit,
        accessedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Public chats API error:', error);
    
    if (error instanceof ChatSDKError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}