import { NextRequest, NextResponse } from 'next/server';
import { createAgentSession, getAgentSessionById } from '@/lib/db/queries';
import { generateUUID } from '@/lib/utils';

/**
 * POST /api/agent-session
 * 创建新的Agent会话
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { agentId, agentName, title, username } = body;

    // 验证必填字段
    if (!agentId || !agentName || !title) {
      return NextResponse.json(
        { error: 'Missing required fields: agentId, agentName, title' },
        { status: 400 }
      );
    }

    // 生成会话ID
    const sessionId = generateUUID();

    // 创建会话
    const session = await createAgentSession({
      id: sessionId,
      agentId,
      agentName,
      title,
      username
    });

    return NextResponse.json({ 
      success: true, 
      sessionId,
      session 
    });

  } catch (error) {
    console.error('Create agent session error:', error);
    return NextResponse.json(
      { error: 'Failed to create agent session' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/agent-session?sessionId=xxx
 * 获取Agent会话详情
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }

    const session = await getAgentSessionById({ id: sessionId });

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      session 
    });

  } catch (error) {
    console.error('Get agent session error:', error);
    return NextResponse.json(
      { error: 'Failed to get agent session' },
      { status: 500 }
    );
  }
} 