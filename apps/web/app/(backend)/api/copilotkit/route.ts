import {
    CopilotRuntime,
    OpenAIAdapter,
    copilotRuntimeNextJSAppRouterEndpoint,
} from "@copilotkit/runtime";
import { NextRequest } from "next/server";

const serviceAdapter = new OpenAIAdapter();

// 创建基础运行时配置
const createRuntime = () => {
    return new CopilotRuntime({
        remoteEndpoints: [
            {
                url: process.env.REMOTE_ACTION_URL || "http://0.0.0.0:8000/copilotkit",
            }
        ]
    });
};

export const POST = async (req: NextRequest) => {
    try {
        // 检查是否是 Mastra 请求
        const isMastraRequest = req.nextUrl.searchParams.get("isMastra");

        if (isMastraRequest) {
            // 暂时禁用 Mastra 集成，直到修复 agent 配置问题
            console.warn("Mastra integration is temporarily disabled due to agent configuration issues");

            // 使用标准运行时配置
            const runtime = createRuntime();
            const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
                runtime,
                serviceAdapter,
                endpoint: "/api/copilotkit",
            });

            return handleRequest(req);
        } else {
            // 标准 CopilotKit 配置
            const runtime = createRuntime();
            const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
                runtime,
                serviceAdapter,
                endpoint: "/api/copilotkit",
            });

            return handleRequest(req);
        }
    } catch (error) {
        console.error("CopilotKit API error:", error);

        // 返回错误响应
        return new Response(
            JSON.stringify({
                error: "Internal server error",
                message: error instanceof Error ? error.message : "Unknown error"
            }),
            {
                status: 500,
                headers: { "Content-Type": "application/json" }
            }
        );
    }
};
