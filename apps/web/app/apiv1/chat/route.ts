import { streamText } from "ai";
import { getAIModel } from "@/lib/ai-config";

/**
 * POST 处理函数 - 处理聊天请求
 * @param req - 请求对象
 * @returns 流式响应
 */
export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // 使用配置的 AI 模型进行流式文本生成
    const result = await streamText({
      model: getAIModel(),
      messages,
      maxTokens: 1000,
      temperature: 0.7,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error("AI API 错误:", error);
    return new Response(JSON.stringify({ error: "处理请求时发生错误" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
