'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { AppLayout } from '@/components/layout/app-layout';
import { AgentProvider } from '@/components/providers/agent-provider';
import { CopilotKit, useCopilotMessagesContext } from '@copilotkit/react-core';
import '@copilotkit/react-ui/styles.css';
import type { AgentSession, AgentMessage } from '@/lib/db/schema';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import { useMarioHttp } from '@/hooks/use-mario-http';


interface AgentSessionClientPageProps {
  agentId: string;
  agentName: string;
  sessionId: string;
  session: AgentSession;
}

/**
 * 带会话ID的客户端 Agent 页面组件
 * 处理特定会话的CopilotKit逻辑并加载历史消息
 * 支持 Mario 模式和常规 CopilotKit 模式
 */
export default function AgentSessionClientPage({
  agentId,
  agentName,
  sessionId,
  session
}: AgentSessionClientPageProps) {
  const pathname = usePathname();
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [loading, setLoading] = useState(true);
  
  // 检测是否为 Mario 模式
  const isMarioMode = pathname.includes('/mario') || agentId === 'mario';

  useEffect(() => {
    const loadMessages = async () => {
      try {
        const response = await fetch(`/api/agent-message?sessionId=${sessionId}`);
        if (response.ok) {
          const data = await response.json();
          setMessages(data.messages || []);
        } else {
          console.error('Failed to load messages');
        }
      } catch (error) {
        console.error('Error loading messages:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [sessionId]);

  if (loading) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <div className='text-center'>
          <div className='mx-auto mb-4 h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900'></div>
          <p className='text-lg text-gray-600'>正在加载 {session.title}...</p>
        </div>
      </div>
    );
  }

  return (
    <AgentProvider>
      {isMarioMode ? (
        <MarioKitWrapper
          agentId={agentId}
          agentName={agentName}
          sessionId={sessionId}
          session={session}
          messages={messages}
        />
      ) : (
        <CopilotKitWrapper
          agentId={agentId}
          agentName={agentName}
          sessionId={sessionId}
          session={session}
          messages={messages}
        />
      )}
    </AgentProvider>
  );
}

/**
 * CopilotKit 包装器组件
 * 根据 agentId 和 sessionId 配置运行时 URL 和 agent
 */
function CopilotKitWrapper({
  agentId,
  agentName,
  sessionId,
  session,
  messages
}: {
  agentId: string;
  agentName: string;
  sessionId: string;
  session: AgentSession;
  messages: AgentMessage[];
}) {
  return (
    <>
      <CopilotKit runtimeUrl={`/api/integration/copilotkit/${agentId}?sessionId=${sessionId}`} agent={agentId}>
        <MessageLoader messages={messages} />
        <AppLayout sessionId={sessionId} session={session} />
      </CopilotKit>
    </>
  );
}

/**
 * 消息加载器组件
 * 使用 useCopilotMessagesContext 来设置历史消息
 */
function MessageLoader({ messages }: { messages: AgentMessage[] }) {
  const { setMessages } = useCopilotMessagesContext();

  useEffect(() => {
    if (messages.length > 0) {
      // 将数据库消息转换为 CopilotKit 消息格式
      const copilotMessages = messages.map(
        (msg) =>
          new TextMessage({
            id: msg.id,
            role: msg.role === 'user' ? Role.User : Role.Assistant,
            content: msg.content,
            createdAt: msg.createdAt
          })
      );
      setMessages(copilotMessages);
    }
  }, [messages, setMessages]);

  return null; // 这个组件不渲染任何内容
}

/**
 * Mario Kit 包装器组件
 * 复用现有的 AppLayout 布局，而不是创建新的布局
 */
function MarioKitWrapper({
  agentId,
  agentName,
  sessionId,
  session,
  messages
}: {
  agentId: string;
  agentName: string;
  sessionId: string;
  session: AgentSession;
  messages: AgentMessage[];
}) {
  return (
    <>
      <CopilotKit runtimeUrl={`/api/integration/mario/${agentId}?sessionId=${sessionId}`} agent={agentId}>
        <MessageLoader messages={messages} />
        <AppLayout sessionId={sessionId} session={session} />
      </CopilotKit>
    </>
  );
}
