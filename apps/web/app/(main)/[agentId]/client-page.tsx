'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUserAuth } from '@/hooks/use-user-auth';
import { AuthLoadingTerminal } from '@/components/loading/loading-terminal';

interface AgentClientPageProps {
  agentId: string;
  agentName: string;
}

/**
 * 客户端 Agent 页面组件
 * 自动创建新会话并重定向到带UUID的URL
 */
export default function AgentClientPage({ agentId, agentName }: AgentClientPageProps) {
  const router = useRouter();
  const { isLoggedIn, username, isLoading } = useUserAuth();

  useEffect(() => {
    const createNewSession = async () => {
      // 等待认证状态确定
      if (isLoading) {
        return;
      }

      // 检查用户是否已登录
      if (!isLoggedIn) {
        console.error('用户未登录，无法创建会话');
        // 重定向到首页，用户需要先登录
        router.replace('/');
        return;
      }

      try {
        // 创建新会话
        const response = await fetch('/api/agent-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            agentId,
            agentName,
            title: `${agentName}对话 - ${new Date().toLocaleString('zh-CN')}`,
            username: username || ''
          })
        });

        if (response.ok) {
          const data = await response.json();
          // 重定向到带UUID的URL
          router.replace(`/${agentId}/${data.sessionId}`);
        } else {
          console.error('Failed to create session');
        }
      } catch (error) {
        console.error('Error creating session:', error);
      }
    };

    createNewSession();
  }, [agentId, agentName, router, isLoggedIn, username, isLoading]);

  // 显示加载状态
  return (
    <div className='flex h-screen items-center justify-center'>
      <div className='text-center'>
        <div className='mx-auto mb-4 h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900'></div>
        <p className='text-lg text-gray-600'>{isLoading ? '正在验证用户身份...' : `正在创建 ${agentName} 新会话...`}</p>
      </div>
    </div>
  );
}
