'use client';

import React from 'react';
import { AppLayout } from '@/components/app-layout';
import { AgentProvider } from '@/components/providers/agent-provider';
import { CopilotKit } from '@copilotkit/react-core';
import '@copilotkit/react-ui/styles.css';
import { getAgent } from '@/agents/registry';
import { notFound } from 'next/navigation';

interface AgentPageProps {
  params: Promise<{
    agentId: string;
  }>;
}

/**
 * 参数化的 Agent 页面组件
 * 根据 agentId 动态加载对应的 agent
 */
export default function AgentPage({ params }: AgentPageProps) {
  const { agentId } = React.use(params);

  // 从统一的注册中心查找对应的 agent 配置
  const agentInfo = getAgent(agentId);

  // 如果 agent 不存在，显示 404
  if (!agentInfo) {
    notFound();
  }

  return (
    <AgentProvider>
      <CopilotKitWrapper agentId={agentId} agentName={agentInfo.name} />
    </AgentProvider>
  );
}

/**
 * CopilotKit 包装器组件
 * 根据 agentId 配置运行时 URL 和 agent
 */
function CopilotKitWrapper({ agentId, agentName }: { agentId: string; agentName: string }) {
  return (
    <>
      <CopilotKit 
        runtimeUrl={`/api/integration/copilotkit/${agentId}`} 
        agent={agentId}
      >
        <AppLayout />
      </CopilotKit>
    </>
  );
} 