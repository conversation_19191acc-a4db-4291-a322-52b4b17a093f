'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import {
  Bot,
  Palette,
  Lightbulb,
  PanelRightClose,
  PanelRightOpen,
  Expand,
  Shrink,
  PanelLeftClose,
  PanelLeftOpen,
  Eye,
  X
} from 'lucide-react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@workspace/ui/components/resizable';
import { useSidebar } from '@workspace/ui/components/sidebar';
import { CopilotKitWrapper } from '@/components/chat';
import { useAgent } from '@/components/providers/agent-provider';
import { AuroraText } from '@workspace/ui/components/magicui/aurora-text';
import { MessagesPreview } from '@/components/chat/messages-preview';
import { CanvasPreviewProvider } from '@/components/chat/message-components';

/**
 * 自定义侧边栏切换按钮组件
 * 根据侧边栏状态显示不同的图标：展开时显示 PanelLeftClose，折叠时显示 PanelLeftOpen
 */
function CustomSidebarTrigger() {
  const { toggleSidebar, state } = useSidebar();

  return (
    <Button
      variant='ghost'
      size='icon'
      className='size-7'
      onClick={toggleSidebar}
      title={state === 'expanded' ? '折叠侧边栏' : '展开侧边栏'}>
      {state === 'expanded' ? <PanelLeftClose className='h-4 w-4' /> : <PanelLeftOpen className='h-4 w-4' />}
      <span className='sr-only'>Toggle Sidebar</span>
    </Button>
  );
}

/**
 * 聊天页面组件，实现简洁的左中右三栏布局
 * 左侧：保持现有的左导航
 * 中间：CopilotChat 聊天区域，简洁设计
 * 右侧：Canvas 区域，简单线条分隔
 */
export default function Page() {
  const [isAgentActive, setIsAgentActive] = useState(false);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewMessage, setPreviewMessage] = useState<string | null>(null);
  const { activeAgent, isExecuting } = useAgent();

  // 当agent执行状态变化时更新isAgentActive
  useEffect(() => {
    setIsAgentActive(isExecuting);
  }, [isExecuting]);

  /**
   * 在Canvas中显示预览消息
   * @param message - 要预览的消息内容
   */
  const handleShowPreviewInCanvas = (message: string) => {
    setPreviewMessage(message);
    setShowPreview(true);
    // 如果右侧面板被折叠，则展开它
    if (isRightPanelCollapsed) {
      setIsRightPanelCollapsed(false);
    }
  };

  /**
   * 隐藏Canvas中的预览
   */
  const handleHidePreviewInCanvas = () => {
    setShowPreview(false);
    setPreviewMessage(null);
  };

  return (
    <CanvasPreviewProvider onShowPreview={handleShowPreviewInCanvas} onHidePreview={handleHidePreviewInCanvas}>
      <div className={`h-screen w-full bg-white dark:bg-gray-900 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
        <ResizablePanelGroup direction='horizontal' className='h-full'>
          <ResizablePanel
            defaultSize={isFullscreen ? 0 : 25}
            minSize={isFullscreen ? 0 : 20}
            maxSize={isFullscreen ? 0 : 40}
            collapsible={true}>
            <div className='flex h-full w-[calc(100%-8px)] flex-col border-gray-200 dark:border-gray-700'>
              {/* 聊天区域头部 - 固定定位 + 毛玻璃效果 */}
              <div className='sticky top-0 z-10 flex h-12 items-center border-b border-white/20 bg-white/70 px-2 backdrop-blur-lg dark:border-gray-700/50 dark:bg-gray-900/70'>
                <CustomSidebarTrigger />
                <div className='ml-auto flex items-center gap-2'>
                  <Badge variant='default' className='animate-pulse gap-1'>
                    <Lightbulb className='h-3 w-3' />
                    <AuroraText>Agent Contributing</AuroraText>
                  </Badge>
                  {activeAgent && (
                    <Badge variant='outline' className='text-xs'>
                      {activeAgent.metadata.name}
                    </Badge>
                  )}
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => setIsRightPanelCollapsed(!isRightPanelCollapsed)}
                    className='h-8 w-8 p-0'
                    title={isRightPanelCollapsed ? '展开面板' : '折叠面板'}>
                    {isRightPanelCollapsed ? (
                      <PanelRightOpen className='h-4 w-4' />
                    ) : (
                      <PanelRightClose className='h-4 w-4' />
                    )}
                  </Button>
                </div>
              </div>

              {/* CopilotKit聊天区域 - 添加滚动条 */}
              <div className='flex-1 overflow-y-auto'>
                <CopilotKitWrapper />
              </div>
            </div>
          </ResizablePanel>
          {!isFullscreen && !isRightPanelCollapsed && <ResizableHandle withHandle />}
          {!isRightPanelCollapsed && (
            <ResizablePanel
              defaultSize={isFullscreen ? 100 : 75}
              minSize={isFullscreen ? 100 : 60}
              maxSize={isFullscreen ? 100 : 80}
              collapsible={true}>
              <div className='flex h-full flex-col'>
                {/* Canvas Header */}
                <div className='flex h-12 w-full items-center border-b border-gray-200 px-2 dark:border-gray-700'>
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-2'>
                      {/* 全屏按钮 */}
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => setIsFullscreen(!isFullscreen)}
                        className='h-8 w-8 p-0'
                        title={isFullscreen ? '退出全屏' : '全屏显示'}>
                        {isFullscreen ? <Shrink className='h-4 w-4' /> : <Expand className='h-4 w-4' />}
                      </Button>

                      {isAgentActive && (
                        <Badge variant='outline' className='text-xs'>
                          Agent 正在执行
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Canvas Content */}
                <div className='flex-1 p-4'>
                  <div className='flex h-full items-center justify-center rounded border border-gray-200 dark:border-gray-700'>
                    {showPreview && previewMessage ? (
                      <div className='flex h-full w-full flex-col'>
                        {/* 预览头部 */}
                        <div className='flex items-center justify-between border-b border-gray-200 p-3 dark:border-gray-700'>
                          <div className='flex items-center gap-2'>
                            <Eye className='h-4 w-4 text-blue-500' />
                            <span className='text-sm font-medium'>Python代码预览</span>
                          </div>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={handleHidePreviewInCanvas}
                            className='h-6 w-6 p-0'
                            title='关闭预览'>
                            <X className='h-3 w-3' />
                          </Button>
                        </div>
                        {/* 预览内容 */}
                        <div className='flex-1 overflow-y-auto'>
                          <MessagesPreview
                            onClose={handleHidePreviewInCanvas}
                            previewMessage={previewMessage || undefined}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className='p-4 text-center'>
                        <p className='text-sm text-gray-500 dark:text-gray-400'>Canvas 内容区域</p>
                        <p className='mt-1 text-xs text-gray-400 dark:text-gray-500'>
                          点击Python代码的预览按钮，内容将在这里显示
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </ResizablePanel>
          )}
        </ResizablePanelGroup>
      </div>
    </CanvasPreviewProvider>
  );
}
