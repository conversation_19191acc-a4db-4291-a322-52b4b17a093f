import { cookies } from 'next/headers';
import { SparklesText } from "@workspace/ui/components/magicui/sparkles-text";

import { Chat } from '@/components/chat';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { generateUUID } from '@/lib/utils';
import { DataStreamHandler } from '@/components/data-stream-handler';

/**
 * 聊天页面组件， 根据 cookie 中的模型设置或使用默认模型初始化聊天界面
 */
export default async function Page() {
    const id = generateUUID();

    const cookieStore = await cookies();
    const modelIdFromCookie = cookieStore.get('chat-model');

    // 确定要使用的聊天模型：优先使用 cookie 中的设置，否则使用默认模型
    const initialChatModel = modelIdFromCookie?.value || DEFAULT_CHAT_MODEL;

    return (
        <>
            <Chat
                key={id}
                id={id}
                initialMessages={[]}
                initialChatModel={initialChatModel}
                initialVisibilityType='public'
                isReadonly={false}
                autoResume={false}
            />
            <DataStreamHandler id={id} />
        </>
    );
}
