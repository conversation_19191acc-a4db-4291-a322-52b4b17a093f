import { cookies, headers } from 'next/headers';
import { notFound } from 'next/navigation';

import { getChatById, getMessagesByChatId, saveChat } from '@/lib/db/queries';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { getUserFromRequest } from '@/lib/sso';
import type { DBMessage } from '@/lib/db/schema';
import type { UIMessage } from 'ai';
import { AnimatedSpan, Terminal, TypingAnimation } from '@workspace/ui/components/magicui/terminal';

export default async function Page(props: { params: Promise<{ id: string }> }) {
    const params = await props.params;
    const { id } = params;
    let chat = await getChatById({ id });

    // 如果找不到chat记录，尝试创建一个默认记录（用于直接访问chat URL的情况）
    if (!chat) {
        try {
            // 获取当前登录用户信息
            const headersList = await headers();

            // 构造一个模拟的Request对象来获取用户信息
            const mockRequest = new Request('http://localhost', {
                headers: headersList
            });

            const userInfo = await getUserFromRequest(mockRequest);
            const username = userInfo?.username || userInfo?.loginName || 'anonymous';

            console.log('🔧 [DEBUG] Chat记录不存在，创建默认记录:', { id, username });

            // 创建默认的chat记录
            await saveChat({
                id,
                title: 'Chat Session',
                visibility: 'public',
                username
            });

            // 重新获取创建的记录
            chat = await getChatById({ id });

            if (!chat) {
                console.error('❌ [ERROR] 无法创建默认chat记录');
                notFound();
            }

            console.log('✅ [DEBUG] 默认chat记录创建成功:', chat.id);
        } catch (error) {
            console.error('❌ [ERROR] 创建默认chat记录失败:', error);
            notFound();
        }
    }

    const messagesFromDb = await getMessagesByChatId({
        id
    });

    function convertToUIMessages(messages: Array<DBMessage>): Array<UIMessage> {
        return messages.map((message) => ({
            id: message.id,
            parts: message.parts as UIMessage['parts'],
            role: message.role as UIMessage['role'],
            // Note: content will soon be deprecated in @ai-sdk/react
            content: '',
            createdAt: message.createdAt
        }));
    }

    const cookieStore = await cookies();
    const chatModelFromCookie = cookieStore.get('chat-model');

    if (!chatModelFromCookie) {
        return (
            <>
                <Chat
                    id={chat.id}
                    initialMessages={convertToUIMessages(messagesFromDb)}
                    initialChatModel={DEFAULT_CHAT_MODEL}
                    initialVisibilityType={chat.visibility}
                    isReadonly={false}
                    autoResume={true}
                />
                <DataStreamHandler id={id} />
            </>
        );
    }

    return (
        <>
            <Terminal>
                <TypingAnimation>&gt; pnpm dlx shadcn@latest init</TypingAnimation>

                <AnimatedSpan delay={1500} className='text-green-500'>
                    <span>✔ Preflight checks.</span>
                </AnimatedSpan>

                <AnimatedSpan delay={2000} className='text-green-500'>
                    <span>✔ Verifying framework. Found Next.js.</span>
                </AnimatedSpan>

                <AnimatedSpan delay={2500} className='text-green-500'>
                    <span>✔ Validating Tailwind CSS.</span>
                </AnimatedSpan>

                <AnimatedSpan delay={3000} className='text-green-500'>
                    <span>✔ Validating import alias.</span>
                </AnimatedSpan>

                <AnimatedSpan delay={3500} className='text-green-500'>
                    <span>✔ Writing components.json.</span>
                </AnimatedSpan>

                <AnimatedSpan delay={4000} className='text-green-500'>
                    <span>✔ Checking registry.</span>
                </AnimatedSpan>

                <AnimatedSpan delay={4500} className='text-green-500'>
                    <span>✔ Updating tailwind.config.ts</span>
                </AnimatedSpan>

                <AnimatedSpan delay={5000} className='text-green-500'>
                    <span>✔ Updating app/globals.css</span>
                </AnimatedSpan>

                <AnimatedSpan delay={5500} className='text-green-500'>
                    <span>✔ Installing dependencies.</span>
                </AnimatedSpan>

                <AnimatedSpan delay={6000} className='text-blue-500'>
                    <span>ℹ Updated 1 file:</span>
                    <span className='pl-2'>- lib/utils.ts</span>
                </AnimatedSpan>

                <TypingAnimation delay={6500} className='text-muted-foreground'>
                    Success! Project initialization completed.
                </TypingAnimation>

                <TypingAnimation delay={7000} className='text-muted-foreground'>
                    You may now add components.
                </TypingAnimation>
            </Terminal>
        </>
    );
}
