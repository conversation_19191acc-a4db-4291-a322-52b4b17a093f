import { cookies } from 'next/headers';
import { AppSidebar } from '@/components/layout/app-sidebar';
import { SidebarInset, SidebarProvider } from '@workspace/ui/components/sidebar';
import Script from 'next/script';

export default async function MainLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const cookieStore = await cookies();
  const isCollapsed = cookieStore.get('sidebar:state')?.value !== 'true';

  return (
    <>
      <Script src='/js/pyodide-v0.27.6.js' strategy='beforeInteractive' />
      <SidebarProvider defaultOpen={!isCollapsed}>
        <AppSidebar collapsible='icon' />
        <SidebarInset>
          <main className='flex flex-1 flex-col'>{children}</main>
        </SidebarInset>
      </SidebarProvider>
    </>
  );
}
