/**
 * Haiku Agent 配置文件
 * 定义默认配置和环境特定配置
 */

import { AgentConfig, AgentState } from './types';

// 默认配置
export const DEFAULT_CONFIG: AgentConfig = {
  model: 'gpt-4',
  systemPrompt: `你是一个专业的俳句创作助手。请根据用户提供的主题，创作优美的俳句。

要求：
1. 严格遵循俳句的5-7-5音节结构
2. 体现季节感和自然意象
3. 表达深刻的情感或哲理
4. 语言简洁优美
5. 同时提供中文和英文版本

请以JSON格式返回，包含chinese和english字段。`,
  maxRetries: 3,
  temperature: 0.7,
  generation: {
    language: 'zh-CN',
    enableFallback: true,
    fallbackContent: {
      chinese: '春风轻拂面\n樱花片片飞舞\n心中有诗意',
      english: 'Spring breeze on my face\nCherry blossoms dance and fall\nPoetry in heart'
    }
  },
  retry: {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxBackoffMs: 10000
  },
  initialState: {
    items: [],
    isGenerating: false,
    history: [],
    preferences: {
      style: 'traditional',
      outputFormat: 'both',
      includeExtras: true
    }
  }
};

/**
 * 配置验证函数
 * 确保配置的有效性和完整性
 */
export function validateConfig(config: Partial<AgentConfig>): string[] {
  const errors: string[] = [];
  
  // 验证基础配置
  if (config.temperature !== undefined) {
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('Temperature must be between 0 and 2');
    }
  }
  
  if (config.maxTokens !== undefined) {
    if (config.maxTokens < 1 || config.maxTokens > 4000) {
      errors.push('MaxTokens must be between 1 and 4000');
    }
  }
  
  // 验证生成配置
  if (config.generation) {
    if (config.generation.retryAttempts !== undefined) {
      if (config.generation.retryAttempts < 0 || config.generation.retryAttempts > 10) {
        errors.push('Generation retryAttempts must be between 0 and 10');
      }
    }
    
    if (config.generation.timeout !== undefined) {
      if (config.generation.timeout < 1000 || config.generation.timeout > 300000) {
        errors.push('Generation timeout must be between 1000ms and 300000ms');
      }
    }
  }
  
  // 验证重试配置
  if (config.retry) {
    if (config.retry.maxAttempts !== undefined) {
      if (config.retry.maxAttempts < 1 || config.retry.maxAttempts > 10) {
        errors.push('Retry maxAttempts must be between 1 and 10');
      }
    }
    
    if (config.retry.backoffMs !== undefined) {
      if (config.retry.backoffMs < 100 || config.retry.backoffMs > 10000) {
        errors.push('Retry backoffMs must be between 100ms and 10000ms');
      }
    }
  }
  
  return errors;
}

/**
 * 安全的配置创建函数
 * 包含验证和错误处理
 */
export function createSafeConfig(
  environment: 'development' | 'production' | 'test' = 'development',
  overrides?: Partial<AgentConfig>
): { config: AgentConfig; errors: string[] } {
  const errors = overrides ? validateConfig(overrides) : [];
  const config = createConfig(environment, overrides);
  
  return { config, errors };
}

/**
 * 获取环境变量配置
 * 从环境变量中读取配置覆盖
 */
export function getEnvConfig(): Partial<AgentConfig> {
  const envConfig: Partial<AgentConfig> = {};
  
  // 从环境变量读取配置
  if (process.env.HAIKU_MODEL) {
    envConfig.model = process.env.HAIKU_MODEL;
  }
  
  if (process.env.HAIKU_TEMPERATURE) {
    const temp = parseFloat(process.env.HAIKU_TEMPERATURE);
    if (!isNaN(temp)) {
      envConfig.temperature = temp;
    }
  }
  
  if (process.env.HAIKU_MAX_TOKENS) {
    const tokens = parseInt(process.env.HAIKU_MAX_TOKENS, 10);
    if (!isNaN(tokens)) {
      envConfig.maxTokens = tokens;
    }
  }
  
  if (process.env.HAIKU_SYSTEM_PROMPT) {
    envConfig.systemPrompt = process.env.HAIKU_SYSTEM_PROMPT;
  }
  
  return envConfig;
}

/**
 * 环境特定配置工厂函数
 * 根据不同环境返回优化的配置
 */
export function createConfig(
  environment: 'development' | 'production' | 'test' = 'development',
  overrides?: Partial<AgentConfig>
): AgentConfig {
  const baseConfig = { ...DEFAULT_CONFIG };
  
  let envConfig: Partial<AgentConfig>;
  
  switch (environment) {
    case 'development':
      envConfig = {
        temperature: 0.8, // 更高的创造性
        generation: {
          ...baseConfig.generation,
          timeout: 60000, // 更长的超时时间
          retryAttempts: 5 // 更多重试次数
        },
        retry: {
          ...baseConfig.retry,
          maxAttempts: 5
        }
      };
      break;
    
    case 'production':
      envConfig = {
        temperature: 0.7, // 平衡的创造性
        generation: {
          ...baseConfig.generation,
          timeout: 30000, // 标准超时时间
          retryAttempts: 3
        },
        retry: {
          ...baseConfig.retry,
          maxAttempts: 3
        }
      };
      break;
    
    case 'test':
      envConfig = {
        temperature: 0.1, // 更确定性的输出
        maxTokens: 500, // 减少 token 使用
        generation: {
          ...baseConfig.generation,
          timeout: 10000, // 更短的超时时间
          retryAttempts: 1, // 减少重试次数
          streaming: false // 禁用流式输出
        },
        retry: {
          ...baseConfig.retry,
          maxAttempts: 1
        }
      };
      break;
    
    default:
      envConfig = {};
  }
  
  // 合并配置：基础配置 + 环境配置 + 用户覆盖
  return {
    ...baseConfig,
    ...envConfig,
    ...overrides,
    // 深度合并嵌套对象
    generation: {
      ...baseConfig.generation,
      ...envConfig.generation,
      ...overrides?.generation
    },
    retry: {
      ...baseConfig.retry,
      ...envConfig.retry,
      ...overrides?.retry
    },
    initialState: {
      ...baseConfig.initialState,
      ...envConfig.initialState,
      ...overrides?.initialState
    }
  };
}

// 默认导出
export default DEFAULT_CONFIG;
