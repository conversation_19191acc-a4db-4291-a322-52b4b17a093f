/**
 * Haiku Agent 服务端集成
 * 专为服务端环境设计，不包含任何客户端组件
 * 提供完整的 Agent 功能和 API 接口
 */

import { createAgent } from './agent.js';
import { DEFAULT_CONFIG, createConfig, validateConfig, getEnvConfig } from './config.js';
import { topicExtractorTool } from './tools/topic-tool.js';
import { contentGeneratorTool } from './tools/generate-tool.js';
import { mastraAgent } from './mastra-agent.js';
import type { 
  AgentConfig, 
  AgentState, 
  AgentTool, 
  IntegrationPackage,
  Item 
} from './types.js';

/**
 * 服务端 Haiku Integration 对象
 * 提供完整的服务端功能集
 */
export const HaikuServerIntegration = {
  // 基础信息
  id: 'haiku-agent',
  name: 'Haiku Generator',
  description: 'AI-powered haiku poetry generator with bilingual support and advanced customization',
  version: '1.0.0',
  author: 'AG-UI Team',
  license: 'MIT',
  
  // 核心功能
  createAgent: (config?: Partial<AgentConfig>) => {
    // 合并环境配置
    const envConfig = getEnvConfig();
    const finalConfig = {
      ...config,
      ...envConfig
    };
    
    // 验证配置
    const errors = validateConfig(finalConfig);
    if (errors.length > 0) {
      console.warn('Configuration validation warnings:', errors);
    }
    
    return createAgent(finalConfig);
  },
  
  // Mastra 集成
  createMastraAgent: () => mastraAgent,
  
  // 配置管理
  defaultConfig: DEFAULT_CONFIG,
  createConfig,
  validateConfig,
  getEnvConfig,
  
  // 工具集
  tools: {
    topicExtractorTool,
    contentGeneratorTool
  },
  
  // 实用函数
  utils: {
    /**
     * 创建安全的配置
     */
    createSafeConfig: (env: 'development' | 'production' | 'test', overrides?: Partial<AgentConfig>) => {
      const envConfig = getEnvConfig();
      const config = createConfig(env, { ...overrides, ...envConfig });
      const errors = validateConfig(config);
      return { config, errors };
    },
    
    /**
     * 验证 Agent 实例
     */
    validateAgent: (agent: any): boolean => {
      return agent && 
             typeof agent.run === 'function' &&
             typeof agent.getAgentState === 'function';
    },
    
    /**
     * 格式化俳句输出
     */
    formatHaikuForAPI: (item: Item) => {
      return {
        id: item.id,
        topic: item.topic,
        content: item.content,
        timestamp: item.timestamp,
        metadata: item.metadata
      };
    }
  },
  
  // 服务端标识
  serverOnly: true,
  environment: 'server'
} as const;

/**
 * 服务端集成包
 * 符合 IntegrationPackage 接口
 */
export const haikuServerIntegrationPackage: IntegrationPackage = {
  integration: HaikuServerIntegration
  // workspace 在服务端不需要，因此省略
};

/**
 * 快速创建 Agent 的便捷函数
 */
export function createHaikuAgent(options?: {
  environment?: 'development' | 'production' | 'test';
  config?: Partial<AgentConfig>;
}) {
  const { environment = 'production', config } = options || {};
  const { config: finalConfig, errors } = HaikuServerIntegration.utils.createSafeConfig(environment, config);
  
  if (errors.length > 0) {
    console.warn('Agent creation warnings:', errors);
  }
  
  return createAgent(finalConfig);
}

/**
 * 创建 API 路由处理器
 */
export function createHaikuAPIHandler(agent?: ReturnType<typeof createAgent>) {
  const agentInstance = agent || createHaikuAgent();
  
  return {
    /**
     * 处理俳句生成请求
     */
    async generateHaiku(request: { topic: string; preferences?: any }) {
      try {
        // 这里需要实现具体的生成逻辑
        // 由于 run 方法返回 Observable，需要转换为 Promise
        const result = await new Promise((resolve, reject) => {
          // 实现 Observable 到 Promise 的转换
          // 这里是简化版本，实际实现需要处理事件流
          resolve({
            success: true,
            data: {
              topic: request.topic,
              content: '示例俳句内容',
              timestamp: Date.now()
            }
          });
        });
        
        return result;
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    },
    
    /**
     * 获取 Agent 状态
     */
    getAgentState() {
      return agentInstance.getAgentState();
    },
    
    /**
     * 清除历史记录
     */
    clearHistory() {
      agentInstance.clearHistory();
      return { success: true };
    }
  };
}

// 默认导出服务器端集成
export default HaikuServerIntegration;

// 导出所有服务端所需的内容
export { createAgent, DEFAULT_CONFIG, createConfig, validateConfig, getEnvConfig };
export { topicExtractorTool, contentGeneratorTool };
export { mastraAgent };
export type { AgentConfig, AgentState, AgentTool, Item };
