/**
 * Haiku Agent - 统一入口文件
 * 提供环境感知的导出，确保 Next.js 和 React 兼容性
 */

// 类型导出（始终可用）
export type { 
  AgentConfig, 
  AgentState, 
  AgentTool,
  Item, 
  IntegrationPackage,
  ComponentType,
  BaseAgentConfig
} from './types.js';

// 配置导出（始终可用）
export { DEFAULT_CONFIG } from './config.js';

// 工具导出（始终可用）
export { topicExtractorTool, contentGeneratorTool } from './tools/index.js';

// 环境检测
const isServer = typeof window === 'undefined';
const isClient = typeof window !== 'undefined';

// 条件导出 - 服务端功能
if (isServer) {
  // 服务端专用导出
  export { 
    HaikuServerIntegration,
    haikuServerIntegrationPackage,
    createHaikuAgent,
    createHaikuAPIHandler
  } from './server.js';
  
  export { 
    createAgent,
    createConfig,
    validateConfig,
    getEnvConfig
  } from './server.js';
  
  export { mastraAgent } from './server.js';
}

// 条件导出 - 客户端功能
if (isClient) {
  // 客户端专用导出
  export { 
    Workspace,
    WorkspaceHeader,
    InputSection,
    CurrentHaikuDisplay,
    HistorySection,
    StatusIndicator,
    HaikuClientIntegration,
    haikuClientIntegrationPackage
  } from './client.js';
}

// 通用导出（同构）
export const HaikuAgent = {
  // 环境信息
  isServer,
  isClient,
  
  // 异步加载器
  async loadServerIntegration() {
    if (isServer) {
      const { HaikuServerIntegration } = await import('./server.js');
      return HaikuServerIntegration;
    }
    throw new Error('Server integration can only be loaded on server side');
  },
  
  async loadClientIntegration() {
    if (isClient) {
      const { HaikuClientIntegration } = await import('./client.js');
      return HaikuClientIntegration;
    }
    throw new Error('Client integration can only be loaded on client side');
  },
  
  // 工厂函数
  async createAgent(config?: any) {
    if (isServer) {
      const { createAgent } = await import('./server.js');
      return createAgent(config);
    }
    throw new Error('Agent creation is only available on server side');
  },
  
  async createWorkspace(props?: any) {
    if (isClient) {
      const { Workspace } = await import('./client.js');
      return Workspace;
    }
    throw new Error('Workspace creation is only available on client side');
  }
};

// 默认导出
export default HaikuAgent;
