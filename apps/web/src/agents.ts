import { AgentIntegrationConfig } from "./types/integration";
import { MiddlewareStarterAgent } from "@ag-ui/middleware-starter";
import { MastraClient } from "@mastra/client-js";
import { MastraAgent } from "@ag-ui/mastra";
import { VercelAISDKAgent } from "@ag-ui/vercel-ai-sdk";
import { openai } from "@ai-sdk/openai";
import { BaseAgent } from "@ag-ui/base-agent";

export const agentsIntegrations: AgentIntegrationConfig[] = [
  {
    id: "middleware-starter",
    agents: async () => {
      return {
        agentic_chat: new MiddlewareStarterAgent(),
      };
    },
  },
  {
    id: "mastra",
    agents: async () => {
      const mastraClient = new MastraClient({
        baseUrl: "http://localhost:4111",
      });

      return MastraAgent.getRemoteAgents({
        mastraClient,
      });
    },
  },
  {
    id: "vercel-ai-sdk",
    agents: async () => {
      return {
        agentic_chat: new VercelAISDKAgent({ model: openai("gpt-4o") }),
      };
    },
  },
  {
    id: "base-agent",
    agents: async () => {
      try {
        console.log(`✅ Initializing Base Agent.`);

        return {
          agentic_chat: new BaseAgent({
            openaiConfig: {
              apiKey: process.env.OPENAI_API_KEY || "1914304559263223873",
              baseURL: process.env.OPENAI_BASE_URL || "https://aigc.sankuai.com/v1/openai/native/",
              model: process.env.OPENAI_MODEL || "gpt-4o-2024-11-20",
            },
            config: {
              agentId: "base-agent",
              description: "A comprehensive base agent with artifact support",
              temperature: 0.7,
              maxTokens: 4000,
              enabledFeatures: ["chat", "generative_ui", "tool_calling"],
              ui: {
                theme: "system",
                showHeader: true,
                showFooter: true,
                showToolbar: true,
                layout: "default",
              },
            },
          }),
        };
      } catch (error) {
        console.error(`❌ Error initializing Base Agent:`, error);
        return {};
      }
    },
  },
];
