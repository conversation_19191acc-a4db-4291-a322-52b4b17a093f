"use client";

import React, { useEffect, useState } from "react";
import { Bot, Loader2 } from "lucide-react";

interface AgentStatusIndicatorProps {
  agentName: string;
  isActive: boolean;
}

export function AgentStatusIndicator({ agentName, isActive }: AgentStatusIndicatorProps) {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (isActive) {
      setVisible(true);
    } else {
      // Fade out after a delay
      const timer = setTimeout(() => {
        setVisible(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isActive]);

  if (!visible) {
    return null;
  }

  return (
    <div className="animate-in slide-in-from-right-2 duration-300">
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full shadow-sm border border-white/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          {isActive ? (
            <Loader2 className="w-3 h-3 animate-spin" />
          ) : (
            <Bot className="w-3 h-3" />
          )}
          <span className="font-medium text-xs">{agentName}</span>
        </div>
      </div>
    </div>
  );
}
