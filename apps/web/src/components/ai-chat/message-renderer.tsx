"use client";

import React from "react";
import { cn } from "@workspace/ui/lib/utils";
import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { EnhancedTableRenderer } from "./enhanced-table-renderer";
import {
  FileText,
  BarChart3,
  Code,
  Table as TableIcon,
  FileImage,
  Maximize2,
  Copy
} from "lucide-react";
import { ExtendedMessage, Artifact, ContentType } from "@workspace/core/types";

interface MessageRendererProps {
  message: ExtendedMessage;
  onArtifactExpand?: (artifact: Artifact) => void;
  className?: string;
}

/**
 * 智能消息渲染器 - 根据消息类型进行差异化渲染
 */
export function MessageRenderer({ 
  message, 
  onArtifactExpand, 
  className 
}: MessageRendererProps) {
  const isUser = message.role === "user";
  
  return (
    <div className={cn(
      "flex w-full",
      isUser ? "justify-end" : "justify-start",
      className
    )}>
      <div className={cn(
        "max-w-[85%] rounded-lg p-3",
        isUser 
          ? "bg-primary text-primary-foreground" 
          : "bg-muted"
      )}>
        {/* 基础文本内容 */}
        <div className="whitespace-pre-wrap text-sm mb-2">
          {message.content}
        </div>

        {/* 渲染Artifacts */}
        {message.artifacts && message.artifacts.length > 0 && (
          <div className="space-y-3 mt-3">
            {message.artifacts.map((artifact) => (
              <ArtifactRenderer
                key={artifact.id}
                artifact={artifact}
                onExpand={onArtifactExpand}
                isInMessage={true}
              />
            ))}
          </div>
        )}

        {/* 消息元数据 */}
        {message.metadata?.renderHints && (
          <div className="flex flex-wrap gap-1 mt-2">
            {message.metadata.renderHints.map((hint) => (
              <Badge key={hint} variant="outline" className="text-xs">
                {hint}
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

interface ArtifactRendererProps {
  artifact: Artifact;
  onExpand?: (artifact: Artifact) => void;
  isInMessage?: boolean;
}

/**
 * Artifact渲染器 - 根据类型渲染不同的预览
 */
function ArtifactRenderer({ 
  artifact, 
  onExpand, 
  isInMessage = false 
}: ArtifactRendererProps) {
  const handleExpand = () => {
    if (onExpand) {
      onExpand(artifact);
    }
  };

  const getIcon = (type: ContentType) => {
    switch (type) {
      case "table": return <TableIcon className="w-4 h-4" />;
      case "chart": return <BarChart3 className="w-4 h-4" />;
      case "code": return <Code className="w-4 h-4" />;
      case "document":
      case "report": return <FileText className="w-4 h-4" />;
      case "image": return <FileImage className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const renderPreview = () => {
    switch (artifact.type) {
      case "table":
        return <TablePreview content={artifact.content} />;
      case "chart":
        return <ChartPreview content={artifact.content} />;
      case "code":
        return <CodePreview content={artifact.content} />;
      case "report":
      case "document":
        return <DocumentPreview content={artifact.content} />;
      default:
        return <TextPreview content={artifact.content} />;
    }
  };

  return (
    <Card className={cn(
      "cursor-pointer transition-all hover:shadow-md hover:border-primary/50",
      isInMessage ? "bg-background/50" : "bg-background"
    )}>
      <CardContent className="p-4">
        {/* 头部信息 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="text-muted-foreground">
              {getIcon(artifact.type)}
            </div>
            <div>
              <h4 className="font-medium text-sm">{artifact.title}</h4>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <Badge variant="outline" className="text-xs">
                  {artifact.type}
                </Badge>
                <span>•</span>
                <span>{artifact.status}</span>
              </div>
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                navigator.clipboard.writeText(artifact.content);
              }}
              className="h-8 w-8 p-0"
            >
              <Copy className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleExpand();
              }}
              className="h-8 w-8 p-0"
            >
              <Maximize2 className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* 预览内容 */}
        <div 
          className="cursor-pointer"
          onDoubleClick={handleExpand}
        >
          {renderPreview()}
        </div>

        {/* 双击提示 */}
        {isInMessage && (
          <div className="text-xs text-muted-foreground mt-2 text-center">
            双击放大查看 • 点击按钮展开到Artifact页面
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * 表格预览组件
 */
function TablePreview({ content }: { content: string }) {
  return (
    <EnhancedTableRenderer
      content={content}
      isPreview={true}
    />
  );
}

/**
 * 图表预览组件
 */
function ChartPreview({ content }: { content: string }) {
  return (
    <div className="text-center p-6 bg-muted/50 rounded">
      <BarChart3 className="w-12 h-12 mx-auto mb-2 text-muted-foreground" />
      <p className="text-sm text-muted-foreground mb-1">图表可视化</p>
      <p className="text-xs text-muted-foreground">
        {content.substring(0, 50)}...
      </p>
    </div>
  );
}

/**
 * 代码预览组件
 */
function CodePreview({ content }: { content: string }) {
  const previewContent = content.length > 200 
    ? content.substring(0, 200) + "..." 
    : content;

  return (
    <pre className="text-xs font-mono bg-muted/50 p-3 rounded overflow-hidden">
      <code>{previewContent}</code>
    </pre>
  );
}

/**
 * 文档预览组件
 */
function DocumentPreview({ content }: { content: string }) {
  const previewContent = content.length > 150 
    ? content.substring(0, 150) + "..." 
    : content;

  return (
    <div className="text-sm text-muted-foreground">
      {previewContent.split('\n').slice(0, 4).map((line, index) => (
        <p key={index} className="mb-1">{line}</p>
      ))}
    </div>
  );
}

/**
 * 文本预览组件
 */
function TextPreview({ content }: { content: string }) {
  const previewContent = content.length > 100 
    ? content.substring(0, 100) + "..." 
    : content;

  return (
    <div className="text-sm text-muted-foreground">
      {previewContent}
    </div>
  );
}
