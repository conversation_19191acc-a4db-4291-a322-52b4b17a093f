"use client";

import React, { useState, useEffect } from "react";
import "@copilotkit/react-ui/styles.css";
import "./universal-chat.css";
import { CopilotKit, useCopilotAction, useCopilotChat, useCopilotMessagesContext } from "@copilotkit/react-core";
import { CopilotChat, MessagesProps } from "@copilotkit/react-ui";
import { AgentStatusIndicator } from "./agent-status-indicator";
import { MessageRenderer } from "./message-renderer";
import { analyzeMessageContent } from "./message-content-analyzer";
import { ExtendedMessage, Artifact } from "@workspace/core/types";

interface UniversalChatProps {
  integrationId?: string;
}

// 创建一个Context来共享agent状态
export const AgentContext = React.createContext<{
  currentAgent: string | null;
  isAgentActive: boolean;
  updateAgentStatus: (agentName: string) => void;
  currentAgentId: string;
  updateAgentId: (agentId: string) => void;
}>({
  currentAgent: null,
  isAgentActive: false,
  updateAgentStatus: () => {},
  currentAgentId: "agentic_chat",
  updateAgentId: () => {},
});

export function UniversalChat({ integrationId = "base-agent" }: UniversalChatProps) {
  const { currentAgentId } = React.useContext(AgentContext);
  const [currentArtifact, setCurrentArtifact] = useState<Artifact | null>(null);

  const handleArtifactExpand = (artifact: Artifact) => {
    setCurrentArtifact(artifact);
    // 这里可以触发右侧面板显示Artifact
    window.dispatchEvent(new CustomEvent('showArtifact', { detail: artifact }));
  };

  return (
    <CopilotKit
      runtimeUrl={`/api/copilotkit/${integrationId}`}
      showDevConsole={false}
      agent={currentAgentId}
    >
      <ChatInterface onArtifactExpand={handleArtifactExpand} />
    </CopilotKit>
  );
}

interface ChatInterfaceProps {
  onArtifactExpand?: (artifact: Artifact) => void;
}

function ChatInterface({ onArtifactExpand }: ChatInterfaceProps) {
  const { isLoading } = useCopilotChat();
  const { currentAgent, isAgentActive, updateAgentStatus, updateAgentId } = React.useContext(AgentContext);
  const { messages } = useCopilotMessagesContext();

  // 处理消息，添加内容分析
  const processedMessages = React.useMemo(() => {
    return messages.map(message => {
      if (message.role === "assistant" && message.content) {
        const analysis = analyzeMessageContent(message.content);
        return {
          ...message,
          contentType: analysis.contentType,
          artifacts: analysis.artifacts,
          metadata: analysis.metadata
        } as ExtendedMessage;
      }
      return message as ExtendedMessage;
    });
  }, [messages]);

  // 自定义消息渲染组件
  const CustomMessages = React.useCallback(({
    messages,
    inProgress,
    RenderTextMessage,
    RenderActionExecutionMessage,
    RenderResultMessage,
    RenderAgentStateMessage,
  }: MessagesProps) => {
    return (
      <div className="flex flex-col gap-4 p-4 h-full overflow-y-auto">
        {processedMessages.map((message, index) => (
          <MessageRenderer
            key={message.id}
            message={message}
            onArtifactExpand={onArtifactExpand}
          />
        ))}
      </div>
    );
  }, [processedMessages, onArtifactExpand]);

  // Intent detection function with improved accuracy
  const detectIntent = React.useCallback((message: string) => {
    const lowerMessage = message.toLowerCase();
    console.log("🔍 意图识别 - 输入消息:", message);

    // 所有请求都由base_agent处理，只更新显示状态
    console.log("✅ 使用Base Agent处理请求");

    // 根据用户输入更新显示状态，但都由base_agent处理
    if (lowerMessage.includes("天气") || lowerMessage.includes("weather") ||
        lowerMessage.includes("温度") || lowerMessage.includes("气温")) {
      console.log("✅ 识别为天气查询");
      updateAgentStatus("天气查询助手");
    } else if (lowerMessage.includes("报告") || lowerMessage.includes("文档") ||
               lowerMessage.includes("写") || lowerMessage.includes("document") ||
               lowerMessage.includes("文章") || lowerMessage.includes("邮件") ||
               lowerMessage.includes("写作") || lowerMessage.includes("撰写")) {
      console.log("✅ 识别为文档生成");
      updateAgentStatus("文档生成助手");
    } else if (lowerMessage.includes("代码") || lowerMessage.includes("code") ||
               lowerMessage.includes("编程") || lowerMessage.includes("函数") ||
               lowerMessage.includes("javascript") || lowerMessage.includes("python") ||
               lowerMessage.includes("程序") || lowerMessage.includes("脚本")) {
      console.log("✅ 识别为代码助手");
      updateAgentStatus("代码助手");
    } else if (lowerMessage.includes("背景") || lowerMessage.includes("颜色") ||
               lowerMessage.includes("background") || lowerMessage.includes("样式") ||
               lowerMessage.includes("界面") || lowerMessage.includes("主题")) {
      console.log("✅ 识别为界面定制");
      updateAgentStatus("界面定制助手");
    } else {
      console.log("✅ 识别为通用助手");
      updateAgentStatus("Base Agent");
    }
  }, [updateAgentStatus, updateAgentId]);

  // Monitor loading state to detect agent activity
  useEffect(() => {
    if (isLoading) {
      // Only set default agent name if no specific agent is already set
      if (!currentAgent || currentAgent === "AI助手") {
        updateAgentStatus("AI助手");
      }
    }
  }, [isLoading, currentAgent, updateAgentStatus]);

  // Action to detect user intent and route to appropriate agent
  useCopilotAction({
    name: "route_to_agent",
    description: "Analyze user intent and route to the most appropriate specialized agent",
    parameters: [
      {
        name: "intent",
        type: "string",
        description: "The detected user intent (weather, code, document, ui, general)",
      },
      {
        name: "message",
        type: "string",
        description: "The user's original message",
      },
    ],
    handler: ({ message }) => {
      // Handler is safe for state updates
      detectIntent(message || "");
    },
    followUp: false,
  });

  // Action to detect weather queries and show weather agent
  useCopilotAction({
    name: "lookup_weather",
    description: "Lookup the weather for a given city",
    parameters: [
      {
        name: "city",
        type: "string",
        description: "The city to lookup the weather for",
      },
      {
        name: "weather",
        type: "string",
        description: "The weather for the city",
      },
    ],
    handler: ({ city }) => {
      updateAgentStatus("天气查询助手");
    },
    render: ({ status, args }) => {
      return (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 my-2">
          <div className="text-blue-800">
            🌤️ 查询 {args.city} 的天气: {args.weather}
          </div>
          <div className="text-blue-600 text-sm">状态: {status}</div>
        </div>
      );
    },
    followUp: false,
  });

  // Action to detect document/text generation requests
  useCopilotAction({
    name: "generate_document",
    description: "Generate or edit documents, articles, or any text content",
    parameters: [
      {
        name: "content",
        type: "string",
        description: "The document content to generate or edit",
      },
      {
        name: "type",
        type: "string",
        description: "Type of document (article, report, email, etc.)",
      },
    ],
    handler: ({ type }) => {
      updateAgentStatus("文档生成助手");
    },
    render: ({ status, args }) => {
      return (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 my-2">
          <div className="text-green-800">
            📝 正在生成{args.type || "文档"}...
          </div>
          <div className="text-green-600 text-sm">状态: {status}</div>
        </div>
      );
    },
    followUp: false,
  });

  // Action to detect background change requests
  useCopilotAction({
    name: "change_background",
    description: "Change the background color of the chat. Can be anything that the CSS background attribute accepts.",
    parameters: [
      {
        name: "background",
        type: "string",
        description: "The background color or gradient.",
      },
    ],
    handler: ({ background }) => {
      updateAgentStatus("界面定制助手");
      // Apply background change logic here if needed
    },
    render: ({ status, args }) => {
      return (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 my-2">
          <div className="text-purple-800">
            🎨 正在更改背景为: {args.background}
          </div>
          <div className="text-purple-600 text-sm">状态: {status}</div>
        </div>
      );
    },
  });

  // Action to detect code generation requests
  useCopilotAction({
    name: "generate_code",
    description: "Generate, review, or debug code in various programming languages",
    parameters: [
      {
        name: "language",
        type: "string",
        description: "Programming language (JavaScript, Python, etc.)",
      },
      {
        name: "task",
        type: "string",
        description: "What to do with the code (generate, debug, review, etc.)",
      },
    ],
    handler: ({ language, task }) => {
      updateAgentStatus("代码助手");
    },
    render: ({ status, args }) => {
      return (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 my-2">
          <div className="text-gray-800">
            💻 正在处理{args.language || ""}代码: {args.task}
          </div>
          <div className="text-gray-600 text-sm">状态: {status}</div>
        </div>
      );
    },
    followUp: false,
  });



  return (
    <div className="h-full w-full relative bg-white border-r border-gray-200">
      {/* 顶部标题栏 */}
      <div className="bg-gray-50 border-b border-gray-200 px-3 py-2">
        <div className="flex items-center justify-between">
          <h1 className="font-semibold text-gray-800 text-sm">AI Chat</h1>
          <AgentStatusIndicator
            agentName={currentAgent || "AI助手"}
            isActive={isAgentActive}
          />
        </div>
      </div>

      {/* 聊天区域 */}
      <div className="h-[calc(100%-48px)]">
        <CopilotChat
          className="h-full"
          Messages={CustomMessages}
          labels={{
            initial: "你好！我是你的智能Base Agent 🤖\n\n我是一个功能强大的AI助手，可以帮助你完成各种任务：\n\n🌤️ **天气查询** - 告诉我城市名称\n📝 **文档生成** - 帮你写文章、报告、邮件等\n💻 **代码助手** - 编程、调试、代码审查\n🎨 **界面定制** - 更改背景、颜色、样式\n📊 **数据分析** - 图表生成、数据处理\n🛠️ **工具调用** - 各种实用工具和功能\n\n我使用先进的Artifact系统来生成和管理内容，支持：\n- 📄 文档创建和编辑\n- 💻 代码生成和运行\n- 📈 图表和可视化\n- 🎨 UI组件生成\n\n当我处理你的请求时，右侧会显示相应的内容和工具。\n\n现在，请告诉我你需要什么帮助！"
          }}
        />
      </div>
    </div>
  );
}
