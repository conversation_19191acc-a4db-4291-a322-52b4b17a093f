/* Universal Chat Styles */
.copilotKitInput {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
  border: 1px solid var(--copilot-kit-separator-color) !important;
}

.copilotKitChat {
  background-color: #fff !important;
}

/* Agent status indicator animations */
@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slideInFromTop 0.3s ease-out;
}

/* Custom scrollbar for chat */
.copilotKitChat::-webkit-scrollbar {
  width: 6px;
}

.copilotKitChat::-webkit-scrollbar-track {
  background: transparent;
}

.copilotKitChat::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.copilotKitChat::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Message styling */
.copilotKitMessage {
  margin-bottom: 1rem;
}

/* Action result styling */
.action-result {
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-width: 1px;
}

.action-result.weather {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #1e40af;
}

.action-result.document {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.action-result.code {
  background-color: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
}

.action-result.ui {
  background-color: #faf5ff;
  border-color: #d8b4fe;
  color: #7c3aed;
}
