{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --port 8000", "build": "NODE_ENV=test next build", "build:test": "NODE_ENV=test next build", "build:prod": "NODE_ENV=production next build", "start": "NODE_ENV=test next start --port 8000", "start:test": "NODE_ENV=test next start --port 8000", "start:prod": "NODE_ENV=production next start --port 8000", "db:push": "drizzle-kit push --force", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@ag-ui/client": "^0.0.28", "@ag-ui/core": "^0.0.28", "@ag-ui/encoder": "^0.0.28", "@ag-ui/mastra": "^0.0.1", "@ag-ui/proto": "^0.0.28", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/ui-utils": "^1.2.11", "@copilotkit/react-core": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@copilotkit/runtime": "^1.9.1", "@copilotkit/runtime-client-gql": "^1.9.1", "@copilotkit/sdk-js": "^1.9.1", "@copilotkit/shared": "^1.9.1", "@mastra/client-js": "^0.10.6", "@mastra/core": "^0.10.7", "@mastra/openai": "^1.0.1-alpha.48", "@mtfe/basic-auth": "^0.3.2", "@mtfe/sso-web": "^2.6.1", "@workspace/ui": "workspace:*", "@workspace/shared": "workspace:*", "@workspace/agent-registry": "workspace:*", "ai": "^4.3.16", "lucide-react": "^0.523.0", "moment": "^2.30.1", "next": "^15.3.4", "next-themes": "^0.4.6", "openai": "4.104.0", "react": "^19.1.0", "react-dom": "^19.1.0", "rxjs": "7.8.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.8.3"}}