{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@workspace/ui": "workspace:*", "lucide-react": "^0.525.0", "next": "^15.3.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.8.3"}}