{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@types/prismjs": "^1.26.5", "@types/react-syntax-highlighter": "^15.5.13", "@workspace/ui": "workspace:*", "framer-motion": "^12.18.1", "lucide-react": "^0.518.0", "mermaid": "^11.6.0", "monaco-editor": "^0.52.2", "next": "^15.3.4", "next-themes": "^0.4.6", "prismjs": "^1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.8.3"}}