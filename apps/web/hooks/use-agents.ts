import { useState, useEffect, useRef } from 'react';

export interface Agent {
  id: string;
  name: string;
  description: string;
  route: string;
  type?: string;
  originalId?: string;
}

interface UseAgentsReturn {
  agents: Agent[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook 用于获取可用的 agents 列表
 * 从统一的后端 API 获取，使用参数化路由
 */
export function useAgents(): UseAgentsReturn {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const fetchedRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchAgents = async () => {
    // 如果已经有一个请求在进行中，取消它
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/agents', {
        signal: abortController.signal
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch agents: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.agents && Array.isArray(data.agents)) {
        setAgents(data.agents);
        fetchedRef.current = true;
        console.log('Agents fetched successfully:', data.agents.length);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      // 如果请求被取消，不要更新状态
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('Fetch agents request was aborted');
        return;
      }

      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error fetching agents:', err);

      // 只有在第一次失败时才提供默认的 fallback agents
      if (!fetchedRef.current) {
        const fallbackAgents = [
          {
            id: 'haiku',
            name: 'Haiku Agent',
            description: '专门用于创作诗歌的AI助手',
            route: '/haiku',
            originalId: 'haiku'
          },
          {
            id: 'mario',
            name: 'Mario Agent',
            description: 'Mario游戏助手',
            route: '/mario',
            originalId: 'mario'
          }
        ];
        setAgents(fallbackAgents);
        fetchedRef.current = true;
        console.log('Using fallback agents:', fallbackAgents.length);
      }
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  };

  useEffect(() => {
    // 只在组件首次挂载时获取数据
    if (!fetchedRef.current) {
      fetchAgents();
    }

    // 清理函数：取消正在进行的请求
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []); // 空依赖数组，只在挂载时执行一次

  return {
    agents,
    loading,
    error,
    refetch: fetchAgents
  };
}

/**
 * 根据 agent ID 获取对应的路由路径
 * 使用新的参数化路由格式
 */
export function getAgentRoute(agentId: string, agents: Agent[]): string {
  const agent = agents.find((a) => a.id === agentId);
  if (agent?.route) {
    return agent.route;
  }

  // 对于新的参数化路由，直接使用 agentId
  const cleanId = agentId.replace(/Agent$/, '').toLowerCase();
  return `/${cleanId}`;
}
