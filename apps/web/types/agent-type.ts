/**
 * Agent 配置接口
 * 定义所有 agent 的通用配置结构
 */
export interface AgentConfig {
  /** Agent 唯一标识符 */
  id: string;
  /** Agent 显示名称 */
  name: string;
  /** Agent 描述 */
  description: string;
  /** Agent 版本 */
  version: string;
  /** Agent 能力列表 */
  capabilities: string[];
  /** Agent 设置 */
  settings: AgentSettings;
  /** API 端点配置 */
  endpoints: AgentEndpoints;
  /** UI 配置 */
  ui: AgentUIConfig;
}

/**
 * Agent 设置接口
 */
export interface AgentSettings {
  /** 最大 token 数 */
  maxTokens: number;
  /** 温度参数 */
  temperature: number;
  /** 使用的模型 */
  model: string;
  /** 其他自定义设置 */
  [key: string]: any;
}

/**
 * Agent API 端点配置
 */
export interface AgentEndpoints {
  /** Actions API 端点 */
  actions: string;
  /** Chat API 端点 */
  chat: string;
  /** 其他自定义端点 */
  [key: string]: string;
}

/**
 * Agent UI 配置接口
 */
export interface AgentUIConfig {
  /** UI 主题 */
  theme: 'default' | 'dark' | 'light';
  /** 布局类型 */
  layout: 'sidebar' | 'fullscreen' | 'split';
  /** 功能特性配置 */
  features: AgentFeatures;
}

/**
 * Agent 功能特性配置
 */
export interface AgentFeatures {
  /** 是否启用聊天功能 */
  chat: boolean;
  /** 是否启用任务列表 */
  taskList: boolean;
  /** 是否显示状态指示器 */
  statusIndicator: boolean;
  /** 其他自定义功能 */
  [key: string]: boolean;
}

/**
 * Agent 状态枚举
 */
export enum AgentStatus {
  IDLE = 'idle',
  THINKING = 'thinking',
  WORKING = 'working',
  ERROR = 'error'
}

/**
 * 任务接口
 */
export interface Task {
  /** 任务 ID */
  id: string;
  /** 任务标题 */
  title: string;
  /** 任务描述 */
  description?: string;
  /** 任务状态 */
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 任务优先级 */
  priority: 'low' | 'medium' | 'high';
  /** 任务标签 */
  tags?: string[];
}

/**
 * Agent 上下文接口
 */
export interface AgentContext {
  /** 当前 agent 配置 */
  agent: AgentConfig;
  /** Agent 状态 */
  status: AgentStatus;
  /** 任务列表 */
  tasks: Task[];
  /** 更新 agent 状态 */
  updateStatus: (status: AgentStatus) => void;
  /** 添加任务 */
  addTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void;
  /** 更新任务 */
  updateTask: (id: string, updates: Partial<Task>) => void;
  /** 删除任务 */
  removeTask: (id: string) => void;
}