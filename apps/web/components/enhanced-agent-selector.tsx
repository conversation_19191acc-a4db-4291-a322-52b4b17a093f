'use client';

import React, { useState, useCallback } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
  SelectGroup
} from '@workspace/ui/components/select';
import { Badge } from '@workspace/ui/components/badge';
import { <PERSON><PERSON> } from '@workspace/ui/components/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@workspace/ui/components/tooltip';
import { 
  Bot, 
  TestTube, 
  PenTool, 
  Search, 
  Calendar, 
  Code, 
  Wifi, 
  WifiOff,
  Settings,
  Info
} from 'lucide-react';
import { getAllAgentConfigs, getAgentConfig } from '@/lib/agent-config';
import type { AgentConfig } from '@/lib/types/chat';

interface EnhancedAgentSelectorProps {
  selectedAgentId?: string;
  onAgentChange?: (agentId: string) => void;
  showConnectionStatus?: boolean;
  connectionStates?: Record<string, boolean>;
  className?: string;
}

/**
 * 获取 Agent 类型对应的图标
 */
function getAgentIcon(agentType: string) {
  switch (agentType) {
    case 'test-generator':
      return <TestTube className='h-4 w-4' />;
    case 'creative-writer':
      return <PenTool className='h-4 w-4' />;
    case 'researcher':
      return <Search className='h-4 w-4' />;
    case 'planner':
      return <Calendar className='h-4 w-4' />;
    case 'coder':
      return <Code className='h-4 w-4' />;
    default:
      return <Bot className='h-4 w-4' />;
  }
}

/**
 * 获取 Agent 类型的显示名称
 */
function getAgentTypeDisplayName(agentType: string): string {
  switch (agentType) {
    case 'test-generator':
      return '测试生成';
    case 'creative-writer':
      return '创意写作';
    case 'researcher':
      return '研究分析';
    case 'planner':
      return '项目规划';
    case 'coder':
      return '代码开发';
    default:
      return '通用助手';
  }
}

/**
 * 增强的 Agent 选择器组件
 * 支持多种 Agent 类型，显示连接状态和详细信息
 */
export function EnhancedAgentSelector({
  selectedAgentId,
  onAgentChange,
  showConnectionStatus = true,
  connectionStates = {},
  className = ''
}: EnhancedAgentSelectorProps) {
  const [showDetails, setShowDetails] = useState(false);
  const agentConfigs = getAllAgentConfigs();
  const selectedAgent = selectedAgentId ? getAgentConfig(selectedAgentId) : null;

  /**
   * 处理 Agent 选择变化
   */
  const handleAgentChange = useCallback((agentId: string) => {
    onAgentChange?.(agentId);
  }, [onAgentChange]);

  /**
   * 按类型分组 Agent 配置
   */
  const groupedAgents = agentConfigs.reduce((groups, agent) => {
    const typeDisplayName = getAgentTypeDisplayName(agent.type);
    if (!groups[typeDisplayName]) {
      groups[typeDisplayName] = [];
    }
    groups[typeDisplayName].push(agent);
    return groups;
  }, {} as Record<string, AgentConfig[]>);

  /**
   * 渲染 Agent 选项
   */
  const renderAgentOption = (agent: AgentConfig) => {
    const isConnected = connectionStates[agent.id];
    
    return (
      <SelectItem key={agent.id} value={agent.id} className='text-xs'>
        <div className='flex items-center gap-2 w-full'>
          {/* Agent 图标 */}
          <div className='flex-shrink-0'>
            {getAgentIcon(agent.type)}
          </div>
          
          {/* Agent 信息 */}
          <div className='flex-1 min-w-0'>
            <div className='flex items-center gap-2'>
              <span className='font-medium truncate'>{agent.name}</span>
              {showConnectionStatus && (
                <div className='flex-shrink-0'>
                  {isConnected ? (
                    <Wifi className='h-3 w-3 text-green-500' />
                  ) : (
                    <WifiOff className='h-3 w-3 text-gray-400' />
                  )}
                </div>
              )}
            </div>
            {agent.description && (
              <div className='text-xs text-gray-500 truncate mt-0.5'>
                {agent.description}
              </div>
            )}
          </div>
        </div>
      </SelectItem>
    );
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Agent 选择器 */}
      <Select value={selectedAgentId} onValueChange={handleAgentChange}>
        <SelectTrigger className='h-8 w-auto min-w-[120px] rounded-md border-0 bg-gray-100 px-3 text-xs shadow-none focus:ring-0'>
          <div className='flex items-center gap-2'>
            {selectedAgent && getAgentIcon(selectedAgent.type)}
            <SelectValue placeholder='选择 Agent' />
            {showConnectionStatus && selectedAgentId && (
              <div className='ml-1'>
                {connectionStates[selectedAgentId] ? (
                  <div className='h-2 w-2 rounded-full bg-green-500' />
                ) : (
                  <div className='h-2 w-2 rounded-full bg-gray-400' />
                )}
              </div>
            )}
          </div>
        </SelectTrigger>
        
        <SelectContent>
          {Object.entries(groupedAgents).map(([typeName, agents]) => (
            <SelectGroup key={typeName}>
              <SelectLabel>{typeName}</SelectLabel>
              {agents.map(renderAgentOption)}
            </SelectGroup>
          ))}
        </SelectContent>
      </Select>

      {/* Agent 详情按钮 */}
      {selectedAgent && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                className='h-8 w-8 p-0'
                onClick={() => setShowDetails(!showDetails)}
              >
                <Info className='h-4 w-4' />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <div className='max-w-xs space-y-2'>
                <div className='font-medium'>{selectedAgent.name}</div>
                <div className='text-sm text-gray-600'>{selectedAgent.description}</div>
                
                {/* 功能列表 */}
                {selectedAgent.capabilities && selectedAgent.capabilities.length > 0 && (
                  <div>
                    <div className='text-sm font-medium mb-1'>功能:</div>
                    <div className='flex flex-wrap gap-1'>
                      {selectedAgent.capabilities.map((capability) => (
                        <Badge key={capability} variant='secondary' className='text-xs'>
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* 连接状态 */}
                {showConnectionStatus && (
                  <div className='flex items-center gap-2 text-sm'>
                    <span>连接状态:</span>
                    {connectionStates[selectedAgent.id] ? (
                      <Badge variant='default' className='text-xs'>
                        <Wifi className='h-3 w-3 mr-1' />
                        已连接
                      </Badge>
                    ) : (
                      <Badge variant='secondary' className='text-xs'>
                        <WifiOff className='h-3 w-3 mr-1' />
                        未连接
                      </Badge>
                    )}
                  </div>
                )}
                
                {/* 默认模型 */}
                {selectedAgent.defaultModel && (
                  <div className='text-sm'>
                    <span className='font-medium'>默认模型:</span> {selectedAgent.defaultModel}
                  </div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}

/**
 * 简化版 Agent 选择器（向后兼容）
 */
export function AgentSelector({
  selectedAgentId,
  onAgentChange,
  className
}: {
  selectedAgentId?: string;
  onAgentChange?: (agentId: string) => void;
  className?: string;
}) {
  return (
    <EnhancedAgentSelector
      selectedAgentId={selectedAgentId}
      onAgentChange={onAgentChange}
      showConnectionStatus={false}
      className={className}
    />
  );
}