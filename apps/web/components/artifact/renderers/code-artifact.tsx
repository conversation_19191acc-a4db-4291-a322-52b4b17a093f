/**
 * 代码类型 Artifact 渲染器
 * 支持语法高亮、代码编辑、执行等功能
 */

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import { ScrollArea } from '@workspace/ui/components/scroll-area';
import { Separator } from '@workspace/ui/components/separator';
import { 
  Play, 
  Copy, 
  Download,
  Code,
  Save,
  X
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Editor } from '@monaco-editor/react';
import { ArtifactViewerProps, ArtifactEditorProps } from '@/lib/types/artifact';
import { toast } from 'sonner';

interface CodeContent {
  code: string;
  language: string;
  filename?: string;
  description?: string;
}

/**
 * 代码 Artifact 查看器
 */
export function CodeArtifactViewer({ artifact, onEdit }: ArtifactViewerProps) {
  const content = artifact.content as CodeContent;
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<string | null>(null);
  
  const canExecute = artifact.metadata.capabilities.includes('executable');
  const canEdit = artifact.metadata.capabilities.includes('editable');

  /**
   * 复制代码到剪贴板
   */
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content.code);
      toast.success('代码已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败');
    }
  };

  /**
   * 下载代码文件
   */
  const handleDownload = () => {
    const blob = new Blob([content.code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = content.filename || `code.${getFileExtension(content.language)}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('文件已下载');
  };

  /**
   * 执行代码（模拟）
   */
  const handleExecute = async () => {
    if (!canExecute) return;
    
    setIsExecuting(true);
    try {
      // 这里可以集成真实的代码执行环境
      // 目前只是模拟执行
      await new Promise(resolve => setTimeout(resolve, 1000));
      setExecutionResult('代码执行成功！\n输出: Hello, World!');
      toast.success('代码执行完成');
    } catch (error) {
      setExecutionResult('执行错误: ' + (error as Error).message);
      toast.error('代码执行失败');
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* 代码头部信息 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Badge variant="secondary">{content.language}</Badge>
          {content.filename && (
            <span className="text-sm text-gray-600">{content.filename}</span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleCopy}>
            <Copy className="w-4 h-4 mr-1" />
            复制
          </Button>
          
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="w-4 h-4 mr-1" />
            下载
          </Button>
          
          {canExecute && (
            <Button 
              variant="default" 
              size="sm" 
              onClick={handleExecute}
              disabled={isExecuting}
            >
              <Play className="w-4 h-4 mr-1" />
              {isExecuting ? '执行中...' : '运行'}
            </Button>
          )}
          
          {canEdit && onEdit && (
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Code className="w-4 h-4 mr-1" />
              编辑
            </Button>
          )}
        </div>
      </div>
      
      {/* 描述信息 */}
      {content.description && (
        <div className="p-4 bg-blue-50 border-b border-gray-200">
          <p className="text-sm text-blue-800">{content.description}</p>
        </div>
      )}
      
      {/* 代码内容 */}
      <div className="flex-1 overflow-hidden">
        <SyntaxHighlighter
          language={content.language}
          style={vscDarkPlus}
          customStyle={{
            margin: 0,
            height: '100%',
            fontSize: '14px',
            lineHeight: '1.5',
          }}
          showLineNumbers
          wrapLines
        >
          {content.code}
        </SyntaxHighlighter>
      </div>
      
      {/* 执行结果 */}
      {executionResult && (
        <div className="border-t border-gray-200 bg-gray-900 text-green-400 p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">执行结果</span>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setExecutionResult(null)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          <pre className="text-sm whitespace-pre-wrap">{executionResult}</pre>
        </div>
      )}
    </div>
  );
}

/**
 * 代码 Artifact 编辑器
 */
export function CodeArtifactEditor({ artifact, onSave, onCancel }: ArtifactEditorProps) {
  const content = artifact.content as CodeContent;
  const [code, setCode] = useState(content.code);
  const [language, setLanguage] = useState(content.language);
  const [filename, setFilename] = useState(content.filename || '');
  const [description, setDescription] = useState(content.description || '');

  /**
   * 保存更改
   */
  const handleSave = () => {
    const updatedContent: CodeContent = {
      code,
      language,
      filename: filename || undefined,
      description: description || undefined,
    };
    onSave(updatedContent);
  };

  return (
    <div className="h-full flex flex-col">
      {/* 编辑器头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <div>
            <label className="text-sm font-medium">语言</label>
            <select 
              value={language} 
              onChange={(e) => setLanguage(e.target.value)}
              className="ml-2 px-2 py-1 border rounded text-sm"
            >
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="python">Python</option>
              <option value="java">Java</option>
              <option value="cpp">C++</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              <option value="sql">SQL</option>
              <option value="json">JSON</option>
              <option value="yaml">YAML</option>
            </select>
          </div>
          
          <div>
            <label className="text-sm font-medium">文件名</label>
            <input 
              type="text"
              value={filename}
              onChange={(e) => setFilename(e.target.value)}
              placeholder="可选"
              className="ml-2 px-2 py-1 border rounded text-sm w-32"
            />
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={onCancel}>
            取消
          </Button>
          <Button size="sm" onClick={handleSave}>
            <Save className="w-4 h-4 mr-1" />
            保存
          </Button>
        </div>
      </div>
      
      {/* 描述输入 */}
      <div className="p-4 border-b border-gray-200">
        <label className="text-sm font-medium">描述</label>
        <input 
          type="text"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="代码描述（可选）"
          className="mt-1 w-full px-3 py-2 border rounded text-sm"
        />
      </div>
      
      {/* 代码编辑器 */}
      <div className="flex-1">
        <Editor
          height="100%"
          language={language}
          value={code}
          onChange={(value) => setCode(value || '')}
          theme="vs-dark"
          options={{
            fontSize: 14,
            lineHeight: 1.5,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            automaticLayout: true,
          }}
        />
      </div>
    </div>
  );
}

/**
 * 根据语言获取文件扩展名
 */
function getFileExtension(language: string): string {
  const extensions: Record<string, string> = {
    'javascript': 'js',
    'typescript': 'ts',
    'python': 'py',
    'java': 'java',
    'cpp': 'cpp',
    'html': 'html',
    'css': 'css',
    'sql': 'sql',
    'json': 'json',
    'yaml': 'yml',
  };
  
  return extensions[language] || 'txt';
}