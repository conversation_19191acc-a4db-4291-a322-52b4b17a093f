/**
 * Artifact 操作组件
 * 提供复制、下载、历史等操作
 */

'use client';

import { useState } from 'react';
import { Button } from '@workspace/ui/components/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@workspace/ui/components/dropdown-menu';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog';
import { Badge } from '@workspace/ui/components/badge';
import { ScrollArea } from '@workspace/ui/components/scroll-area';
import { 
  Copy, 
  Download, 
  History, 
  MoreHorizontal,
  Trash2,
  Share,
  FileText,
  RotateCcw
} from 'lucide-react';
import { Artifact } from '@/lib/types/artifact';
import { useArtifact } from '@/lib/stores/artifact-store';
import { toast } from 'sonner';

interface ArtifactActionsProps {
  artifact: Artifact;
}

/**
 * Artifact 操作菜单
 */
export function ArtifactActions({ artifact }: ArtifactActionsProps) {
  const [showHistory, setShowHistory] = useState(false);
  const { 
    duplicateArtifact, 
    deleteArtifact, 
    revertToVersion, 
    getArtifactHistory,
    setActiveArtifact 
  } = useArtifact();
  
  const history = getArtifactHistory(artifact.id);
  const canExport = artifact.metadata.capabilities.includes('exportable');
  const canVersion = artifact.metadata.capabilities.includes('versionable');

  /**
   * 复制 Artifact
   */
  const handleDuplicate = () => {
    const newId = duplicateArtifact(artifact.id);
    if (newId) {
      toast.success('Artifact 已复制');
    }
  };

  /**
   * 删除 Artifact
   */
  const handleDelete = () => {
    deleteArtifact(artifact.id);
    setActiveArtifact(null);
    toast.success('Artifact 已删除');
  };

  /**
   * 复制内容到剪贴板
   */
  const handleCopyContent = async () => {
    try {
      const content = typeof artifact.content === 'string' 
        ? artifact.content 
        : JSON.stringify(artifact.content, null, 2);
      await navigator.clipboard.writeText(content);
      toast.success('内容已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败');
    }
  };

  /**
   * 导出 Artifact
   */
  const handleExport = () => {
    try {
      const content = typeof artifact.content === 'string' 
        ? artifact.content 
        : JSON.stringify(artifact.content, null, 2);
      
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${artifact.title}.${getFileExtension(artifact.type)}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('文件已下载');
    } catch (error) {
      toast.error('导出失败');
    }
  };

  /**
   * 恢复到指定版本
   */
  const handleRevertToVersion = (version: number) => {
    revertToVersion(artifact.id, version);
    setShowHistory(false);
    toast.success(`已恢复到版本 ${version}`);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="p-2">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={handleCopyContent}>
            <Copy className="w-4 h-4 mr-2" />
            复制内容
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleDuplicate}>
            <FileText className="w-4 h-4 mr-2" />
            复制 Artifact
          </DropdownMenuItem>
          
          {canExport && (
            <DropdownMenuItem onClick={handleExport}>
              <Download className="w-4 h-4 mr-2" />
              导出文件
            </DropdownMenuItem>
          )}
          
          <DropdownMenuItem>
            <Share className="w-4 h-4 mr-2" />
            分享
          </DropdownMenuItem>
          
          {canVersion && history.length > 0 && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowHistory(true)}>
                <History className="w-4 h-4 mr-2" />
                版本历史
              </DropdownMenuItem>
            </>
          )}
          
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={handleDelete}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            删除
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* 版本历史对话框 */}
      <Dialog open={showHistory} onOpenChange={setShowHistory}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>版本历史</DialogTitle>
          </DialogHeader>
          
          <ScrollArea className="max-h-96">
            <div className="space-y-3">
              {/* 当前版本 */}
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div>
                  <div className="font-medium">版本 {artifact.metadata.version}</div>
                  <div className="text-sm text-gray-600">
                    {artifact.metadata.updatedAt.toLocaleString()}
                  </div>
                </div>
                <Badge>当前</Badge>
              </div>
              
              {/* 历史版本 */}
              {history.map((version) => (
                <div 
                  key={version.version}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div>
                    <div className="font-medium">版本 {version.version}</div>
                    <div className="text-sm text-gray-600">
                      {version.timestamp.toLocaleString()}
                    </div>
                    {version.changes && (
                      <div className="text-xs text-gray-500 mt-1">
                        {version.changes}
                      </div>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRevertToVersion(version.version)}
                    className="flex items-center gap-1"
                  >
                    <RotateCcw className="w-3 h-3" />
                    恢复
                  </Button>
                </div>
              ))}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
}

/**
 * 根据 Artifact 类型获取文件扩展名
 */
function getFileExtension(type: string): string {
  const extensions: Record<string, string> = {
    'code': 'txt',
    'document': 'md',
    'table': 'csv',
    'chart': 'json',
    'diagram': 'mmd',
    'component': 'html',
    'image': 'svg',
  };
  
  return extensions[type] || 'txt';
}