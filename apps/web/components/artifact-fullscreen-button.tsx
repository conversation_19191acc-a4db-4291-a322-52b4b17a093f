import { memo } from 'react';
import { Maximize, Minimize } from 'lucide-react';
import { Button } from '@workspace/ui/components/button';

interface ArtifactFullscreenButtonProps {
    isFullscreen: boolean;
    onToggleFullscreen: () => void;
}

function PureArtifactFullscreenButton({ isFullscreen, onToggleFullscreen }: ArtifactFullscreenButtonProps) {
    return (
        <Button
            data-testid='artifact-fullscreen-button'
            variant='outline'
            className='flex h-8 w-8 items-center justify-center p-0 dark:hover:bg-zinc-700'
            onClick={onToggleFullscreen}
            title={isFullscreen ? '退出全屏' : '全屏显示'}>
            {isFullscreen ? <Minimize size={18} /> : <Maximize size={18} />}
        </Button>
    );
}

export const ArtifactFullscreenButton = memo(PureArtifactFullscreenButton, (prevProps, nextProps) => {
    return prevProps.isFullscreen === nextProps.isFullscreen;
});
