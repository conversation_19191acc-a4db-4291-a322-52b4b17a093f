"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@workspace/ui/components/card";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Progress } from "@workspace/ui/components/progress";
import { Badge } from "@workspace/ui/components/badge";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@workspace/ui/components/dialog";
import { MultiAgentState, TaskStatus } from "@/types/multi-agent";
import { AgentCard } from "./agent-card";
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Users, 
  Clock, 
  CheckCircle2,
  AlertCircle,
  FileText,
  Activity
} from "lucide-react";

interface MultiAgentPanelProps {
  onTaskSubmit: (task: string) => void;
  state: MultiAgentState;
  isLoading: boolean;
}

export function MultiAgentPanel({ onTaskSubmit, state, isLoading }: MultiAgentPanelProps) {
  const [task, setTask] = useState("");
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 自动刷新状态
  useEffect(() => {
    if (!autoRefresh || state.overallStatus === TaskStatus.COMPLETED) return;
    
    const interval = setInterval(() => {
      // 这里可以调用状态更新
      console.log('Auto refresh state');
    }, 2000);

    return () => clearInterval(interval);
  }, [autoRefresh, state.overallStatus]);

  const handleSubmit = () => {
    if (task.trim()) {
      onTaskSubmit(task.trim());
      setTask("");
    }
  };

  const getOverallStatusIcon = () => {
    switch (state.overallStatus) {
      case TaskStatus.PENDING:
        return <Clock className="h-5 w-5" />;
      case TaskStatus.IN_PROGRESS:
        return <Activity className="h-5 w-5 animate-spin" />;
      case TaskStatus.COMPLETED:
        return <CheckCircle2 className="h-5 w-5" />;
      case TaskStatus.FAILED:
        return <AlertCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getOverallStatusColor = () => {
    switch (state.overallStatus) {
      case TaskStatus.PENDING:
        return "secondary";
      case TaskStatus.IN_PROGRESS:
        return "default";
      case TaskStatus.COMPLETED:
        return "success";
      case TaskStatus.FAILED:
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getOverallStatusText = () => {
    switch (state.overallStatus) {
      case TaskStatus.PENDING:
        return "等待开始";
      case TaskStatus.IN_PROGRESS:
        return "执行中";
      case TaskStatus.COMPLETED:
        return "已完成";
      case TaskStatus.FAILED:
        return "执行失败";
      default:
        return "未知状态";
    }
  };

  const formatExecutionTime = () => {
    if (!state.startTime) return "未开始";
    const end = state.endTime || Date.now();
    const duration = Math.round((end - state.startTime) / 1000);
    return `${duration}秒`;
  };

  const agentsList = Object.values(state.agents);
  const selectedAgentData = selectedAgent ? state.agents[selectedAgent] : null;

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      {/* 头部控制区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-6 w-6" />
            多智能体协作系统
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 任务输入 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">任务描述</label>
            <div className="flex gap-2">
              <Textarea
                placeholder="请描述您希望多智能体系统执行的任务..."
                value={task}
                onChange={(e) => setTask(e.target.value)}
                className="min-h-[100px]"
                disabled={isLoading}
              />
            </div>
          </div>
          
          {/* 控制按钮 */}
          <div className="flex gap-2">
            <Button 
              onClick={handleSubmit} 
              disabled={!task.trim() || isLoading}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              开始执行
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className="flex items-center gap-2"
            >
              {autoRefresh ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              {autoRefresh ? "停止自动刷新" : "开启自动刷新"}
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => window.location.reload()}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              重置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 整体状态显示 */}
      {state.task && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>任务执行状态</span>
              <Badge variant={getOverallStatusColor() as any} className="flex items-center gap-1">
                {getOverallStatusIcon()}
                {getOverallStatusText()}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>当前任务:</span>
                <span className="font-medium">{state.task}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>整体进度:</span>
                <span>{state.overallProgress}%</span>
              </div>
              <Progress value={state.overallProgress} className="h-2" />
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">会话ID:</span>
                <p className="font-mono text-xs">{state.sessionId}</p>
              </div>
              <div>
                <span className="text-muted-foreground">执行时间:</span>
                <p>{formatExecutionTime()}</p>
              </div>
              <div>
                <span className="text-muted-foreground">智能体数量:</span>
                <p>{agentsList.length}</p>
              </div>
              <div>
                <span className="text-muted-foreground">完成数量:</span>
                <p>{agentsList.filter(a => a.status === TaskStatus.COMPLETED).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 智能体网格 */}
      {agentsList.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>智能体状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agentsList.map((agent) => (
                <AgentCard
                  key={agent.id}
                  agent={agent}
                  onClick={() => setSelectedAgent(agent.id)}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 详细信息标签页 */}
      {(state.coordinatorLogs.length > 0 || state.finalResult) && (
        <Card>
          <CardContent className="p-0">
            <Tabs defaultValue="logs" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="logs">协调器日志</TabsTrigger>
                <TabsTrigger value="result">最终结果</TabsTrigger>
                <TabsTrigger value="summary">执行摘要</TabsTrigger>
              </TabsList>
              
              <TabsContent value="logs" className="p-6">
                <ScrollArea className="h-[300px] w-full">
                  <div className="space-y-2">
                    {state.coordinatorLogs.map((log, index) => (
                      <div key={index} className="text-sm font-mono bg-muted p-2 rounded">
                        {log}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
              
              <TabsContent value="result" className="p-6">
                {state.finalResult ? (
                  <ScrollArea className="h-[300px] w-full">
                    <div className="prose prose-sm max-w-none">
                      <pre className="whitespace-pre-wrap text-sm">
                        {state.finalResult}
                      </pre>
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    还没有最终结果
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="summary" className="p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-muted rounded">
                      <div className="text-2xl font-bold">{agentsList.length}</div>
                      <div className="text-sm text-muted-foreground">总智能体</div>
                    </div>
                    <div className="text-center p-4 bg-muted rounded">
                      <div className="text-2xl font-bold text-green-600">
                        {agentsList.filter(a => a.status === TaskStatus.COMPLETED).length}
                      </div>
                      <div className="text-sm text-muted-foreground">已完成</div>
                    </div>
                    <div className="text-center p-4 bg-muted rounded">
                      <div className="text-2xl font-bold text-blue-600">
                        {agentsList.filter(a => a.status === TaskStatus.IN_PROGRESS).length}
                      </div>
                      <div className="text-sm text-muted-foreground">执行中</div>
                    </div>
                    <div className="text-center p-4 bg-muted rounded">
                      <div className="text-2xl font-bold text-red-600">
                        {agentsList.filter(a => a.status === TaskStatus.FAILED).length}
                      </div>
                      <div className="text-sm text-muted-foreground">失败</div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* 智能体详情对话框 */}
      <Dialog open={!!selectedAgent} onOpenChange={() => setSelectedAgent(null)}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {selectedAgentData?.name} - 详细信息
            </DialogTitle>
          </DialogHeader>
          
          {selectedAgentData && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground">智能体ID:</span>
                  <p className="font-mono text-sm">{selectedAgentData.id}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">类型:</span>
                  <p>{selectedAgentData.type}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">状态:</span>
                  <Badge variant={getOverallStatusColor() as any}>
                    {getOverallStatusText()}
                  </Badge>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">进度:</span>
                  <p>{selectedAgentData.progress}%</p>
                </div>
              </div>
              
              {selectedAgentData.currentTask && (
                <div>
                  <span className="text-sm text-muted-foreground">当前任务:</span>
                  <p className="mt-1">{selectedAgentData.currentTask}</p>
                </div>
              )}
              
              {selectedAgentData.error && (
                <div>
                  <span className="text-sm text-muted-foreground text-destructive">错误信息:</span>
                  <p className="mt-1 text-destructive">{selectedAgentData.error}</p>
                </div>
              )}
              
              <div>
                <span className="text-sm text-muted-foreground">执行日志:</span>
                <ScrollArea className="h-[200px] mt-2 border rounded p-2">
                  <div className="space-y-1">
                    {selectedAgentData.logs.map((log, index) => (
                      <div key={index} className="text-xs font-mono">
                        {log}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
              
              {selectedAgentData.result && (
                <div>
                  <span className="text-sm text-muted-foreground">执行结果:</span>
                  <ScrollArea className="h-[150px] mt-2 border rounded p-2">
                    <pre className="text-xs whitespace-pre-wrap">
                      {selectedAgentData.result}
                    </pre>
                  </ScrollArea>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 