import type { UIMessage } from 'ai';
import { formatDistance } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import { memo, useCallback, useEffect, useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { useDebounceCallback } from 'usehooks-ts';
import { useWindowSize } from '@/hooks/use-window-size';
import type { Document, Vote } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';
import { MultimodalInput } from './multimodal-input';
import { Toolbar } from './toolbar';
import { VersionFooter } from './version-footer';
import { ArtifactActions } from './artifact-actions';
import { ArtifactCloseButton } from './artifact-close-button';
import { ArtifactFullscreenButton } from './artifact-fullscreen-button';
import { ArtifactMessages } from './artifact-messages';
import { useSidebar } from '@workspace/ui/components/sidebar';
import { useArtifact } from '@/hooks/use-artifact';
import { SystemIndicator } from './system-indicator';
import { ChatHeader } from './chat-header';

// 导入新的 Agent 组件 - 直接从各个 agent 包导入
import { CodeArtifactPage } from '@workspace/code-agent';
import { ImageArtifactPage } from '@workspace/image-agent';
import { SheetArtifactPage } from '@workspace/sheet-agent';
import { TextArtifactPage } from '@workspace/text-agent';
import { MarioArtifactPage } from '@workspace/mario-agent';

// 导入新的 agent 系统
import equal from 'fast-deep-equal';
import type { UseChatHelpers } from '@ai-sdk/react';
import type { VisibilityType } from './visibility-selector';
import type { DataStreamDelta } from './data-stream-handler';

// 类型定义
interface ArtifactMetadata {
  outputs?: unknown[];
  [key: string]: unknown;
}

interface ArtifactDraft {
  content: string;
  status: 'streaming' | 'idle';
  isVisible: boolean;
  [key: string]: unknown;
}

type SetArtifactFunction = (updaterFn: UIArtifact | ((currentArtifact: UIArtifact) => UIArtifact)) => void;
type SetMetadataFunction = (updater: (metadata: ArtifactMetadata) => ArtifactMetadata) => void;

// Clean Agent 定义结构
interface AgentDefinition {
  kind: 'code' | 'image' | 'sheet' | 'text' | 'mario';
  description: string;
  content: React.ComponentType<any>;
  initialize?: (params: { documentId: string; setMetadata: SetMetadataFunction }) => void;
  onStreamPart?: (params: {
    streamPart: DataStreamDelta;
    setArtifact: SetArtifactFunction;
    setMetadata: SetMetadataFunction;
  }) => void;
}

// Clean Agent 定义 - 使用解耦的 ArtifactPage 组件
export const cleanAgentDefinitions: AgentDefinition[] = [
  {
    kind: 'text',
    description: 'Useful for text generation and editing',
    content: TextArtifactPage,
    initialize: ({ setMetadata }) => {
      console.log('🚀 [TEXT-AGENT] Initializing');
      setMetadata(() => ({}));
    },
    onStreamPart: ({ streamPart, setArtifact }) => {
      if (streamPart.type === 'kind') {
        console.log('🏷️ [TEXT-AGENT] Setting kind:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          kind: streamPart.content as ArtifactKind,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'title') {
        console.log('📝 [TEXT-AGENT] Setting title:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          title: streamPart.content as string,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'text-delta') {
        console.log('🔄 [TEXT-AGENT] Received text delta:', {
          contentLength: (streamPart.content as string).length,
          isComplete: streamPart.isComplete,
          content: (streamPart.content as string).substring(0, 50) + '...'
        });

        setArtifact((draft) => {
          const newContent = streamPart.isComplete
            ? (streamPart.content as string) // 如果是完整内容，直接替换
            : draft.content + (streamPart.content as string); // 否则累积增量内容

          return {
            ...draft,
            content: newContent,
            status: streamPart.isComplete ? ('idle' as const) : ('streaming' as const),
            isVisible: newContent.length > 100 ? true : draft.isVisible
          };
        });
      } else if (streamPart.type === 'finish') {
        console.log('✅ [TEXT-AGENT] Streaming finished');
        setArtifact((draft) => ({
          ...draft,
          status: 'idle' as const
        }));
      } else if (streamPart.type === 'clear') {
        console.log('🧹 [TEXT-AGENT] Clearing content');
        setArtifact((draft) => ({
          ...draft,
          content: '',
          status: 'streaming' as const
        }));
      }
    }
  },
  {
    kind: 'code',
    description: 'Useful for code generation and execution',
    content: CodeArtifactPage,
    initialize: ({ setMetadata }) => {
      console.log('🚀 [CODE-AGENT] Initializing');
      setMetadata(() => ({ outputs: [] }));
    },
    onStreamPart: ({ streamPart, setArtifact, setMetadata }) => {
      if (streamPart.type === 'kind') {
        console.log('🏷️ [CODE-AGENT] Setting kind:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          kind: streamPart.content as ArtifactKind,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'title') {
        console.log('📝 [CODE-AGENT] Setting title:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          title: streamPart.content as string,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'code-delta') {
        console.log('🔄 [CODE-AGENT] Received code delta:', {
          contentLength: (streamPart.content as string).length,
          isComplete: streamPart.isComplete
        });

        setArtifact((draft) => {
          // 如果是完整内容，直接替换；否则累积
          const newContent = streamPart.isComplete
            ? (streamPart.content as string)
            : draft.content + (streamPart.content as string);

          // 修复：确保在有内容时就显示artifact，特别是完整内容替换时
          const shouldShow =
            newContent.length > 0 &&
            (streamPart.isComplete || // 完整内容时立即显示
              newContent.length > 50); // 或者累积内容超过50字符时显示

          return {
            ...draft,
            content: newContent,
            status: 'streaming' as const,
            isVisible: shouldShow ? true : draft.isVisible
          };
        });
        setMetadata((metadata) => ({ ...metadata, outputs: [] }));
      } else if (streamPart.type === 'finish') {
        console.log('✅ [CODE-AGENT] Streaming finished');
        setArtifact((draft) => ({
          ...draft,
          status: 'idle' as const,
          isVisible: draft.content.length > 0 ? true : draft.isVisible
        }));
      }
    }
  },
  {
    kind: 'image',
    description: 'Useful for image generation and editing',
    content: ImageArtifactPage,
    initialize: ({ setMetadata }) => {
      console.log('🚀 [IMAGE-AGENT] Initializing');
      setMetadata(() => ({}));
    },
    onStreamPart: ({ streamPart, setArtifact }) => {
      if (streamPart.type === 'kind') {
        console.log('🏷️ [IMAGE-AGENT] Setting kind:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          kind: streamPart.content as ArtifactKind,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'title') {
        console.log('📝 [IMAGE-AGENT] Setting title:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          title: streamPart.content as string,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'image-delta') {
        console.log('🔄 [IMAGE-AGENT] Received image delta:', {
          contentLength: (streamPart.content as string).length,
          isComplete: streamPart.isComplete
        });

        setArtifact((draft) => {
          // 图片通常是完整内容，直接替换
          const newContent = streamPart.isComplete
            ? (streamPart.content as string)
            : draft.content + (streamPart.content as string);

          return {
            ...draft,
            content: newContent,
            status: 'streaming' as const,
            isVisible: true
          };
        });
      } else if (streamPart.type === 'finish') {
        console.log('✅ [IMAGE-AGENT] Streaming finished');
        setArtifact((draft) => ({
          ...draft,
          status: 'idle' as const
        }));
      }
    }
  },
  {
    kind: 'sheet',
    description: 'Useful for spreadsheet generation and data analysis',
    content: SheetArtifactPage,
    initialize: ({ setMetadata }) => {
      console.log('🚀 [SHEET-AGENT] Initializing');
      setMetadata(() => ({}));
    },
    onStreamPart: ({ streamPart, setArtifact }) => {
      if (streamPart.type === 'kind') {
        console.log('🏷️ [SHEET-AGENT] Setting kind:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          kind: streamPart.content as ArtifactKind,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'title') {
        console.log('📝 [SHEET-AGENT] Setting title:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          title: streamPart.content as string,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'sheet-delta') {
        console.log('🔄 [SHEET-AGENT] Received sheet delta:', {
          contentLength: (streamPart.content as string).length,
          isComplete: streamPart.isComplete
        });

        setArtifact((draft) => {
          // 表格通常是完整内容，直接替换
          const newContent = streamPart.isComplete
            ? (streamPart.content as string)
            : draft.content + (streamPart.content as string);

          return {
            ...draft,
            content: newContent,
            status: 'streaming' as const,
            isVisible: newContent.length > 50 ? true : draft.isVisible
          };
        });
      } else if (streamPart.type === 'finish') {
        console.log('✅ [SHEET-AGENT] Streaming finished');
        setArtifact((draft) => ({
          ...draft,
          status: 'idle' as const
        }));
      }
    }
  },
  {
    kind: 'mario',
    description: 'Useful for Mario test case generation and container management',
    content: MarioArtifactPage,
    initialize: ({ setMetadata }) => {
      console.log('🚀 [MARIO-AGENT] Initializing');
      setMetadata(() => ({}));
    },
    onStreamPart: ({ streamPart, setArtifact }) => {
      if (streamPart.type === 'kind') {
        console.log('🏷️ [MARIO-AGENT] Setting kind:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          kind: streamPart.content as ArtifactKind,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'title') {
        console.log('📝 [MARIO-AGENT] Setting title:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          title: streamPart.content as string,
          status: 'streaming' as const
        }));
      } else if (streamPart.type === 'mario-delta') {
        console.log('🔄 [MARIO-AGENT] Received mario delta:', {
          contentLength: (streamPart.content as string).length,
          isComplete: streamPart.isComplete
        });

        setArtifact((draft) => {
          // Mario测试用例通常是完整内容，直接替换
          const newContent = streamPart.isComplete
            ? (streamPart.content as string)
            : draft.content + (streamPart.content as string);

          return {
            ...draft,
            content: newContent,
            status: 'streaming' as const,
            isVisible: newContent.length > 100 ? true : draft.isVisible
          };
        });
      } else if (streamPart.type === 'text-delta') {
        // 兼容文本流
        console.log('🔄 [MARIO-AGENT] Received text delta:', streamPart.content);
        setArtifact((draft) => ({
          ...draft,
          content: draft.content + (streamPart.content as string),
          status: 'streaming' as const,
          isVisible: draft.content.length > 100 ? true : draft.isVisible
        }));
      } else if (streamPart.type === 'finish') {
        console.log('✅ [MARIO-AGENT] Streaming finished');
        setArtifact((draft) => ({
          ...draft,
          status: 'idle' as const
        }));
      }
    }
  }
];

// 使用 Clean 架构的定义
export const artifactDefinitions = cleanAgentDefinitions;
export type ArtifactKind = (typeof artifactDefinitions)[number]['kind'];

export interface UIArtifact {
  title: string;
  documentId: string;
  kind: ArtifactKind;
  content: string;
  isVisible: boolean;
  status: 'streaming' | 'idle';
  boundingBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
  // 新增 agent 信息
  agentId?: string;
  agentName?: string;
  agentVersion?: string;
}

function PureArtifact({
  chatId,
  input,
  setInput,
  handleSubmit,
  status,
  stop,
  append,
  messages,
  setMessages,
  reload,
  votes,
  isReadonly,
  selectedVisibilityType,
  selectedModelId
}: {
  chatId: string;
  input: string;
  setInput: UseChatHelpers['setInput'];
  status: UseChatHelpers['status'];
  stop: UseChatHelpers['stop'];
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  votes: Array<Vote> | undefined;
  append: UseChatHelpers['append'];
  handleSubmit: UseChatHelpers['handleSubmit'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  selectedVisibilityType: VisibilityType;
  selectedModelId: string;
}) {
  const { artifact, setArtifact, metadata, setMetadata } = useArtifact();
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen((prev) => !prev);
  }, []);

  const {
    data: documents,
    isLoading: isDocumentsFetching,
    mutate: mutateDocuments
  } = useSWR<Array<Document>>(
    artifact.documentId !== 'init' && artifact.status !== 'streaming'
      ? `/api/document?id=${artifact.documentId}`
      : null,
    fetcher
  );

  const [mode, setMode] = useState<'edit' | 'diff'>('edit');
  const [document, setDocument] = useState<Document | null>(null);
  const [currentVersionIndex, setCurrentVersionIndex] = useState(-1);

  const { open: isSidebarOpen } = useSidebar();

  useEffect(() => {
    if (documents && documents.length > 0) {
      const mostRecentDocument = documents.at(-1);

      if (mostRecentDocument) {
        setDocument(mostRecentDocument);
        setCurrentVersionIndex(documents.length - 1);
        setArtifact((currentArtifact) => ({
          ...currentArtifact,
          content: mostRecentDocument.content ?? ''
        }));
      }
    }
  }, [documents, setArtifact]);

  useEffect(() => {
    mutateDocuments();
  }, [artifact.status, mutateDocuments]);

  const { mutate } = useSWRConfig();
  const [isContentDirty, setIsContentDirty] = useState(false);

  const handleContentChange = useCallback(
    (updatedContent: string) => {
      if (!artifact) return;

      mutate<Array<Document>>(
        `/api/document?id=${artifact.documentId}`,
        async (currentDocuments) => {
          if (!currentDocuments) return undefined;

          const currentDocument = currentDocuments.at(-1);

          if (!currentDocument || !currentDocument.content) {
            setIsContentDirty(false);
            return currentDocuments;
          }

          if (currentDocument.content !== updatedContent) {
            await fetch(`/api/document?id=${artifact.documentId}`, {
              method: 'POST',
              body: JSON.stringify({
                title: artifact.title,
                content: updatedContent,
                kind: artifact.kind
              })
            });

            setIsContentDirty(false);

            const newDocument = {
              ...currentDocument,
              content: updatedContent,
              createdAt: new Date()
            };

            return [...currentDocuments, newDocument];
          }
          return currentDocuments;
        },
        { revalidate: false }
      );
    },
    [artifact, mutate]
  );

  const debouncedHandleContentChange = useDebounceCallback(handleContentChange, 2000);

  const saveContent = useCallback(
    (updatedContent: string, debounce: boolean) => {
      if (document && updatedContent !== document.content) {
        setIsContentDirty(true);

        if (debounce) {
          debouncedHandleContentChange(updatedContent);
        } else {
          handleContentChange(updatedContent);
        }
      }
    },
    [document, debouncedHandleContentChange, handleContentChange]
  );

  function getDocumentContentById(index: number) {
    if (!documents) return '';
    if (!documents[index]) return '';
    return documents[index].content ?? '';
  }

  const handleVersionChange = (type: 'next' | 'prev' | 'toggle' | 'latest') => {
    if (!documents) return;

    if (type === 'latest') {
      setCurrentVersionIndex(documents.length - 1);
      setMode('edit');
    }

    if (type === 'toggle') {
      setMode((mode) => (mode === 'edit' ? 'diff' : 'edit'));
    }

    if (type === 'prev') {
      if (currentVersionIndex > 0) {
        setCurrentVersionIndex((index) => index - 1);
      }
    } else if (type === 'next') {
      if (currentVersionIndex < documents.length - 1) {
        setCurrentVersionIndex((index) => index + 1);
      }
    }
  };

  /*
   * NOTE: if there are no documents, or if
   * the documents are being fetched, then
   * we mark it as the current version.
   */

  const isCurrentVersion = documents && documents.length > 0 ? currentVersionIndex === documents.length - 1 : true;

  const { width: windowWidth, height: windowHeight } = useWindowSize();
  const isMobile = windowWidth ? windowWidth < 768 : false;

  const artifactDefinition = artifactDefinitions.find((definition) => definition.kind === artifact.kind);

  if (!artifactDefinition) {
    throw new Error('Artifact definition not found!');
  }

  useEffect(() => {
    if (artifact.documentId !== 'init') {
      if (artifactDefinition.initialize) {
        artifactDefinition.initialize({
          documentId: artifact.documentId,
          setMetadata
        });
      }
    }
  }, [artifact.documentId, artifactDefinition, setMetadata]);

  return (
    <AnimatePresence>
      {artifact.isVisible && (
        <motion.div
          className='dark:bg-muted bg-background relative flex h-full flex-col overflow-hidden'
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}>
                    <div className='flex h-full flex-col'>
            {/* 固定的header */}
            <div className='bg-background/95 dark:bg-background/95 flex h-12 flex-row items-center justify-between border-b border-zinc-200 p-2 backdrop-blur-lg dark:border-zinc-700 flex-shrink-0'>
              <div className='flex h-full min-w-0 flex-1 flex-row items-center gap-2'>
                <ArtifactCloseButton />
                <ArtifactFullscreenButton isFullscreen={isFullscreen} onToggleFullscreen={toggleFullscreen} />

                <div className='flex h-full min-w-0 flex-1 flex-row items-center gap-2'>
                  <div className='flex items-center truncate font-medium'>{artifact.title}</div>

                  <SystemIndicator
                    system='new'
                    type={artifact.kind as 'code' | 'image' | 'sheet' | 'text'}
                    context={{ isNewSystem: true, agentType: artifact.kind }}
                    className='ml-2'
                    agentId={artifact.agentId}
                    agentName={artifact.agentName}
                    agentVersion={artifact.agentVersion}
                  />

                  {isContentDirty ? (
                    <div className='text-muted-foreground flex items-center whitespace-nowrap text-sm'>
                      Saving changes...
                    </div>
                  ) : document ? (
                    <div className='text-muted-foreground flex items-center whitespace-nowrap text-sm'>
                      {`Updated ${formatDistance(new Date(document.createdAt), new Date(), {
                        addSuffix: true
                      })}`}
                    </div>
                  ) : (
                    <div className='bg-muted-foreground/20 flex h-3 w-32 animate-pulse items-center rounded-md' />
                  )}
                </div>
              </div>

              <div className='flex h-full items-center'>
                <ArtifactActions
                  artifact={artifact}
                  currentVersionIndex={currentVersionIndex}
                  handleVersionChange={handleVersionChange}
                  isCurrentVersion={isCurrentVersion}
                  mode={mode}
                  metadata={metadata}
                  setMetadata={setMetadata}
                />
              </div>
            </div>
            
            {/* 滚动内容区域 */}
            <div className='dark:bg-muted bg-background !max-w-full flex-1 overflow-y-auto min-h-0'>
              <div className='pb-4'>
                <artifactDefinition.content
                  title={artifact.title}
                  content={isCurrentVersion ? artifact.content : getDocumentContentById(currentVersionIndex)}
                  mode={mode}
                  status={artifact.status}
                  currentVersionIndex={currentVersionIndex}
                  suggestions={[]}
                  onSaveContent={saveContent}
                  isInline={false}
                  isCurrentVersion={isCurrentVersion}
                  getDocumentContentById={getDocumentContentById}
                  isLoading={isDocumentsFetching && !artifact.content}
                  metadata={metadata}
                  setMetadata={setMetadata}
                />

                {isCurrentVersion && (
                  <Toolbar
                    append={append}
                    status={status}
                    stop={stop}
                    setMessages={setMessages}
                    artifactKind={artifact.kind}
                    content={artifact.content}
                    setMetadata={setMetadata}
                  />
                )}
              </div>
            </div>
          </div>

          {!isCurrentVersion && (
            <div className='border-t border-zinc-200 dark:border-zinc-700'>
              <VersionFooter
                currentVersionIndex={currentVersionIndex}
                documents={documents}
                handleVersionChange={handleVersionChange}
              />
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export const Artifact = memo(PureArtifact, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;
  if (prevProps.input !== nextProps.input) return false;
  if (!equal(prevProps.messages, nextProps.messages.length)) return false;
  if (prevProps.selectedVisibilityType !== nextProps.selectedVisibilityType) return false;
  if (prevProps.selectedModelId !== nextProps.selectedModelId) return false;

  return true;
});
