import { motion } from 'framer-motion';

export const Greeting = () => {
    return (
        <div key='overview' className='mx-auto flex size-full max-w-6xl flex-col justify-center px-8 md:mt-20'>
            <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ delay: 0.5 }}
                className='text-2xl font-semibold'>
                你好！
            </motion.div>
            <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ delay: 0.6 }}
                className='text-2xl text-zinc-500'>
                今天我可以为您做些什么？
            </motion.div>

            <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ delay: 0.8 }}
                className='mt-8'></motion.div>
        </div>
    );
};
