'use client';

import { usePathname } from 'next/navigation';
import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { useAgents, Agent as AgentType } from '@/hooks/use-agents';

// Define the shape of an agent. Adjust as needed for your app.
export type Agent = AgentType;

// Context value type
type AgentContextType = {
  currentAgent: Agent | null;
  setAgent: (agent: Agent) => void;
  agents: Agent[];
  setAgents: (agents: Agent[]) => void;
  loading: boolean;
  error: string | null;
};

const AgentContext = createContext<AgentContextType | undefined>(undefined);

// 预定义的fallback agents，用于立即初始化
const FALLBACK_AGENTS: Agent[] = [
  {
    id: 'haiku',
    name: 'Haiku Agent',
    description: '专门用于创作诗歌的AI助手',
    route: '/haiku',
    originalId: 'haiku'
  },
  {
    id: 'mario',
    name: 'Mario Agent',
    description: '<PERSON>是基于TestNG的Java自动化测试框架，可以自动生成EC和Thrift泛化的自动化测试用例',
    route: '/mario',
    originalId: 'mario'
  }
];

export const AgentProvider = ({ children }: { children: ReactNode }) => {
  const { agents, loading, error } = useAgents();
  const pathname = usePathname();
  const [currentAgent, setCurrentAgent] = useState<Agent | null>(null);
  const initializedRef = useRef(false);

  // 立即初始化agent，不等待API响应
  useEffect(() => {
    if (!initializedRef.current) {
      const pathSegment = pathname?.split('/')[1] || '';

      // 首先尝试从fallback agents中匹配
      const fallbackAgent = FALLBACK_AGENTS.find((agent) => {
        return agent.route === `/${pathSegment}`;
      });

      if (fallbackAgent) {
        setCurrentAgent(fallbackAgent);
        initializedRef.current = true;
        console.log('Agent immediately initialized from fallback:', fallbackAgent.id, 'for path:', pathname);
        return;
      }

      // 如果没有fallback匹配，等待API数据
      if (agents.length > 0) {
        const matchedAgent = agents.find((agent) => {
          return agent.route === `/${pathSegment}`;
        });

        const selectedAgent = matchedAgent || agents[0] || null;
        setCurrentAgent(selectedAgent);
        initializedRef.current = true;
        console.log('Agent initialized from API:', selectedAgent?.id, 'for path:', pathname);
      }
    }
  }, [agents, pathname]);

  // 当路径变化时，更新当前 agent（但不重置初始化标记）
  useEffect(() => {
    if (initializedRef.current) {
      const pathSegment = pathname?.split('/')[1] || '';

      // 首先尝试从fallback agents中匹配
      const fallbackAgent = FALLBACK_AGENTS.find((agent) => {
        return agent.route === `/${pathSegment}`;
      });

      if (fallbackAgent && fallbackAgent.id !== currentAgent?.id) {
        setCurrentAgent(fallbackAgent);
        console.log('Agent changed to fallback due to path change:', fallbackAgent.id, 'for path:', pathname);
        return;
      }

      // 如果有API数据，尝试从API数据中匹配
      if (agents.length > 0) {
        const matchedAgent = agents.find((agent) => {
          return agent.route === `/${pathSegment}`;
        });

        // 只有当找到不同的匹配 agent 时才更新
        if (matchedAgent && matchedAgent.id !== currentAgent?.id) {
          setCurrentAgent(matchedAgent);
          console.log('Agent changed to API agent due to path change:', matchedAgent.id, 'for path:', pathname);
        }
      }
    }
  }, [pathname, agents, currentAgent?.id]);

  const setAgent = (agent: Agent) => {
    setCurrentAgent(agent);
    console.log('Agent manually set to:', agent.id);
  };

  const setAgents = (newAgents: Agent[]) => {
    // 这个方法保留用于向后兼容，但实际上 agents 由 hook 管理
    console.warn('setAgents is deprecated, agents are now managed by useAgents hook');
  };

  return (
    <AgentContext.Provider
      value={{
        currentAgent,
        setAgent,
        agents,
        setAgents,
        loading,
        error
      }}>
      {children}
    </AgentContext.Provider>
  );
};

export const useAgent = () => {
  const context = useContext(AgentContext);
  if (!context) {
    throw new Error('useAgent must be used within an AgentProvider');
  }
  return context;
};
