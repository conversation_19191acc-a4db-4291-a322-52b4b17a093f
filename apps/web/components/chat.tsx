'use client';

import type { UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useEffect, useState, useRef, useCallback } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { ChatHeader } from '@/components/chat-header';
import type { Vote } from '@/lib/db/schema';
import { fetcher, fetchWithErrorHandlers, generateUUID } from '@/lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { unstable_serialize } from 'swr/infinite';
import { getChatHistoryPaginationKey } from '@/components/layout/sidebar-history';
import { toast } from './toast';
import { useSearchParams } from 'next/navigation';
import { useChatVisibility } from '@/hooks/use-chat-visibility';
import { useAutoResume } from '@/hooks/use-auto-resume';
import { ChatSDKError } from '@/lib/errors';
import { useAgent } from '@/components/providers/agent-provider';
// 移除MarioChat导入，现在在普通聊天界面中处理Mario功能
import { useArtifact } from '@/hooks/use-artifact';

export function Chat({
    id,
    initialMessages,
    initialChatModel,
    initialVisibilityType,
    isReadonly,
    autoResume
}: {
    id: string;
    initialMessages: Array<UIMessage>;
    initialChatModel: string;
    initialVisibilityType: VisibilityType;
    isReadonly: boolean;
    autoResume: boolean;
}) {
    const { mutate } = useSWRConfig();
    const { activeAgent, runAgent, agents, executingAgent, isExecuting, setExecutingAgent, setIsExecuting } =
        useAgent();
    const { setArtifact, setMetadata } = useArtifact();

    // Mario模式状态
    const [marioModeEnabled, setMarioModeEnabled] = useState(false);

    // 跟踪是否需要等待API响应完成
    const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);

    const { visibilityType } = useChatVisibility({
        chatId: id,
        initialVisibilityType
    });

    const {
        messages,
        setMessages,
        handleSubmit,
        input,
        setInput,
        append,
        status,
        stop,
        reload,
        experimental_resume,
        data
    } = useChat({
        id,
        initialMessages,
        experimental_throttle: 100,
        sendExtraMessageFields: true,
        generateId: generateUUID,
        fetch: fetchWithErrorHandlers,
        experimental_prepareRequestBody: (body) => {
            // 在发送消息时设置执行状态
            if (activeAgent) {
                setExecutingAgent(activeAgent);
                setIsExecuting(true);
            }

            return {
                id,
                message: body.messages.at(-1),
                selectedChatModel: initialChatModel,
                selectedVisibilityType: visibilityType,
                useAgent: activeAgent ? true : false,
                agentId: activeAgent?.metadata.id
            };
        },
        onFinish: (message) => {
            // 在完成时清除执行状态
            setExecutingAgent(null);
            setIsExecuting(false);
            mutate(unstable_serialize(getChatHistoryPaginationKey));

            // 检查是否是Mario相关消息
            if (message.content) {
                handleMarioMessage(message.content);
            }
        },
        onError: (error) => {
            // 在错误时也清除执行状态
            setExecutingAgent(null);
            setIsExecuting(false);

            if (error instanceof ChatSDKError) {
                toast({
                    type: 'error',
                    description: error.message
                });
            }
        }
    });

    const searchParams = useSearchParams();
    const query = searchParams.get('query');

    const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

    useEffect(() => {
        if (query && !hasAppendedQuery) {
            append({
                role: 'user',
                content: query
            });

            setHasAppendedQuery(true);
            // 清理URL中的查询参数，确保URL格式正确
            const cleanUrl = `/chat/${id}`;
            if (window.location.pathname !== cleanUrl || window.location.search) {
                window.history.replaceState({}, '', cleanUrl);
            }
        }
    }, [query, append, hasAppendedQuery, id]);

    const { data: votes } = useSWR<Array<Vote>>(messages.length >= 2 ? `/api/vote?chatId=${id}` : null, fetcher);

    const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

    // Mario相关处理函数
    const handleMarioToggle = (enabled: boolean) => {
        setMarioModeEnabled(enabled);
    };

    // 移除了handleCreateContainer和handleMuseInput函数，因为现在不再使用独立的Mario聊天界面

    // 检测并处理Mario相关消息
    const handleMarioMessage = useCallback(
        async (message: string) => {
            if (message.toLowerCase().includes('mario') && message.toLowerCase().includes('用例')) {
                // 如果不在Mario模式，自动启用Mario模式
                if (!marioModeEnabled) {
                    setMarioModeEnabled(true);
                }
                // 注意：这里不再自动显示ArtifactPage
                // ArtifactPage应该在用户确认"符合，继续"后才显示
            }
        },
        [marioModeEnabled]
    );

    // 监听用户消息，检查是否包含Mario相关内容
    useEffect(() => {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage && lastMessage.role === 'user' && lastMessage.content) {
            handleMarioMessage(lastMessage.content);
        }
    }, [messages, handleMarioMessage]);

    useAutoResume({
        autoResume,
        initialMessages,
        experimental_resume,
        data,
        setMessages
    });

    return (
        <>
            <div className='bg-background flex h-dvh min-w-0 flex-col'>
                <ChatHeader
                    chatId={id}
                    selectedModelId={initialChatModel}
                    selectedVisibilityType={initialVisibilityType}
                    isReadonly={isReadonly}
                />

                <Messages
                    chatId={id}
                    status={status}
                    votes={votes}
                    messages={messages}
                    setMessages={setMessages}
                    reload={reload}
                    isReadonly={isReadonly}
                    isArtifactVisible={isArtifactVisible}
                    marioModeEnabled={marioModeEnabled}
                    onConfirmMarioGeneration={(testCase: string, metadata: any) => {
                        console.log('🎯 [MARIO] 用户确认生成，在当前聊天界面显示artifact');

                        // 用户确认生成，在当前聊天界面显示ArtifactPage
                        const documentId = 'mario-test-case-' + Date.now();
                        setArtifact({
                            documentId,
                            kind: 'mario',
                            title: 'Mario测试用例',
                            content: testCase,
                            status: 'idle',
                            isVisible: true,
                            boundingBox: {
                                top: 0,
                                left: 0,
                                width: 0,
                                height: 0
                            },
                            agentId: 'mario-agent',
                            agentName: 'Mario Agent',
                            agentVersion: '1.0.0'
                        });

                        // 单独设置metadata
                        if (metadata) {
                            setMetadata(() => metadata);
                        }

                        console.log('✅ [MARIO] Artifact已设置，应该在右侧显示');
                    }}
                />

                {!isArtifactVisible && (
                    <form className='bg-background mx-auto flex w-full gap-2 px-4 pb-4 md:max-w-6xl md:pb-6'>
                        {!isReadonly && (
                            <MultimodalInput
                                chatId={id}
                                input={input}
                                setInput={setInput}
                                handleSubmit={handleSubmit}
                                status={status}
                                stop={stop}
                                messages={messages}
                                setMessages={setMessages}
                                append={append}
                                selectedVisibilityType={visibilityType}
                                selectedModelId={initialChatModel}
                                isReadonly={isReadonly}
                                onMarioModeToggle={handleMarioToggle}
                            />
                        )}
                    </form>
                )}
            </div>

            <Artifact
                chatId={id}
                input={input}
                setInput={setInput}
                handleSubmit={handleSubmit}
                status={status}
                stop={stop}
                append={append}
                messages={messages}
                setMessages={setMessages}
                reload={reload}
                votes={votes}
                isReadonly={isReadonly}
                selectedVisibilityType={visibilityType}
                selectedModelId={initialChatModel}
            />
        </>
    );
}
