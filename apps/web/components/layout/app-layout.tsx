'use client';

import { useState, useMemo } from 'react';
import { Workspace } from '@/components/layout/workspace';
import { AppSidebar } from '@/components/layout/app-sidebar';
import { SidebarProvider } from '@workspace/ui/components/sidebar';
import type { AgentType } from '@/lib/types';
import { useCopilotChatSuggestions } from '@copilotkit/react-ui';
import { usePathname } from 'next/navigation';
import type { AgentSession } from '@/lib/db/schema';

interface AppLayoutProps {
  sessionId?: string;
  session?: AgentSession;
}

export function AppLayout({ sessionId, session }: AppLayoutProps = {}) {
  const [selectedAgent, setSelectedAgent] = useState<AgentType>('Researcher');
  const [messages, setMessages] = useState<{ role: 'user' | 'assistant'; content: string }[]>([
    { role: 'assistant', content: '你好！我是您的AI助手。今天我可以如何帮助您？' }
  ]);

  const pathname = usePathname();

  // 使用 useMemo 稳定函数返回值，避免每次渲染都创建新的对象引用
  const agentInstructions = useMemo(() => {
    if (pathname.includes('haiku')) {
      return '你是一个专业的诗歌生成助手。你能够创作优美的中英双语诗歌，理解诗歌的传统格式（5-7-5音节），并能根据用户提供的主题或情感创作相应的诗歌作品。请始终用中文回复用户。';
    }
    if (pathname.includes('mario')) {
      return '你是Mairo测试框架，你能够自动生成EC测试用例和Thrift泛化测试用例。请始终用中文回复用户。';
    }
    return '你是一个有用的AI助手。请始终用中文回复用户。';
  }, [pathname]);

  // 使用 useMemo 稳定建议词数组
  const suggestions = useMemo(() => {
    if (pathname.includes('haiku')) {
      return ['为我创作一首关于春天的诗歌', '写一首关于夜晚的诗歌', '创作一首关于思念的诗歌'];
    }
    if (pathname.includes('mario')) {
      return [
        '帮我生成Mario用例, EC用例Id：2182608',
        '通过Thrift泛化调用访问服务com.sankuai.web.cart.api.service.CartService、AppKey为com.sankuai.cart.service的queryCartItemCount方法，mtUserId作为请求参数，commonResponse作为响应结果生成用例'
      ];
    }
    return [];
  }, [pathname]);

  // 判断是否应该启用 CopilotKit
  const isEnabled = useMemo(() => {
    return pathname.includes('haiku') || pathname.includes('mario');
  }, [pathname]);

  useCopilotChatSuggestions({
    instructions: agentInstructions,
    minSuggestions: Math.max(1, suggestions.length), // 确保至少为1
    maxSuggestions: Math.max(3, suggestions.length), // 设置最大建议数
    available: isEnabled ? 'enabled' : 'disabled'
  });

  return (
    <SidebarProvider defaultOpen={false}>
      <div className='bg-background flex h-screen w-full overflow-hidden'>
        <AppSidebar />
        <Workspace
          selectedAgent={selectedAgent}
          lastMessage={messages.filter((m) => m.role === 'assistant').pop()?.content || ''}
        />
      </div>
    </SidebarProvider>
  );
}
