'use client';

import { useState, useEffect } from 'react';
import { getIntegration } from '@workspace/agent-registry/client';
import type { AgentType } from '@/lib/types';
import { ChatSidebar } from '@/components/layout/chat-sidebar';
import { useAgent } from '@/components/providers/agent-provider';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@workspace/ui/components/resizable';
import { Button } from '@workspace/ui/components/button';
import { useSidebar } from '@workspace/ui/components/sidebar';
import { PanelRightClose, PanelRightOpen, PanelLeftClose, PanelLeftOpen } from 'lucide-react';

interface WorkspaceProps {
  selectedAgent: AgentType;
  lastMessage: string;
}

/**
 * 自定义侧边栏触发器组件
 * 用于控制AppSidebar的展开/折叠状态
 */
function CustomSidebarTrigger() {
  const { toggleSidebar, state } = useSidebar();

  return (
    <Button
      variant='outline'
      size='sm'
      onClick={toggleSidebar}
      className='h-8 w-8 border-gray-300 p-0 transition-colors hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-800'
      title={state === 'expanded' ? '折叠侧边栏' : '展开侧边栏'}>
      {state === 'collapsed' ? <PanelLeftOpen className='h-4 w-4' /> : <PanelLeftClose className='h-4 w-4' />}
      <span className='sr-only'>Toggle Sidebar</span>
    </Button>
  );
}
/**
 * 通用 Workspace 组件
 * 通过 agent 注册中心动态加载对应的 workspace 组件
 */
export function Workspace({ selectedAgent, lastMessage }: WorkspaceProps) {
  const { currentAgent } = useAgent();
  const [isAgentActive, setIsAgentActive] = useState(false);
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(true);
  const [agentPackage, setAgentPackage] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAgent = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!currentAgent) {
          setAgentPackage(null);
          setLoading(false);
          return;
        }

        // 从注册中心获取 agent 包 (转换 ID 格式)
        let agentId = currentAgent.id;
        if (agentId.endsWith('Agent')) {
          agentId = agentId.replace('Agent', '').toLowerCase();
        }
        const pkg = await getIntegration(agentId);
        setAgentPackage(pkg);
      } catch (err) {
        console.error('Failed to load agent:', err);
        setError('Failed to load agent');
      } finally {
        setLoading(false);
      }
    };
    loadAgent();
  }, [currentAgent]);

  /**
   * 渲染 agent 对应的 workspace 组件
   * 每个 agent 自己管理其特定的状态和逻辑
   */
  const renderWorkspace = () => {
    if (loading) {
      return (
        <div className='flex h-full items-center justify-center'>
          <div className='space-y-2 text-center'>
            <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900'></div>
            <p className='text-gray-600'>Loading agent...</p>
          </div>
        </div>
      );
    }

    // 检查 currentAgent 是否存在
    if (!currentAgent) {
      return (
        <div className='flex h-full items-center justify-center'>
          <div className='space-y-2 text-center'>
            <p className='text-gray-500'>No agent selected</p>
            <p className='text-sm text-gray-400'>Please select an agent to continue</p>
          </div>
        </div>
      );
    }

    if (error || !agentPackage) {
      return (
        <div className='flex h-full items-center justify-center'>
          <div className='space-y-2 text-center'>
            <p className='text-gray-500'>{error || `No workspace available for agent: ${currentAgent.id}`}</p>
            <p className='text-sm text-gray-400'>Please check if the agent is properly registered</p>
          </div>
        </div>
      );
    }

    if (agentPackage && agentPackage.package?.workspace) {
      const WorkspaceComponent = agentPackage.package.workspace;

      // 传递通用的基础 props，让每个 workspace 组件自己管理特定状态
      return (
        <WorkspaceComponent
          isAgentActive={isAgentActive}
          setIsAgentActive={setIsAgentActive}
          lastMessage={lastMessage}
        />
      );
    }

    // 如果没有找到对应的 workspace，显示默认内容
    return (
      <div className='flex h-full items-center justify-center'>
        <div className='space-y-2 text-center'>
          <p className='text-gray-500'>No workspace available for agent: {currentAgent.id}</p>
          <p className='text-sm text-gray-400'>Please check if the agent is properly registered</p>
        </div>
      </div>
    );
  };

  return (
    <main className='flex h-screen flex-1 flex-col overflow-hidden'>
      <div className='h-screen flex-1 overflow-auto'>
        <ResizablePanelGroup
          direction='horizontal'
          className='h-full'
          key={isRightPanelOpen ? 'two-panels' : 'one-panel'}>
          {/* Left Panel - Chat UI Sidebar */}
          <ResizablePanel
            id='chat-panel'
            defaultSize={isRightPanelOpen ? 30 : 100}
            minSize={20}
            maxSize={isRightPanelOpen ? 50 : 100}>
            <div className='flex h-full flex-col bg-white'>
              {/* Chat Header */}
              <div className='h-12 w-full flex-shrink-0 border-b border-gray-200 dark:border-gray-700'>
                <div className='flex h-full items-center justify-between px-6'>
                  <div className='flex items-center gap-2'>
                    <CustomSidebarTrigger />
                  </div>
                  <div className='flex items-center gap-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => setIsRightPanelOpen(!isRightPanelOpen)}
                      className='h-8 w-8 border-gray-300 p-0 transition-colors hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-800'
                      title={isRightPanelOpen ? '关闭工作区' : '打开工作区'}>
                      {isRightPanelOpen ? (
                        <PanelRightClose className='h-4 w-4' />
                      ) : (
                        <PanelRightOpen className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
              {/* Chat Content */}
              <div className='flex-1 overflow-auto'>
                <ChatSidebar />
              </div>
            </div>
          </ResizablePanel>

          {isRightPanelOpen && <ResizableHandle withHandle />}

          {/* Right Panel - Main Workspace */}
          {isRightPanelOpen && (
            <ResizablePanel id='workspace-panel' defaultSize={70} minSize={50} maxSize={80}>
              <div className='flex h-full flex-col'>{renderWorkspace()}</div>
            </ResizablePanel>
          )}
        </ResizablePanelGroup>
      </div>
    </main>
  );
}
