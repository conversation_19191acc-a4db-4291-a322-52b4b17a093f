'use client';

import type React from 'react';

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Send, CornerDownLeft, Plus, ArrowUp, Wifi, WifiOff, TestTube } from 'lucide-react';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Textarea } from '@workspace/ui/components/textarea';
import { Badge } from '@workspace/ui/components/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
  SelectGroup
} from '@workspace/ui/components/select';
import { EnhancedAgentSelector } from '@/components/enhanced-agent-selector';
import type { AgentType } from '@/lib/types';
import { CopilotChat } from '@copilotkit/react-ui';
import { useCopilotChat, useCopilotAction } from '@copilotkit/react-core';
import type { ReactNode } from 'react';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import { getIntegration } from '@workspace/agent-registry/client';
import { useAgent } from '@/components/providers/agent-provider';
import { getGroupedModelOptions, DEFAULT_MODEL_ID } from '@/config/models-config';
import { useParams } from 'next/navigation';
import { CodeBlock, InlineCode } from '@/components/code-block';
import { useUserAuth } from '@/hooks/use-user-auth';
import { useAgentWebSocket } from '@/hooks/use-agent-websocket';
import { getAgentConfig, getAllAgentConfigs } from '@/lib/agent-config';
import type { AgentMessage, ChatInput, WebSocketConnectionState } from '@/lib/types/chat';

interface ChatSidebarProps {
  className?: string;
}

interface ChatInputProps {
  onSend: (message: string) => void;
  inProgress: boolean;
  setIsAgentActive: (active: boolean) => void;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
}

/**
 * 聊天输入组件
 */
function ChatInput({ onSend, inProgress, setIsAgentActive, selectedModel, setSelectedModel }: ChatInputProps) {
  const [input, setInput] = useState('');

  // 使用 useMemo 缓存 groupedModels，避免每次渲染都重新计算
  const groupedModels = useMemo(() => getGroupedModelOptions(), []);

  useEffect(() => {
    if (inProgress) {
      setIsAgentActive(true);
    } else {
      setIsAgentActive(false);
    }
  }, [inProgress, setIsAgentActive]);

  const handleSend = useCallback(
    (e?: React.MouseEvent | React.KeyboardEvent) => {
      console.log('sending message');
      e?.preventDefault();
      if (input.trim()) {
        onSend(input);
        setInput('');
        setIsAgentActive(true);
      }
    },
    [input, onSend, setIsAgentActive]
  );

  /**
   * 处理键盘事件：回车发送，Shift+回车换行
   */
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === 'Enter') {
        if (e.shiftKey) {
          // Shift + Enter: 换行，使用默认行为
          return;
        } else {
          // Enter: 发送消息
          e.preventDefault();
          handleSend(e);
        }
      }
    },
    [handleSend]
  );

  return (
    <div className='space-y-5 px-4 py-2'>
      <form className='flex flex-col gap-3'>
        <div className='relative'>
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder='输入信息...'
            className='border-muted-foreground/20 max-h-[120px] min-h-[120px] resize-none rounded-xl p-3 pb-14 pr-3'
            rows={4}
          />

          {/* 底部控制栏 - 使用flex布局确保垂直居中对齐 */}
          <div className='absolute bottom-2 left-3 right-2 flex items-center gap-2'>
            {/* 模型选择器 */}
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className='h-8 w-auto min-w-[120px] rounded-md border-0 bg-gray-100 px-3 text-xs shadow-none focus:ring-0'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {groupedModels.map((group) => (
                  <SelectGroup key={group.category}>
                    <SelectLabel>{group.categoryName}</SelectLabel>
                    {group.models.map((model) => (
                      <SelectItem key={model.value} value={model.value} className='text-xs'>
                        <div className='flex items-center gap-2'>
                          {model.iconPath && <img src={model.iconPath} alt={model.label} className='h-4 w-4 rounded' />}
                          <span>{model.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectGroup>
                ))}
              </SelectContent>
            </Select>

            {/* Agent选择器 - 添加一个包装器以匹配Select组件的样式 */}
            <div className='relative inline-flex h-9 w-auto min-w-[80px] items-center rounded-md bg-gray-100'>
              <EnhancedAgentSelector
                selectedAgentId={'mario'}
                onAgentChange={() => {}}
                showConnectionStatus={true}
                connectionStates={{}}
                className='w-full'
              />
            </div>

            {/* 占位空间，推动快捷键提示到右侧 */}
            <div className='flex-1' />

            {/* 键盘快捷键提示 */}
            <div className='text-muted-foreground mr-12 flex items-center gap-3 text-xs'>
              <div className='flex items-center gap-1'>
                <CornerDownLeft className='h-4 w-4 stroke-[3]' />
                <span>发送</span>
              </div>
              <div className='flex items-center gap-1'>
                <div className='flex items-center gap-0.5'>
                  <ArrowUp className='h-4 w-4 stroke-[3]' />
                  <Plus className='h-3 w-3' />
                  <CornerDownLeft className='h-4 w-4 stroke-[3]' />
                </div>
                <span>换行</span>
              </div>
            </div>
          </div>

          <Button
            disabled={inProgress}
            onClick={handleSend}
            className='absolute bottom-2 right-2 h-9 w-9 rounded-lg p-0'
            size='sm'>
            <Send className='h-4 w-4' />
          </Button>
        </div>
      </form>
    </div>
  );
}

/**
 * 异步获取 agent 包和初始消息的 hook
 * @param agentId - agent ID
 * @returns agent 包和初始消息
 */
function useAgentPackage(agentId: string) {
  const [agentPackage, setAgentPackage] = useState<any>(null);
  const [initialMessage, setInitialMessage] = useState('Hello! How can I help you today!');

  useEffect(() => {
    const loadAgent = async () => {
      try {
        const pkg = await getIntegration(agentId);
        setAgentPackage(pkg);
        setInitialMessage(pkg?.package?.defaultConfig?.systemPrompt || 'Hello! How can I help you today!');
      } catch (error) {
        console.error('Failed to load agent:', error);
      }
    };
    loadAgent();
  }, [agentId]);

  return { agentPackage, initialMessage };
}

export function ChatSidebar({ className }: ChatSidebarProps) {
  const userAuth = useUserAuth();
  const agentContext = useAgent();
  const [selectedModel, setSelectedModel] = useState<string>(DEFAULT_MODEL_ID);
  const [selectedAgentId, setSelectedAgentId] = useState<string>('mario');
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const params = useParams();
  const canvasId = params?.canvasId as string;

  // Agent WebSocket 连接
  const agentConfig = getAgentConfig(selectedAgentId);
  const agentWebSocket = useAgentWebSocket({
    agentConfig: agentConfig!,
    autoConnect: true
  });
  
  const {
    messages: agentMessages,
    sendMessage: sendAgentMessage,
    clearMessages: clearAgentMessages,
    connect: connectAgent,
    disconnect: disconnectAgent
  } = agentWebSocket;
  
  // 模拟连接状态和其他属性
  const connectionState = 'connected' as const;
  const loadingState = { isLoading: false, message: '' };
  const agentError = null;

  const {
    visibleMessages,
    appendMessage,
    setMessages,
    isLoading: isCopilotLoading,
    deleteMessage
  } = useCopilotChat();
  const [savedMessageIds, setSavedMessageIds] = useState<Set<string>>(new Set());
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastSavedContent, setLastSavedContent] = useState<Map<string, string>>(new Map());

  // 获取所有 Agent 配置
  const allAgentConfigs = getAllAgentConfigs();
  
  // 连接状态映射
  const connectionStates = useMemo(() => {
    const states: Record<string, boolean> = {};
    allAgentConfigs.forEach(config => {
      states[config.id] = config.id === selectedAgentId ? 
        connectionState === 'connected' : false;
    });
    return states;
  }, [allAgentConfigs, selectedAgentId, connectionState]);

  // 处理 Agent 切换
  const handleAgentChange = useCallback((agentId: string) => {
    if (agentId !== selectedAgentId) {
      // 断开当前连接
      disconnectAgent();
      // 切换到新 Agent
      setSelectedAgentId(agentId);
      // 清空消息
      clearAgentMessages();
    }
  }, [selectedAgentId, disconnectAgent, clearAgentMessages]);

  // 使用 useCallback 稳定 setIsAgentActive 函数引用
  const stableSetIsAgentActive = useCallback(
    (active: boolean) => {
      // setIsAgentActive(active);
    },
    []
  );

  // 获取当前集成配置
  const integration = useMemo(() => {
    if (!selectedAgentId) return null;
    return getIntegration(selectedAgentId);
  }, [selectedAgentId]);

  // 获取分组的模型选项
  const groupedModelOptions = useMemo(() => getGroupedModelOptions(), []);
  
  // 检查用户登录状态
  const { isLoggedIn } = useUserAuth();

  // 初始化时，将所有现有消息的 ID 和内容标记为已保存
  useEffect(() => {
    if (!isInitialized && visibleMessages.length > 0) {
      const initialSavedIds = new Set<string>();
      const initialSavedContent = new Map<string, string>();
      
      visibleMessages.forEach((message) => {
        if (message.id) {
          initialSavedIds.add(message.id);
          const messageContent = typeof message.text === 'string' ? message.text : JSON.stringify(message.text);
          initialSavedContent.set(message.id, messageContent);
        }
      });
      
      setSavedMessageIds(initialSavedIds);
      setLastSavedContent(initialSavedContent);
      setIsInitialized(true);
    }
  }, [visibleMessages, isInitialized]);

  /**
   * 自定义Markdown渲染器，优化显示效果
   * 使用正确的HTML语义元素，避免样式和可访问性问题
   */
  const customMarkdownRenderers = useMemo(
    () => ({
      p: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <p className='copilotKitMarkdownElement mb-3 leading-relaxed text-gray-800' {...props}>
          {children}
        </p>
      ),
      code: ({ children, className, ...props }: { children?: ReactNode; className?: string; [key: string]: any }) => {
        // 处理代码块（在pre标签内的代码）
        if (className?.includes('language-')) {
          const code = typeof children === 'string' ? children : String(children);
          return (
            <CodeBlock className={className} language={className?.replace('language-', '')}>
              {code}
            </CodeBlock>
          );
        }
        // 内联代码
        return (
          <InlineCode className={className} {...props}>
            {children}
          </InlineCode>
        );
      },
      pre: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => {
        // 如果pre标签包含code子元素，直接返回children（让code组件处理）
        return <div {...props}>{children}</div>;
      },
      h1: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <h1 className='copilotKitHeading mb-4 mt-6 text-2xl font-bold text-gray-900' {...props}>
          {children}
        </h1>
      ),
      h2: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <h2 className='copilotKitHeading mb-3 mt-5 text-xl font-semibold text-gray-900' {...props}>
          {children}
        </h2>
      ),
      h3: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <h3 className='copilotKitHeading mb-2 mt-4 text-lg font-medium text-gray-900' {...props}>
          {children}
        </h3>
      ),
      ul: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <ul className='copilotKitList mb-3 ml-4 list-disc space-y-1' {...props}>
          {children}
        </ul>
      ),
      ol: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <ol className='copilotKitList mb-3 ml-4 list-decimal space-y-1' {...props}>
          {children}
        </ol>
      ),
      li: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <li className='copilotKitListItem text-gray-800' {...props}>
          {children}
        </li>
      ),
      blockquote: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <blockquote
          className='copilotKitBlockquote my-4 border-l-4 border-blue-500 bg-blue-50 p-4 italic text-gray-700'
          {...props}>
          {children}
        </blockquote>
      ),
      strong: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <strong className='copilotKitStrong font-semibold text-gray-900' {...props}>
          {children}
        </strong>
      ),
      em: ({ children, ...props }: { children?: ReactNode; [key: string]: any }) => (
        <em className='copilotKitEmphasis italic text-gray-800' {...props}>
          {children}
        </em>
      )
    }),
    []
  );

  // 保存消息到数据库的函数
  const saveMessageToDatabase = useCallback(
    async (content: string, role: 'user' | 'assistant', messageId?: string) => {
      try {
        const response = await fetch('/api/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            content,
            role,
            canvasId,
            agentId: selectedAgentId,
            messageId
          })
        });

        if (!response.ok) {
          throw new Error('Failed to save message');
        }

        const result = await response.json();
        console.log('Message saved successfully:', result);
      } catch (error) {
        console.error('Error saving message:', error);
      }
    },
    [canvasId, selectedAgentId]
  );

  // 监听消息变化并保存新消息
      useEffect(() => {
        if (!isInitialized) return;
    
        visibleMessages.forEach((message) => {
          if (!message.id) return;
    
          const wasAlreadySaved = savedMessageIds.has(message.id);
          const messageContent = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
          const contentChanged = lastSavedContent.get(message.id) !== messageContent;
    
          if (!wasAlreadySaved || contentChanged) {
            // 保存消息到数据库
            saveMessageToDatabase(messageContent, message.role as 'user' | 'assistant', message.id);
            
            // 更新本地状态
            setSavedMessageIds(prev => new Set([...prev, message.id!]));
            setLastSavedContent(prev => new Map([...prev, [message.id!, messageContent]]));
          }
        });
      }, [visibleMessages, isInitialized, savedMessageIds, lastSavedContent, saveMessageToDatabase]);

  // 发送消息的处理函数
  const handleSendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim()) return;

      try {
        setIsLoading(true);
        
        // 根据当前选择的 Agent 类型决定使用哪种消息发送方式
        if (agentConfig && connectionState === 'connected') {
          // 使用 Agent WebSocket 发送消息
          const chatInput: ChatInput = {
            agentId: selectedAgentId,
            message: messageContent,
            model: selectedModel,
            metadata: {
              canvasId,
              userId: userAuth?.user?.id,
              timestamp: new Date().toISOString()
            }
          };
          await sendAgentMessage(chatInput);
        } else {
          // 使用 CopilotKit 发送消息
          await appendMessage(new TextMessage({
            content: messageContent,
            role: Role.User
          }));
        }
        
        // 清空输入
        setInput('');
      } catch (error) {
        console.error('Failed to send message:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [agentConfig, connectionState, selectedModel, canvasId, userAuth?.user?.id, sendAgentMessage, appendMessage, input]
  );

  // 使用 useCallback 包装 Input 组件，避免每次渲染都创建新的组件
  const InputComponent = useCallback(
    ({ onSubmitMessage }: { onSubmitMessage: (message: string) => void }) => {
      const [inputValue, setInputValue] = useState('');
      const textareaRef = useRef<HTMLTextAreaElement>(null);

      const handleSubmit = useCallback(
        (e: React.FormEvent) => {
          e.preventDefault();
          if (inputValue.trim()) {
            onSubmitMessage(inputValue);
            setInputValue('');
          }
        },
        [inputValue, onSubmitMessage]
      );

      const handleKeyDown = useCallback(
        (e: React.KeyboardEvent) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e as any);
          }
        },
        [handleSubmit]
      );

      return (
        <form onSubmit={handleSubmit} className='flex gap-2 p-4 border-t'>
          <Textarea
            ref={textareaRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder='Type your message...'
            className='flex-1 min-h-[40px] max-h-[120px] resize-none'
            disabled={isCopilotLoading}
          />
          <Button
            type='submit'
            disabled={!inputValue.trim() || isCopilotLoading}
            size='sm'
          >
            {isCopilotLoading ? (
              <div className='h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600' />
            ) : (
              <Send className='h-4 w-4' />
            )}
          </Button>
        </form>
      );
    },
    [isCopilotLoading]
  );

  return (
    <div className={`flex h-full flex-col bg-white ${className}`}>
      {/* Header */}
      <div className='flex items-center justify-between border-b p-4'>
        <div className='flex items-center gap-3'>
          <h2 className='text-lg font-semibold'>Chat</h2>
          {agentConfig && (
            <div className='flex items-center gap-2'>
              {connectionState === 'connected' ? (
                <>
                  <div className='h-2 w-2 rounded-full bg-green-500' />
                  <span className='text-sm text-gray-600'>{agentConfig.name}</span>
                </>
              ) : (
                <>
                  <div className='h-2 w-2 rounded-full bg-gray-400' />
                  <span className='text-sm text-gray-500'>{agentConfig.name} (断开)</span>
                </>
              )}
            </div>
          )}
        </div>
        <div className='flex items-center gap-2'>
          {/* 清空消息按钮 */}
          <Button 
            variant='ghost' 
            size='sm'
            onClick={clearAgentMessages}
            disabled={agentMessages.length === 0}
          >
            清空
          </Button>
          <Button variant='ghost' size='sm'>
            <Plus className='h-4 w-4' />
          </Button>
        </div>
      </div>

      {/* Agent and Model Selection */}
      <div className='border-b p-4'>
        <div className='space-y-3'>
          {/* Agent Selection */}
          <div className='space-y-2'>
            <label className='text-sm font-medium'>Agent</label>
            <EnhancedAgentSelector
              selectedAgentId={'mario'}
              onAgentChange={() => {}}
              showConnectionStatus={true}
              connectionStates={{}}
              className='w-full'
            />
          </div>

          {/* Model Selection */}
           <div className='space-y-2'>
             <label className='text-sm font-medium'>Model</label>
             <Select value={selectedModel} onValueChange={setSelectedModel}>
               <SelectTrigger className='w-full'>
                 <SelectValue placeholder='Select a model' />
               </SelectTrigger>
               <SelectContent>
                 {Object.entries(groupedModelOptions).map(([provider, models]) => (
                   <SelectGroup key={provider}>
                     <SelectLabel>{provider}</SelectLabel>
                     {models.map((model) => (
                       <SelectItem key={model.id} value={model.id}>
                         <div className='flex items-center justify-between w-full'>
                           <span>{model.name}</span>
                           {model.contextLength && (
                             <span className='text-xs text-gray-500 ml-2'>
                               {model.contextLength.toLocaleString()} tokens
                             </span>
                           )}
                         </div>
                       </SelectItem>
                     ))}
                   </SelectGroup>
                 ))}
               </SelectContent>
             </Select>
           </div>
          
          {/* 连接状态和错误显示 */}
          {agentError && (
            <div className='rounded-md bg-red-50 p-3'>
              <div className='text-sm text-red-800'>
                连接错误: {agentError.message}
              </div>
            </div>
          )}
          
          {loadingState.isLoading && (
            <div className='flex items-center gap-2 text-sm text-gray-600'>
              <div className='h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600' />
              <span>{loadingState.message || '处理中...'}</span>
            </div>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className='flex-1 overflow-hidden'>
        {agentConfig && connectionState === 'connected' ? (
          /* Agent WebSocket 消息显示 */
          <div className='h-full flex flex-col'>
            <div className='flex-1 overflow-y-auto p-4 space-y-4'>
              {agentMessages.length === 0 ? (
                <div className='flex items-center justify-center h-full text-gray-500'>
                  <div className='text-center'>
                    <TestTube className='h-12 w-12 mx-auto mb-4 text-gray-400' />
                    <p className='text-lg font-medium mb-2'>开始与 {agentConfig.name} 对话</p>
                    <p className='text-sm'>发送消息开始聊天</p>
                  </div>
                </div>
              ) : (
                agentMessages.map((message, index) => (
                  <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] rounded-lg px-4 py-2 ${
                      message.role === 'user' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <div className='text-sm'>
                        {message.content}
                      </div>
                      {message.timestamp && (
                        <div className={`text-xs mt-1 ${
                          message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
            
            {/* Agent 输入区域 */}
            <div className='border-t p-4'>
              <div className='flex gap-2'>
                <Textarea
                  ref={textareaRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder={`向 ${agentConfig.name} 发送消息...`}
                  className='flex-1 min-h-[40px] max-h-[120px] resize-none'
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(input);
                    }
                  }}
                  disabled={isLoading || connectionState !== 'connected'}
                />
                <Button
                  onClick={() => handleSendMessage(input)}
                  disabled={!input.trim() || isLoading || connectionState !== 'connected'}
                  size='sm'
                >
                  {isLoading ? (
                    <div className='h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600' />
                  ) : (
                    <Send className='h-4 w-4' />
                  )}
                </Button>
              </div>
            </div>
          </div>
        ) : (
          /* CopilotKit 消息显示 */
          <CopilotChat
            className='h-full'
            markdownTagRenderers={customMarkdownRenderers}
            onSubmitMessage={async (message: string) => {
              // 检查用户是否已登录
              if (!isLoggedIn) {
                console.error('用户未登录，无法发送消息');
                return;
              }
              // 用户发送消息时保存
              await saveMessageToDatabase(message, 'user');
            }}
            Input={InputComponent}
          />
        )}
      </div>
    </div>
  );
}
