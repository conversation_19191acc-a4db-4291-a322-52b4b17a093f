import React from 'react';
import { FeatureConfig } from '@/types/feature';
import { cn } from '@workspace/ui/lib/utils';
import { Badge } from '@workspace/ui/components/badge';

interface DemoListProps {
  demos: FeatureConfig[];
  selectedDemo?: string;
  onSelect: (demoId: string) => void;
  llmSelector?: React.ReactNode;
}

export function DemoList({ demos, selectedDemo, onSelect, llmSelector }: DemoListProps) {
  return (
    <div className='h-full'>
      <div className='px-4 pb-2 pt-3'>
        <h2 className='text-muted-foreground mb-1 text-xs font-semibold uppercase tracking-wider'>Demos</h2>
        {llmSelector && <div className='mt-2'>{llmSelector}</div>}
      </div>
      <ul className='space-y-0.5 px-2'>
        {demos.map((demo) => (
          <li key={demo.id}>
            <button
              className={cn(
                'hover:bg-accent/50 w-full rounded-md px-3 py-2 text-left transition-colors',
                'flex flex-col gap-0.5',
                selectedDemo === demo.id && 'bg-accent'
              )}
              onClick={() => onSelect(demo.id)}>
              <div className='text-sm font-medium leading-tight'>{demo.name}</div>
              <div className='text-muted-foreground line-clamp-2 text-xs leading-relaxed'>{demo.description}</div>
              {demo.tags && demo.tags.length > 0 && (
                <div className='mt-0.5 flex flex-wrap gap-1'>
                  {demo.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedDemo === demo.id ? 'default' : 'secondary'}
                      className={cn(
                        'rounded-full px-1.5 py-0.5 text-xs',
                        selectedDemo === demo.id && 'bg-primary text-primary-foreground border-transparent'
                      )}>
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}
