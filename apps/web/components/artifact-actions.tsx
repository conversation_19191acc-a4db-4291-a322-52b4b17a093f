import { artifactDefinitions, type UIArtifact } from './artifact';
import { memo, type Dispatch, type SetStateAction } from 'react';

interface ArtifactActionsProps {
    artifact: UIArtifact;
    handleVersionChange: (type: 'next' | 'prev' | 'toggle' | 'latest') => void;
    currentVersionIndex: number;
    isCurrentVersion: boolean;
    mode: 'edit' | 'diff';
    metadata: any;
    setMetadata: Dispatch<SetStateAction<any>>;
}

function PureArtifactActions({ artifact }: ArtifactActionsProps) {
    const artifactDefinition = artifactDefinitions.find((definition) => definition.kind === artifact.kind);

    if (!artifactDefinition) {
        throw new Error('Artifact definition not found!');
    }

    return <div className='flex flex-row gap-2'></div>;
}

export const ArtifactActions = memo(PureArtifactActions, (prevProps, nextProps) => {
    if (prevProps.artifact.status !== nextProps.artifact.status) return false;
    if (prevProps.currentVersionIndex !== nextProps.currentVersionIndex) return false;
    if (prevProps.isCurrentVersion !== nextProps.isCurrentVersion) return false;
    if (prevProps.artifact.content !== nextProps.artifact.content) return false;

    return true;
});
