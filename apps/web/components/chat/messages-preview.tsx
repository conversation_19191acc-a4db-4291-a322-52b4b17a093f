'use client';

import React from 'react';
import { ScrollArea } from '@workspace/ui/components/scroll-area';
import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import { X, MessageSquare, Code, FileText, User, Bot } from 'lucide-react';
import { useCopilotChat } from '@copilotkit/react-core';
import { cn } from '@workspace/ui/lib/utils';

/**
 * 消息预览组件属性接口
 */
export interface MessagesPreviewProps {
  onClose: () => void;
  previewMessage?: string; // 可选的特定预览消息
}

/**
 * 提取消息中的代码块
 * @param content 消息内容
 * @returns 代码块数组
 */
function extractCodeBlocks(content: string): Array<{ language: string; code: string }> {
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  const codeBlocks: Array<{ language: string; code: string }> = [];
  let match;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    codeBlocks.push({
      language: match[1] || 'text',
      code: match[2].trim()
    });
  }

  return codeBlocks;
}

/**
 * 消息预览组件
 * 显示聊天中的消息内容，特别突出显示代码类内容
 * 如果提供了previewMessage，则只显示该消息的预览
 */
export function MessagesPreview({ onClose, previewMessage }: MessagesPreviewProps) {
  // 尝试获取CopilotChat上下文，如果不存在则使用空数组
  let visibleMessages: any[] = [];
  try {
    const copilotChat = useCopilotChat();
    visibleMessages = copilotChat.visibleMessages || [];
  } catch (error) {
    // 如果不在CopilotKit上下文中，使用空数组
    visibleMessages = [];
  }

  // 如果有特定的预览消息，则使用它；否则使用所有消息
  const messagesToShow = previewMessage 
    ? [{ role: 'assistant', content: previewMessage }] 
    : visibleMessages;

  // 过滤出包含代码的消息
  const messagesWithCode = messagesToShow.filter((message) => {
    if (typeof message.content === 'string') {
      return message.content.includes('```');
    }
    return false;
  });

  // 统计信息
  const totalMessages = messagesToShow.length;
  const codeMessages = messagesWithCode.length;
  const totalCodeBlocks = messagesWithCode.reduce((total, message) => {
    if (typeof message.content === 'string') {
      return total + extractCodeBlocks(message.content).length;
    }
    return total;
  }, 0);

  return (
    <div className='flex h-full w-full flex-col bg-white dark:bg-gray-900'>
      {/* 头部 */}
      <div className='flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700'>
        <div className='flex items-center gap-3'>
          <MessageSquare className='h-5 w-5 text-blue-500' />
          <h2 className='text-lg font-semibold text-gray-900 dark:text-gray-100'>消息预览</h2>
        </div>
        <Button variant='ghost' size='sm' onClick={onClose} className='h-8 w-8 p-0' title='关闭预览'>
          <X className='h-4 w-4' />
        </Button>
      </div>

      {/* 统计信息 */}
      <div className='flex flex-wrap gap-2 border-b border-gray-200 p-4 dark:border-gray-700'>
        <Badge variant='secondary' className='gap-1'>
          <MessageSquare className='h-3 w-3' />
          总消息: {totalMessages}
        </Badge>
        <Badge variant='secondary' className='gap-1'>
          <Code className='h-3 w-3' />
          代码消息: {codeMessages}
        </Badge>
        <Badge variant='secondary' className='gap-1'>
          <Code className='h-3 w-3' />
          代码块: {totalCodeBlocks}
        </Badge>
      </div>

      {/* 消息列表 */}
      <ScrollArea className='flex-1 p-4'>
        {messagesToShow.length === 0 ? (
          <div className='flex h-full items-center justify-center text-center'>
            <div className='text-gray-500 dark:text-gray-400'>
              <MessageSquare className='mx-auto h-12 w-12 mb-4 opacity-50' />
              <p className='text-sm'>暂无消息</p>
              <p className='mt-1 text-xs'>开始对话后消息将在这里显示</p>
            </div>
          </div>
        ) : (
          <div className='space-y-4'>
            {messagesToShow.map((message, index) => {
              const isUser = message.role === 'user';
              const isAssistant = message.role === 'assistant';
              const messageContent = typeof message.content === 'string' ? message.content : '';
              const codeBlocks = extractCodeBlocks(messageContent);
              const hasCode = codeBlocks.length > 0;

              return (
                <div
                  key={index}
                  className={cn(
                    'rounded-lg border p-4',
                    hasCode
                      ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20'
                      : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50'
                  )}>
                  {/* 消息头部 */}
                  <div className='mb-3 flex items-center gap-2'>
                    {isUser ? (
                      <User className='h-4 w-4 text-green-500' />
                    ) : isAssistant ? (
                      <Bot className='h-4 w-4 text-blue-500' />
                    ) : (
                      <MessageSquare className='h-4 w-4 text-gray-500' />
                    )}
                    <span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                      {isUser ? '用户' : isAssistant ? 'Assistant' : message.role}
                    </span>
                    {hasCode && (
                      <Badge variant='outline' size='sm' className='gap-1'>
                        <Code className='h-3 w-3' />
                        {codeBlocks.length} 个代码块
                      </Badge>
                    )}
                  </div>

                  {/* 消息内容 */}
                  <div className='space-y-3'>
                    {/* 文本内容预览 */}
                    {messageContent && (
                      <div className='text-sm text-gray-600 dark:text-gray-400'>
                        {messageContent.length > 200
                          ? `${messageContent.substring(0, 200)}...`
                          : messageContent}
                      </div>
                    )}

                    {/* 代码块展示 */}
                    {codeBlocks.map((block, blockIndex) => (
                      <div
                        key={blockIndex}
                        className='rounded border border-gray-300 bg-gray-100 dark:border-gray-600 dark:bg-gray-800'>
                        <div className='flex items-center justify-between border-b border-gray-300 px-3 py-2 dark:border-gray-600'>
                          <div className='flex items-center gap-2'>
                            <Code className='h-3 w-3 text-gray-500' />
                            <span className='text-xs font-medium text-gray-600 dark:text-gray-400'>
                              {block.language}
                            </span>
                          </div>
                          <Badge variant='secondary' size='sm'>
                            {block.code.split('\n').length} 行
                          </Badge>
                        </div>
                        <pre className='overflow-x-auto p-3 text-xs'>
                          <code className='text-gray-800 dark:text-gray-200'>
                            {block.code.length > 500
                              ? `${block.code.substring(0, 500)}...\n\n[代码已截断，完整内容请查看原消息]`
                              : block.code}
                          </code>
                        </pre>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}