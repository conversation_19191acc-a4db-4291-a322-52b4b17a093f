'use client';

import React, { useState, createContext, useContext } from 'react';
import { Markdown, UserMessageProps, AssistantMessageProps, useChatContext } from '@copilotkit/react-ui';
import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { Button } from '@workspace/ui/components/button';
import { <PERSON><PERSON><PERSON>, ScanEye } from 'lucide-react';
import { useUser } from '@/components/providers/user-provider';

/**
 * Canvas预览上下文接口
 */
interface CanvasPreviewContextType {
  showPreviewInCanvas: (message: string) => void;
  hidePreviewInCanvas: () => void;
}

/**
 * Canvas预览上下文
 */
const CanvasPreviewContext = createContext<CanvasPreviewContextType | null>(null);

/**
 * 使用Canvas预览上下文的Hook
 */
export const useCanvasPreview = () => {
  const context = useContext(CanvasPreviewContext);
  if (!context) {
    throw new Error('useCanvasPreview must be used within a CanvasPreviewProvider');
  }
  return context;
};

/**
 * Canvas预览提供者组件
 */
export const CanvasPreviewProvider: React.FC<{
  children: React.ReactNode;
  onShowPreview: (message: string) => void;
  onHidePreview: () => void;
}> = ({ children, onShowPreview, onHidePreview }) => {
  const value = {
    showPreviewInCanvas: onShowPreview,
    hidePreviewInCanvas: onHidePreview
  };

  return <CanvasPreviewContext.Provider value={value}>{children}</CanvasPreviewContext.Provider>;
};

/**
 * 消息组件的通用属性接口
 */
export interface MessageComponentProps {
  className?: string;
}

/**
 * 扩展的用户消息属性
 */
interface ExtendedUserMessageProps extends UserMessageProps {
  userAvatar?: string;
  message: string;
}

/**
 * 扩展的助手消息属性
 */
interface ExtendedAssistantMessageProps extends Omit<AssistantMessageProps, 'isLoading'> {
  assistantAvatar?: string;
  modelId?: string;
  message?: string;
  isLoading?: boolean;
  subComponent?: React.ReactElement;
}

/**
 * 自定义用户消息组件
 * 显示用户发送的消息，包含用户头像
 */
export const CustomUserMessage = (props: ExtendedUserMessageProps) => {
  const { userData } = useUser();
  const wrapperStyles = 'flex items-start gap-3 justify-end mb-4';
  const messageStyles = 'bg-blue-500 text-white py-3 px-4 break-words flex-shrink-0 max-w-[80%] shadow-sm';
  const avatarStyles = 'flex-shrink-0';

  return (
    <div className={wrapperStyles}>
      <div className={messageStyles}>{props.message}</div>
      <div className={avatarStyles}>
        <Avatar className='h-8 w-8'>
          <AvatarImage src={props.userAvatar || userData.avatar} alt={userData.displayName} />
          <AvatarFallback className='bg-blue-500 text-sm text-white'>
            {userData.displayName?.charAt(0)?.toUpperCase() || 'U'}
          </AvatarFallback>
        </Avatar>
      </div>
    </div>
  );
};

/**
 * 根据模型ID获取对应的头像
 */
const getModelAvatar = (modelId?: string): string => {
  const modelAvatars: Record<string, string> = {
    'gpt-4o': '/avatars/gpt-4o.svg',
    'gpt-4o-mini': '/avatars/gpt-4o-mini.svg',
    'claude-3-5-sonnet-20241022': '/avatars/claude-sonnet.svg',
    'claude-3-5-haiku-20241022': '/avatars/claude-haiku.svg',
    'chat-model-reasoning': '/avatars/reasoning.svg',
    default: '/avatars/ai-default.svg'
  };
  const key = modelId || 'default';
  return modelAvatars[key] || modelAvatars['default'];
};

/**
 * 根据模型ID获取对应的名称
 */
const getModelName = (modelId?: string): string => {
  const modelNames: Record<string, string> = {
    'gpt-4o': 'GPT-4o',
    'gpt-4o-mini': 'GPT-4o Mini',
    'claude-3-5-sonnet-20241022': 'Claude 3.5 Sonnet',
    'claude-3-5-haiku-20241022': 'Claude 3.5 Haiku',
    'chat-model-reasoning': 'Reasoning Model',
    default: 'AI Assistant'
  };
  const key = modelId || 'default';
  return modelNames[key] || modelNames['default'];
};

/**
 * Python代码块操作按钮组件
 * @param code - Python代码
 * @param index - 代码块索引
 * @param message - 完整消息内容
 */
/**
 * 头像后预览操作组件
 * 提供预览和自动预览功能，位于AI头像后面
 */
const AvatarPreviewActions: React.FC<{ message: string }> = ({ message }) => {
  const canvasPreview = useCanvasPreview();
  const [autoPreview, setAutoPreview] = useState(false);

  /**
   * 处理手动预览消息
   */
  const handlePreview = () => {
    canvasPreview.showPreviewInCanvas(message);
  };

  /**
   * 处理自动预览切换
   */
  const handleAutoPreviewToggle = () => {
    const newAutoPreview = !autoPreview;
    setAutoPreview(newAutoPreview);

    if (newAutoPreview) {
      // 开启自动预览时立即预览
      canvasPreview.showPreviewInCanvas(message);
    }
  };

  // 当自动预览开启且消息内容变化时，自动更新预览
  React.useEffect(() => {
    if (autoPreview && message) {
      canvasPreview.showPreviewInCanvas(message);
    }
  }, [message, autoPreview, canvasPreview]);

  return (
    <div className='ml-2 flex items-center gap-1.5'>
      <Button
        variant='ghost'
        size='sm'
        onClick={handlePreview}
        className='h-7 w-7 rounded-full p-0 text-xs transition-all duration-200 hover:scale-105 hover:bg-blue-50 dark:hover:bg-blue-900/20'
        title='预览'>
        <ScanEye className='h-3.5 w-3.5 text-gray-500 dark:text-gray-400' />
      </Button>
    </div>
  );
};

/**
 * 消息预览操作组件（保留原有功能）
 * 提供消息预览功能
 */
const MessagePreviewActions: React.FC<{ message: string }> = ({ message }) => {
  const canvasPreview = useCanvasPreview();

  /**
   * 处理预览消息
   */
  const handlePreview = () => {
    canvasPreview.showPreviewInCanvas(message);
  };

  return (
    <div className='flex items-center gap-1 rounded-lg border border-white/20 bg-gradient-to-r from-white/90 to-gray-50/90 p-1.5 shadow-lg backdrop-blur-md dark:border-gray-700/30 dark:from-gray-800/90 dark:to-gray-900/90'>
      <Button
        variant='ghost'
        size='sm'
        onClick={handlePreview}
        className='h-8 rounded-md border-0 px-3 text-xs font-medium transition-all duration-200 hover:scale-105 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:shadow-md dark:hover:from-purple-900/30 dark:hover:to-blue-900/30'
        title='预览消息内容'>
        <Eye className='mr-1.5 h-3.5 w-3.5 text-gray-600 dark:text-gray-300' />
        <span className='text-gray-700 dark:text-gray-200'>预览</span>
      </Button>
    </div>
  );
};

/**
 * 自定义助手消息组件
 * 显示AI助手的回复，包含模型头像和消息内容
 * AI消息左边与头像左边对齐（换行显示）
 */
export const CustomAssistantMessage = (props: ExtendedAssistantMessageProps) => {
  const { icons } = useChatContext();
  const { message, isLoading = false, subComponent } = props;
  const wrapperStyles = 'flex flex-col mb-4';
  const avatarRowStyles = 'flex items-center gap-3 mb-2';
  const messageStyles = 'bg-gray-50 dark:bg-gray-800 py-3 px-4 shadow-sm max-w-[80%] relative group';
  const avatarStyles = 'flex-shrink-0';

  // 检查消息是否有内容可以预览
  const hasPreviewableContent = message && message.trim().length > 0;

  return (
    <div className={wrapperStyles}>
      <div className={avatarRowStyles}>
        <div className={avatarStyles}>
          <Avatar className='h-8 w-8'>
            <AvatarImage
              src={props.assistantAvatar || getModelAvatar(props.modelId)}
              alt={getModelName(props.modelId)}
            />
            <AvatarFallback className='bg-gray-400 text-sm text-white'>
              <Sparkles className='h-4 w-4' />
            </AvatarFallback>
          </Avatar>
        </div>

        {/* 预览按钮放在头像后面 */}
        {hasPreviewableContent && !isLoading && <AvatarPreviewActions message={message || ''} />}
      </div>
      <div className={messageStyles}>
        {message && <Markdown content={message} />}
        {isLoading && icons.spinnerIcon}
        {subComponent && <div className='mt-2'>{subComponent}</div>}
      </div>
    </div>
  );
};

/**
 * 自定义Markdown渲染器配置
 * 修复HTML嵌套问题，避免<div>嵌套在<p>标签内
 */
export const customMarkdownRenderers = {
  // 自定义代码块渲染，避免在p标签内嵌套div
  code: ({ inline, className, children, ...props }: any) => {
    if (inline) {
      return (
        <code className='bg-gray-100 px-1.5 py-0.5 font-mono text-[0.9em] dark:bg-gray-800' {...props}>
          {children}
        </code>
      );
    }

    // 对于代码块，使用span包装而不是div来避免嵌套问题
    return (
      <span className='my-3 block'>
        <pre className='bg-muted/80 overflow-x-scroll border p-4 font-mono text-sm dark:bg-slate-900'>
          <code className={className} {...props}>
            {children}
          </code>
        </pre>
      </span>
    );
  },
  // 确保p标签不包含块级元素
  p: ({ children, ...props }: any) => {
    return (
      <span className='mb-4 block leading-relaxed' {...props}>
        {children}
      </span>
    );
  },
  // 其他可能导致嵌套问题的元素
  div: ({ children, ...props }: any) => {
    return (
      <span className='block' {...props}>
        {children}
      </span>
    );
  }
};
