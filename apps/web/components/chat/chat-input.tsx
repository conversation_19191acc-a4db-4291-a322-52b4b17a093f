'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@workspace/ui/components/button';
import { Textarea } from '@workspace/ui/components/textarea';
import { Send } from 'lucide-react';
import { ModelSelector, useModelSelector } from './model-selector';
import { DEFAULT_MODEL_ID } from '@/config/models-config';

/**
 * 聊天输入组件的属性接口
 */
export interface ChatInputProps {
  onSend: (message: string) => void;
  inProgress: boolean;
  isLoading?: boolean;
  placeholder?: string;
  className?: string;
  onModelChange?: (modelId: string) => void;
  selectedModel?: string;
}

/**
 * 聊天输入组件
 * 提供消息输入、模型选择和发送功能
 */
export function ChatInput({
  onSend,
  inProgress,
  isLoading = false,
  placeholder = '输入信息...',
  className = '',
  onModelChange,
  selectedModel: externalSelectedModel
}: ChatInputProps) {
  const [input, setInput] = useState('');
  const { selectedModel: internalSelectedModel, handleModelChange } = useModelSelector(DEFAULT_MODEL_ID);

  // 使用外部传入的模型或内部状态
  const selectedModel = externalSelectedModel || internalSelectedModel;
  const modelChangeHandler = onModelChange || handleModelChange;

  /**
   * 处理发送消息
   */
  const handleSend = () => {
    if (input.trim() && !isLoading && !inProgress) {
      onSend(input.trim());
      setInput('');
    }
  };

  /**
   * 处理键盘事件
   */
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  /**
   * 检查是否可以发送
   */
  const canSend = input.trim() && !isLoading && !inProgress;

  return (
    <div
      className={`border-t border-gray-200/50 bg-white/80 p-4 backdrop-blur-sm dark:border-gray-700/50 dark:bg-slate-900/80 ${className}`}>
      <div className='flex flex-col gap-3'>
        <div className='relative'>
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={placeholder}
            className='min-h-[120px] resize-none rounded-xl border-slate-300/60 bg-white/90 pb-12 shadow-sm backdrop-blur-sm transition-all duration-200 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/50 dark:border-slate-600/60 dark:bg-slate-800/90'
            onKeyDown={handleKeyDown}
            disabled={isLoading || inProgress}
          />
          <div className='absolute bottom-3 left-3 right-3 z-10 flex items-center justify-between'>
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={modelChangeHandler}
              disabled={isLoading || inProgress}
            />
            <Button
              onClick={handleSend}
              disabled={!canSend}
              size='sm'
              className='rounded-lg border-0 bg-gray-600 px-3 text-white transition-colors duration-200 hover:bg-gray-700 disabled:cursor-not-allowed disabled:opacity-50'
              type='button'>
              {isLoading || inProgress ? (
                <div className='h-3 w-3 animate-spin rounded-full border-2 border-white/30 border-t-white'></div>
              ) : (
                <Send className='h-3 w-3' />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * 聊天输入Hook
 * 提供输入状态管理和发送逻辑
 */
export function useChatInput() {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  /**
   * 清空输入
   */
  const clearInput = () => {
    setInput('');
  };

  /**
   * 设置加载状态
   */
  const setLoadingState = (loading: boolean) => {
    setIsLoading(loading);
  };

  /**
   * 处理输入变化
   */
  const handleInputChange = (value: string) => {
    setInput(value);
  };

  return {
    input,
    isLoading,
    setInput: handleInputChange,
    clearInput,
    setLoadingState
  };
}
