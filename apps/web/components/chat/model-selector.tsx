'use client';

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@workspace/ui/components/select';
import { DEFAULT_MODEL_ID, getGroupedModelOptions } from '@/config/models-config';

/**
 * 模型选择器组件的属性接口
 */
export interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

/**
 * 模型选择器组件
 * 提供模型选择功能，支持分组显示
 */
export function ModelSelector({
  selectedModel,
  onModelChange,
  className = '',
  size = 'sm',
  disabled = false
}: ModelSelectorProps) {
  const modelOptions = getGroupedModelOptions();

  const sizeClasses = {
    sm: 'h-8 text-xs',
    md: 'h-10 text-sm',
    lg: 'h-12 text-base'
  };

  return (
    <Select value={selectedModel} onValueChange={onModelChange} disabled={disabled}>
      <SelectTrigger className={`min-w-fit max-w-[300px] ${sizeClasses[size]} ${className}`}>
        <SelectValue placeholder='选择模型' />
      </SelectTrigger>
      <SelectContent>
        {modelOptions.map((group) => (
          <div key={group.category}>
            <div className='px-2 py-1.5 text-xs font-semibold'>{group.categoryName}</div>
            {group.models.map((model) => (
              <SelectItem key={model.value} value={model.value} className='text-xs'>
                <div className='flex items-center gap-2'>
                  {model.iconPath && <img src={model.iconPath} alt={model.label} className='h-4 w-4 rounded-full' />}
                  <span>{model.label}</span>
                </div>
              </SelectItem>
            ))}
          </div>
        ))}
      </SelectContent>
    </Select>
  );
}

/**
 * 使用模型选择器的Hook
 * 提供模型选择的状态管理
 */
export function useModelSelector(initialModel: string = DEFAULT_MODEL_ID) {
  const [selectedModel, setSelectedModel] = React.useState(initialModel);

  /**
   * 更改选中的模型
   */
  const handleModelChange = React.useCallback((modelId: string) => {
    setSelectedModel(modelId);
  }, []);

  /**
   * 重置为默认模型
   */
  const resetToDefault = React.useCallback(() => {
    setSelectedModel(DEFAULT_MODEL_ID);
  }, []);

  return {
    selectedModel,
    handleModelChange,
    resetToDefault
  };
}
