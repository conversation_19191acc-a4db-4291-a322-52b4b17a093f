'use client';

import React from 'react';
import { useAgent } from '@/components/providers/agent-provider';

/**
 * Agent选择器组件的属性接口
 */
export interface AgentSelectorProps {
  className?: string;
  onAgentSelected?: (agentId: string, reason?: string) => void;
}

/**
 * Agent选择器组件
 * 提供智能Agent选择功能
 */
export function AgentSelector({ className, onAgentSelected }: AgentSelectorProps) {
  const { agents, activeAgent, setActiveAgent } = useAgent();

  /**
   * 处理Agent选择
   */
  const handleAgentSelect = (agentId: string, reason?: string) => {
    const agent = agents.find((a) => a.metadata.id === agentId);
    if (agent) {
      setActiveAgent(agent);
      onAgentSelected?.(agentId, reason);
    }
  };

  return (
    <div className={`agent-selector ${className || ''}`}>
      {/* Agent选择器UI可以根据需要实现 */}
      <div className='text-sm text-gray-600'>
        当前Agent: {activeAgent?.metadata.name || '未选择'}
      </div>
    </div>
  );
}

/**
 * 智能Agent选择Hook
 * 提供基于用户输入的智能Agent选择功能
 */
export function useAgentSelector() {
  const { agents, activeAgent, setActiveAgent } = useAgent();

  /**
   * 智能选择最适合的Agent
   * 根据用户输入的内容分析并选择合适的Agent
   */
  const selectBestAgent = React.useCallback(async (userInput: string) => {
    if (agents.length === 0) return null;

    // 智能分析用户输入，自动选择最合适的Agent
    const input = userInput.toLowerCase();

    // 关键词映射到Agent类型
    const agentKeywords = {
      research: ['研究', '调研', '分析', '搜索', 'research', 'analyze'],
      code: ['代码', '编程', '开发', 'code', 'programming', 'development'],
      design: ['设计', '界面', 'UI', 'UX', 'design'],
      writing: ['写作', '文档', '内容', 'writing', 'content'],
      planning: ['计划', '规划', '策略', 'planning', 'strategy']
    };

    // 查找最匹配的agent
    let bestAgent = null;
    let maxScore = 0;

    for (const agent of agents) {
      let score = 0;
      const agentName = agent.metadata.name.toLowerCase();
      const agentDesc = agent.metadata.description?.toLowerCase() || '';

      // 检查关键词匹配
      for (const [category, keywords] of Object.entries(agentKeywords)) {
        for (const keyword of keywords) {
          if (input.includes(keyword)) {
            if (agentName.includes(category) || agentDesc.includes(category)) {
              score += 10;
            }
          }
        }
      }

      // 直接名称匹配
      if (input.includes(agentName)) {
        score += 20;
      }

      if (score > maxScore) {
        maxScore = score;
        bestAgent = agent;
      }
    }

    // 如果没有匹配的，使用第一个可用的Agent
    return bestAgent || agents[0];
  }, [agents]);

  /**
   * 选择Agent并返回选择原因
   */
  const selectAgentWithReason = React.useCallback(async (userInput: string) => {
    const bestAgent = await selectBestAgent(userInput);
    if (bestAgent && bestAgent !== activeAgent) {
      setActiveAgent(bestAgent);
      return {
        agent: bestAgent,
        reason: `基于您的输入"${userInput}"，选择了${bestAgent.metadata.name}`
      };
    }
    return {
      agent: activeAgent,
      reason: '继续使用当前Agent'
    };
  }, [selectBestAgent, activeAgent, setActiveAgent]);

  /**
   * 手动选择Agent
   */
  const selectAgent = React.useCallback((agentId: string) => {
    const agent = agents.find((a) => a.metadata.id === agentId);
    if (agent) {
      setActiveAgent(agent);
      return agent;
    }
    return null;
  }, [agents, setActiveAgent]);

  return {
    agents,
    activeAgent,
    selectBestAgent,
    selectAgentWithReason,
    selectAgent
  };
}