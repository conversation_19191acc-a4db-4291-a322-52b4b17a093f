# Chat 模块

这是一个重构后的聊天模块，提供了清晰的架构和可扩展的组件设计。

## 架构概览

```
chat/
├── index.ts                           # 模块入口，导出所有组件和类型
├── copilot-chat-with-thinking.tsx     # 主聊天组件
├── copilot-kit-wrapper.tsx           # CopilotKit包装器
├── thinking-display.tsx              # 思考显示组件
├── message-components.tsx             # 消息组件（用户消息、助手消息）
├── chat-input.tsx                     # 聊天输入组件
├── model-selector.tsx                 # 模型选择器组件
├── agent-selector.tsx                 # Agent选择器组件
└── README.md                          # 文档说明
```

## 组件说明

### 1. CopilotChatWithThinking
主聊天组件，集成了所有功能：
- 智能Agent选择
- 思考过程显示
- 消息渲染
- 输入处理

### 2. CopilotKitWrapper
CopilotKit的包装器组件，负责：
- 提供CopilotKit上下文
- 配置API端点
- 管理当前活跃的Agent

### 3. ThinkingDisplay
思考显示组件，功能包括：
- 显示AI思考过程
- 动画效果
- 思考内容展示

### 4. MessageComponents
消息组件模块，包含：
- CustomUserMessage: 用户消息组件
- CustomAssistantMessage: 助手消息组件
- customMarkdownRenderers: 自定义Markdown渲染器

### 5. ChatInput
聊天输入组件，提供：
- 消息输入框
- 模型选择器集成
- 发送按钮
- 键盘快捷键支持

### 6. ModelSelector
模型选择器组件，功能：
- 模型选择下拉框
- 分组显示
- 图标支持

### 7. AgentSelector
 Agent选择器组件，提供：
- 智能Agent选择算法
- 基于关键词的匹配
- Agent状态管理

## 使用方法

### 基本使用

```tsx
import { CopilotKitWrapper } from '@/components/chat';

function App() {
  return (
    <div className="h-screen">
      <CopilotKitWrapper />
    </div>
  );
}
```

### 自定义配置

```tsx
import { CopilotChatWithThinking } from '@/components/chat';
import { CopilotKit } from '@copilotkit/react-core';

function CustomChat() {
  return (
    <CopilotKit runtimeUrl="/api/custom-copilot">
      <CopilotChatWithThinking className="custom-chat" />
    </CopilotKit>
  );
}
```

### 使用独立组件

```tsx
import { 
  ThinkingDisplay, 
  useThinking,
  ModelSelector,
  useModelSelector 
} from '@/components/chat';

function CustomComponent() {
  const { isThinking, thinkingContent } = useThinking();
  const { selectedModel, handleModelChange } = useModelSelector();

  return (
    <div>
      <ThinkingDisplay 
        isThinking={isThinking} 
        thinkingContent={thinkingContent} 
      />
      <ModelSelector 
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
      />
    </div>
  );
}
```

## 扩展指南

### 添加新的消息类型

1. 在 `message-components.tsx` 中添加新的消息组件
2. 更新 `MessageComponentProps` 类型
3. 在主聊天组件中集成新组件

### 添加新的输入功能

1. 扩展 `ChatInputProps` 接口
2. 在 `chat-input.tsx` 中添加新功能
3. 更新相关的Hook

### 自定义Agent选择逻辑

1. 修改 `agent-selector.tsx` 中的 `selectBestAgent` 函数
2. 添加新的关键词映射
3. 实现自定义评分算法

## 依赖关系

- `@copilotkit/react-core`: CopilotKit核心功能
- `@copilotkit/react-ui`: CopilotKit UI组件
- `@workspace/ui`: 内部UI组件库
- `@ag-ui/core`: AG-UI核心功能
- React Hooks: 状态管理和副作用处理

## 最佳实践

1. **组件职责单一**: 每个组件只负责一个特定功能
2. **类型安全**: 使用TypeScript确保类型安全
3. **可复用性**: 组件设计考虑复用性
4. **性能优化**: 使用React.memo和useCallback优化性能
5. **错误处理**: 适当的错误边界和异常处理

## 注意事项

- 确保所有依赖项已正确安装
- 检查环境变量配置
- 注意组件的生命周期管理
- 遵循项目的代码规范