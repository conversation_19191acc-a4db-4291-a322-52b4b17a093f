'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { TestTube, Play, Square, RotateCcw, Maximize2, Minimize2 } from 'lucide-react';
import { ChatSidebar } from '@/components/layout/chat-sidebar';
import { useAgentWebSocket } from '@/hooks/use-agent-websocket';
import { getAgentConfig } from '@/lib/agent-config';
import type { ChatInput } from '@/lib/types/chat';

/**
 * Mario Agent 工作区示例组件
 * 展示如何使用新的聊天架构集成 Mario Agent
 */
export function MarioWorkspaceExample() {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [testCases, setTestCases] = useState<any[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  
  // 获取 Mario Agent 配置
  const marioConfig = getAgentConfig('mario');
  
  // 使用 Agent WebSocket Hook
  const {
    messages,
    sendMessage,
    clearMessages,
    connect,
    disconnect
  } = useAgentWebSocket({
    websocketUrl: marioConfig?.websocketUrl,
    autoConnect: true
  });
  
  // 模拟连接状态和其他属性
  const connectionState = 'connected' as const;
  const loadingState = { isLoading: false, message: '' };
  const error = null;

  /**
   * 处理测试用例生成
   */
  const handleGenerateTestCases = useCallback(async () => {
    if (connectionState !== 'connected') {
      console.error('Mario Agent 未连接');
      return;
    }

    setIsGenerating(true);
    
    try {
      const chatInput: ChatInput = {
        agentId: 'mario',
        message: '请为当前项目生成测试用例',
        model: 'gpt-4',
        metadata: {
          action: 'generate_test_cases',
          timestamp: new Date().toISOString()
        }
      };
      
      await sendMessage(chatInput);
    } catch (error) {
      console.error('生成测试用例失败:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [connectionState, sendMessage]);

  /**
   * 处理全屏切换
   */
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  /**
   * 处理连接重试
   */
  const handleRetryConnection = useCallback(() => {
    disconnect();
    setTimeout(() => {
      connect();
    }, 1000);
  }, [disconnect, connect]);

  return (
    <div className={`flex h-full ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      {/* 主工作区 */}
      <div className='flex-1 flex flex-col'>
        {/* 工作区头部 */}
        <div className='border-b p-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <TestTube className='h-6 w-6 text-blue-500' />
              <div>
                <h1 className='text-xl font-semibold'>Mario 测试生成器</h1>
                <p className='text-sm text-gray-600'>AI 驱动的测试用例生成工具</p>
              </div>
            </div>
            
            <div className='flex items-center gap-2'>
              {/* 连接状态 */}
              <Badge 
                variant={connectionState === 'connected' ? 'default' : 'secondary'}
                className='text-xs'
              >
                {connectionState === 'connected' ? '已连接' : '未连接'}
              </Badge>
              
              {/* 全屏切换 */}
              <Button
                variant='ghost'
                size='sm'
                onClick={toggleFullscreen}
              >
                {isFullscreen ? (
                  <Minimize2 className='h-4 w-4' />
                ) : (
                  <Maximize2 className='h-4 w-4' />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 工作区内容 */}
        <div className='flex-1 p-4 space-y-4'>
          {/* 控制面板 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>测试生成控制</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='flex items-center gap-4'>
                <Button
                  onClick={handleGenerateTestCases}
                  disabled={connectionState !== 'connected' || isGenerating}
                  className='flex items-center gap-2'
                >
                  {isGenerating ? (
                    <>
                      <div className='h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-white' />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Play className='h-4 w-4' />
                      生成测试用例
                    </>
                  )}
                </Button>
                
                <Button
                  variant='outline'
                  onClick={() => setTestCases([])}
                  disabled={testCases.length === 0}
                >
                  <Square className='h-4 w-4 mr-2' />
                  清空结果
                </Button>
                
                {connectionState !== 'connected' && (
                  <Button
                    variant='outline'
                    onClick={handleRetryConnection}
                  >
                    <RotateCcw className='h-4 w-4 mr-2' />
                    重新连接
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 错误显示 */}
          {error && (
            <Card className='border-red-200 bg-red-50'>
              <CardContent className='pt-6'>
                <div className='text-red-800'>
                  <strong>连接错误:</strong> {error.message}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 加载状态 */}
          {loadingState.isLoading && (
            <Card className='border-blue-200 bg-blue-50'>
              <CardContent className='pt-6'>
                <div className='flex items-center gap-2 text-blue-800'>
                  <div className='h-4 w-4 animate-spin rounded-full border-2 border-blue-300 border-t-blue-800' />
                  <span>{loadingState.message || '处理中...'}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 测试用例结果 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>生成的测试用例</CardTitle>
            </CardHeader>
            <CardContent>
              {testCases.length === 0 ? (
                <div className='text-center py-8 text-gray-500'>
                  <TestTube className='h-12 w-12 mx-auto mb-4 text-gray-400' />
                  <p>暂无测试用例</p>
                  <p className='text-sm'>点击"生成测试用例"开始</p>
                </div>
              ) : (
                <div className='space-y-4'>
                  {testCases.map((testCase, index) => (
                    <div key={index} className='border rounded-lg p-4'>
                      <h4 className='font-medium mb-2'>{testCase.name}</h4>
                      <p className='text-sm text-gray-600 mb-2'>{testCase.description}</p>
                      <div className='flex gap-2'>
                        <Badge variant='outline'>{testCase.type}</Badge>
                        <Badge variant='outline'>{testCase.priority}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 聊天侧边栏 */}
      <div className='w-96 border-l'>
        <ChatSidebar className='h-full' />
      </div>
    </div>
  );
}

/**
 * 使用说明组件
 */
export function MarioUsageInstructions() {
  return (
    <Card className='m-4'>
      <CardHeader>
        <CardTitle>Mario Agent 集成说明</CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div>
          <h4 className='font-medium mb-2'>架构优势</h4>
          <ul className='text-sm text-gray-600 space-y-1 list-disc list-inside'>
            <li>统一的 Agent WebSocket 管理</li>
            <li>可扩展的多 Agent 支持</li>
            <li>清晰的状态管理和错误处理</li>
            <li>一致的用户界面和交互体验</li>
          </ul>
        </div>
        
        <div>
          <h4 className='font-medium mb-2'>使用方式</h4>
          <ol className='text-sm text-gray-600 space-y-1 list-decimal list-inside'>
            <li>在 workspace.tsx 中导入 ChatSidebar 组件</li>
            <li>使用 useAgentWebSocket Hook 管理连接</li>
            <li>通过 sendMessage 发送消息到 Agent</li>
            <li>监听 messages 状态获取响应</li>
          </ol>
        </div>
        
        <div>
          <h4 className='font-medium mb-2'>代码示例</h4>
          <pre className='text-xs bg-gray-100 p-3 rounded overflow-x-auto'>
{`// 在 workspace.tsx 中使用
import { ChatSidebar } from '@/components/layout/chat-sidebar';
import { useAgentWebSocket } from '@/hooks/use-agent-websocket';

const { sendMessage, messages, connectionState } = useAgentWebSocket({
  agentId: 'mario',
  websocketUrl: 'ws://localhost:8000/mario',
  autoConnect: true
});

// 发送消息
const handleSendMessage = async (message: string) => {
  await sendMessage({
    message,
    model: 'gpt-4',
    metadata: { action: 'generate_test' }
  });
};

// 渲染聊天侧边栏
<ChatSidebar className="w-96" />`}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
}