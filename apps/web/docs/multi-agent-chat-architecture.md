# 多 Agent 聊天架构文档

## 概述

本文档介绍了新的多 Agent 聊天架构，该架构旨在提供统一、可扩展的方式来管理多个 AI Agent 的聊天功能。

## 架构特点

### 🎯 核心优势

- **统一管理**: 所有 Agent 使用相同的 WebSocket 连接和状态管理模式
- **可扩展性**: 轻松添加新的 Agent 类型，无需修改核心聊天逻辑
- **类型安全**: 完整的 TypeScript 类型定义，确保开发时的类型安全
- **状态管理**: 统一的连接状态、加载状态和错误处理
- **用户体验**: 一致的 UI 交互和视觉反馈

### 🏗️ 架构组件

```
apps/web/
├── lib/
│   ├── types/
│   │   └── chat.ts              # 聊天系统类型定义
│   └── agent-config.ts          # Agent 配置管理
├── hooks/
│   └── use-agent-websocket.ts   # Agent WebSocket Hook
├── components/
│   ├── layout/
│   │   └── chat-sidebar.tsx     # 聊天侧边栏组件
│   └── enhanced-agent-selector.tsx # Agent 选择器组件
└── docs/
    └── multi-agent-chat-architecture.md # 本文档
```

## 核心组件详解

### 1. 类型定义 (`lib/types/chat.ts`)

定义了聊天系统的核心类型：

```typescript
// 基础消息类型
interface BaseMessage {
  id: string;
  content: string;
  timestamp: Date;
  role: 'user' | 'assistant' | 'system';
}

// Agent 消息类型
interface AgentMessage extends BaseMessage {
  agentId: string;
  model?: string;
  metadata?: Record<string, any>;
}

// WebSocket 连接状态
type WebSocketConnectionState = 'disconnected' | 'connecting' | 'connected' | 'error';

// 聊天输入类型
interface ChatInput {
  message: string;
  model?: string;
  metadata?: Record<string, any>;
}
```

### 2. Agent 配置管理 (`lib/agent-config.ts`)

统一管理所有 Agent 的配置信息：

```typescript
interface AgentConfig {
  id: string;
  name: string;
  type: AgentType;
  description: string;
  websocketUrl?: string;
  defaultModel: string;
  supportedModels: string[];
  capabilities: string[];
  metadata: Record<string, any>;
}

// 预定义的 Agent 配置
const AGENT_CONFIGS: AgentConfig[] = [
  {
    id: 'mario',
    name: 'Mario',
    type: 'Coder',
    description: 'AI 测试用例生成专家',
    websocketUrl: 'ws://localhost:8000/mario',
    defaultModel: 'gpt-4',
    supportedModels: ['gpt-4', 'gpt-3.5-turbo'],
    capabilities: ['测试生成', '代码分析', '质量保证'],
    metadata: { version: '1.0.0', category: 'testing' }
  },
  // ... 其他 Agent 配置
];
```

### 3. WebSocket Hook (`hooks/use-agent-websocket.ts`)

提供统一的 Agent WebSocket 连接管理：

```typescript
interface UseAgentWebSocketOptions {
  agentId: string;
  websocketUrl?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

interface UseAgentWebSocketReturn {
  // 状态
  messages: AgentMessage[];
  connectionState: WebSocketConnectionState;
  loadingState: LoadingState;
  error: AgentWebSocketError | null;
  
  // 操作
  sendMessage: (input: ChatInput) => Promise<void>;
  clearMessages: () => void;
  connect: () => void;
  disconnect: () => void;
}
```

### 4. 聊天侧边栏 (`components/layout/chat-sidebar.tsx`)

统一的聊天界面组件，支持多 Agent 切换：

- Agent 选择和连接状态显示
- 消息历史记录
- 输入框和发送功能
- 模型选择
- 错误处理和重连

## 使用指南

### 1. 基础使用

在任何需要聊天功能的组件中：

```typescript
import { ChatSidebar } from '@/components/layout/chat-sidebar';
import { useAgentWebSocket } from '@/hooks/use-agent-websocket';

function MyWorkspace() {
  const {
    messages,
    connectionState,
    sendMessage,
    error
  } = useAgentWebSocket({
    agentId: 'mario',
    autoConnect: true
  });

  return (
    <div className="flex h-full">
      {/* 主工作区 */}
      <div className="flex-1">
        {/* 你的工作区内容 */}
      </div>
      
      {/* 聊天侧边栏 */}
      <div className="w-96 border-l">
        <ChatSidebar className="h-full" />
      </div>
    </div>
  );
}
```

### 2. 发送消息

```typescript
const handleSendMessage = async (message: string) => {
  try {
    await sendMessage({
      message,
      model: 'gpt-4',
      metadata: {
        action: 'generate_test_cases',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('发送消息失败:', error);
  }
};
```

### 3. 监听消息

```typescript
useEffect(() => {
  // 监听新消息
  const latestMessage = messages[messages.length - 1];
  if (latestMessage && latestMessage.role === 'assistant') {
    // 处理 Agent 响应
    handleAgentResponse(latestMessage);
  }
}, [messages]);
```

### 4. 连接状态处理

```typescript
const getConnectionStatusDisplay = () => {
  switch (connectionState) {
    case 'connected':
      return <Badge variant="default">已连接</Badge>;
    case 'connecting':
      return <Badge variant="secondary">连接中...</Badge>;
    case 'disconnected':
      return <Badge variant="outline">未连接</Badge>;
    case 'error':
      return <Badge variant="destructive">连接错误</Badge>;
  }
};
```

## 迁移指南

### 从 Mario Agent 迁移

如果你之前使用的是 `integrations/mario-agent/src/workspace.tsx` 中的聊天功能，按以下步骤迁移：

#### 1. 移除旧的导入

```typescript
// 移除这些导入
import { useAGUIWebSocket } from '../hooks/useAGUIWebSocket';
import { useMarioWebSocket } from '../hooks/useMarioWebSocket';
```

#### 2. 添加新的导入

```typescript
// 添加这些导入
import { ChatSidebar } from '@/components/layout/chat-sidebar';
import { useAgentWebSocket } from '@/hooks/use-agent-websocket';
import type { ChatInput } from '@/lib/types/chat';
```

#### 3. 替换 Hook 使用

```typescript
// 旧的方式
const {
  messages: marioMessages,
  sendMessage: sendMarioMessage,
  connectionState: marioConnectionState,
  // ...
} = useMarioWebSocket({
  url: 'ws://localhost:8000/mario',
  autoConnect: true
});

// 新的方式
const {
  messages,
  sendMessage,
  connectionState,
  loadingState,
  error
} = useAgentWebSocket({
  agentId: 'mario',
  autoConnect: true
});
```

#### 4. 更新消息发送逻辑

```typescript
// 旧的方式
sendMarioMessage({
  type: 'user_message',
  content: message,
  metadata: { action: 'generate_test' }
});

// 新的方式
sendMessage({
  message,
  model: 'gpt-4',
  metadata: { action: 'generate_test' }
});
```

#### 5. 替换 UI 组件

```typescript
// 移除自定义聊天 UI，使用统一的 ChatSidebar
<div className="w-96 border-l">
  <ChatSidebar className="h-full" />
</div>
```

## 添加新 Agent

### 1. 定义 Agent 配置

在 `lib/agent-config.ts` 中添加新的 Agent 配置：

```typescript
const newAgentConfig: AgentConfig = {
  id: 'my-new-agent',
  name: 'My New Agent',
  type: 'Researcher',
  description: '专门用于研究和分析的 AI Agent',
  websocketUrl: 'ws://localhost:8000/my-new-agent',
  defaultModel: 'gpt-4',
  supportedModels: ['gpt-4', 'gpt-3.5-turbo'],
  capabilities: ['研究分析', '数据处理', '报告生成'],
  metadata: {
    version: '1.0.0',
    category: 'research'
  }
};

// 添加到配置列表
AGENT_CONFIGS.push(newAgentConfig);
```

### 2. 使用新 Agent

```typescript
const { messages, sendMessage, connectionState } = useAgentWebSocket({
  agentId: 'my-new-agent',
  autoConnect: true
});
```

### 3. 自定义 Agent 图标（可选）

在 `components/enhanced-agent-selector.tsx` 中添加图标映射：

```typescript
const getAgentIcon = (agentId: string) => {
  switch (agentId) {
    case 'mario':
      return TestTube;
    case 'my-new-agent':
      return Search; // 添加新的图标
    default:
      return MessageSquare;
  }
};
```

## 最佳实践

### 1. 错误处理

```typescript
const { error, connectionState } = useAgentWebSocket({ agentId: 'mario' });

// 显示错误信息
if (error) {
  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded">
      <h4 className="font-medium text-red-800">连接错误</h4>
      <p className="text-red-600">{error.message}</p>
      <Button onClick={() => window.location.reload()}>重新加载</Button>
    </div>
  );
}
```

### 2. 加载状态

```typescript
const { loadingState } = useAgentWebSocket({ agentId: 'mario' });

if (loadingState.isLoading) {
  return (
    <div className="flex items-center gap-2 p-4">
      <Spinner className="h-4 w-4" />
      <span>{loadingState.message || '处理中...'}</span>
    </div>
  );
}
```

### 3. 消息类型处理

```typescript
const handleMessage = (message: AgentMessage) => {
  switch (message.metadata?.type) {
    case 'test_case':
      // 处理测试用例消息
      setTestCases(prev => [...prev, message.metadata.testCase]);
      break;
    case 'error':
      // 处理错误消息
      showErrorNotification(message.content);
      break;
    default:
      // 处理普通消息
      console.log('收到消息:', message.content);
  }
};
```

### 4. 性能优化

```typescript
// 使用 useMemo 优化消息渲染
const filteredMessages = useMemo(() => {
  return messages.filter(msg => msg.agentId === currentAgentId);
}, [messages, currentAgentId]);

// 使用 useCallback 优化事件处理
const handleSendMessage = useCallback(async (message: string) => {
  await sendMessage({ message, model: selectedModel });
}, [sendMessage, selectedModel]);
```

## 故障排除

### 常见问题

1. **WebSocket 连接失败**
   - 检查 Agent 服务是否运行
   - 验证 WebSocket URL 是否正确
   - 查看浏览器控制台的错误信息

2. **消息发送失败**
   - 确认连接状态为 'connected'
   - 检查消息格式是否正确
   - 验证 Agent 服务的消息处理逻辑

3. **Agent 选择器不显示新 Agent**
   - 确认 Agent 配置已正确添加到 `AGENT_CONFIGS`
   - 检查 Agent ID 是否唯一
   - 验证 Agent 类型是否在 `AgentType` 中定义

### 调试技巧

```typescript
// 启用详细日志
const { messages, connectionState } = useAgentWebSocket({
  agentId: 'mario',
  autoConnect: true,
  debug: true // 启用调试模式
});

// 监听所有状态变化
useEffect(() => {
  console.log('连接状态变化:', connectionState);
}, [connectionState]);

useEffect(() => {
  console.log('新消息:', messages[messages.length - 1]);
}, [messages]);
```

## 总结

新的多 Agent 聊天架构提供了：

- ✅ 统一的 Agent 管理和配置
- ✅ 类型安全的 TypeScript 支持
- ✅ 可扩展的架构设计
- ✅ 一致的用户体验
- ✅ 完善的错误处理和状态管理
- ✅ 简单的迁移路径

通过这个架构，你可以轻松地添加新的 Agent，而无需重复实现聊天功能，同时保持代码的清晰和可维护性。