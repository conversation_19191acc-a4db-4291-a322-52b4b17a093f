import { MenuIntegrationConfig } from './types/integration';

export const menuIntegrations: MenuIntegrationConfig[] = [
  {
    id: 'middleware-starter',
    name: 'Middleware Starter',
    features: ['agentic_chat']
  },
  {
    id: 'mastra-starter',
    name: 'mastra-starter',
    features: ['agentic_chat']
  },
  {
    id: 'aisdk-starter',
    name: 'aisdk-starter',
    features: ['agentic_chat']
  },
  {
    id: 'langgraph-starter',
    name: 'langgraph-starter',
    features: [
      'agentic_chat',
      'human_in_the_loop',
      'agentic_generative_ui',
      'tool_based_generative_ui',
      'predictive_state_updates',
      'shared_state'
    ]
  }
];
