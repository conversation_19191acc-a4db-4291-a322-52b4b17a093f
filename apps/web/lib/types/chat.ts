/**
 * 通用聊天系统类型定义
 * 支持多种 Agent 的聊天功能
 */

// 基础消息接口
export interface BaseMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  type?: 'text' | 'error' | 'system' | 'interrupt' | 'tool_call' | 'tool_result';
  metadata?: Record<string, any>;
}

// Agent 特定的消息扩展
export interface AgentMessage extends BaseMessage {
  agentId: string;
  agentType: string;
  model?: string;
}

// WebSocket 连接状态
export interface WebSocketConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastConnectedAt?: Date;
  reconnectAttempts?: number;
}

// 加载状态
export interface LoadingState {
  isLoading: boolean;
  loadingMessage: string;
}

// Agent 配置接口
export interface AgentConfig {
  id: string;
  name: string;
  type: string;
  description?: string;
  websocketUrl?: string;
  defaultModel?: string;
  supportedModels?: string[];
  capabilities?: string[];
  metadata?: Record<string, any>;
}

// 聊天输入接口
export interface ChatInput {
  message: string;
  agentId?: string;
  model?: string;
  metadata?: Record<string, any>;
}

// WebSocket 事件接口
export interface WebSocketEvent {
  type: string;
  timestamp: string;
  thread_id?: string;
  run_id?: string;
  message_id?: string;
  role?: string;
  delta?: string;
  content?: string;
  tool_calls?: any[];
  tool_call_results?: any[];
  snapshot?: any;
  messages?: any[];
  error?: string;
  agentId?: string;
}

// 聊天会话接口
export interface ChatSession {
  id: string;
  agentId: string;
  messages: AgentMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

// Agent WebSocket Hook 返回类型
export interface UseAgentWebSocketReturn {
  // 连接状态
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  
  // 加载状态
  isLoading: boolean;
  loadingMessage: string;
  
  // 消息
  messages: AgentMessage[];
  currentStreamingMessage: string;
  
  // 方法
  sendMessage: (input: ChatInput) => Promise<void>;
  stopCurrentOperation: () => void;
  clearMessages: () => void;
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
}

// Agent WebSocket Hook 选项
export interface UseAgentWebSocketOptions {
  agentConfig: AgentConfig;
  websocketUrl?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  onContainerCreated?: (containerId: string, port: number) => void;
  onError?: (error: string) => void;
  onMessage?: (message: AgentMessage) => void;
}

// 聊天面板配置
export interface ChatPanelConfig {
  showModelSelector?: boolean;
  showAgentSelector?: boolean;
  enableFileUpload?: boolean;
  enableVoiceInput?: boolean;
  maxMessageLength?: number;
  placeholder?: string;
}

// 错误类型
export class ChatError extends Error {
  constructor(
    message: string,
    public code?: string,
    public agentId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ChatError';
  }
}

export class AgentWebSocketError extends ChatError {
  constructor(
    message: string,
    public url?: string,
    agentId?: string,
    details?: any
  ) {
    super(message, 'WEBSOCKET_ERROR', agentId, details);
    this.name = 'AgentWebSocketError';
  }
}

// 常量
export const CHAT_CONSTANTS = {
  DEFAULT_WEBSOCKET_RECONNECT_DELAY: 3000,
  MAX_RECONNECT_ATTEMPTS: 5,
  MESSAGE_QUEUE_MAX_SIZE: 1000,
  DEFAULT_MESSAGE_PLACEHOLDER: '输入消息...',
  STREAMING_MESSAGE_DEBOUNCE: 100
} as const;