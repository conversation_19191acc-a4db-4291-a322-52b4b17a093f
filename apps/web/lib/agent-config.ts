/**
 * Agent 配置管理器
 * 统一管理所有 Agent 的配置信息
 */

import type { AgentConfig } from '@/lib/types/chat';

/**
 * 预定义的 Agent 配置
 */
export const AGENT_CONFIGS: Record<string, AgentConfig> = {
  mario: {
    id: 'mario',
    name: 'Mario Agent',
    type: 'test-generator',
    description: '专门用于生成和执行测试用例的智能代理',
    websocketUrl: process.env.NEXT_PUBLIC_MARIO_WS_URL || 'ws://localhost:8080/ws/ag-ui',
    defaultModel: 'gpt-4o-2024-11-20',
    supportedModels: [
      'gpt-4o-2024-11-20',
      'gpt-4-turbo',
      'gpt-3.5-turbo',
      'claude-3-sonnet',
      'claude-3-haiku'
    ],
    capabilities: [
      'test-generation',
      'code-analysis',
      'container-management',
      'web-testing'
    ],
    metadata: {
      defaultPort: 8080,
      containerImage: 'node:18-alpine',
      supportsMCP: true,
      supportsStreaming: true
    }
  },
  
  haiku: {
    id: 'haiku',
    name: 'Haiku Agent',
    type: 'creative-writer',
    description: '专门创作俳句和诗歌的创意代理',
    websocketUrl: process.env.NEXT_PUBLIC_HAIKU_WS_URL || 'ws://localhost:8081/ws/ag-ui',
    defaultModel: 'claude-3-haiku',
    supportedModels: [
      'claude-3-haiku',
      'claude-3-sonnet',
      'gpt-4o-2024-11-20',
      'gpt-4-turbo'
    ],
    capabilities: [
      'poetry-generation',
      'creative-writing',
      'language-analysis'
    ],
    metadata: {
      supportsMCP: false,
      supportsStreaming: true,
      language: 'multilingual'
    }
  },
  
  researcher: {
    id: 'researcher',
    name: 'Research Agent',
    type: 'researcher',
    description: '专门进行研究和信息收集的智能代理',
    websocketUrl: process.env.NEXT_PUBLIC_RESEARCHER_WS_URL || 'ws://localhost:8082/ws/ag-ui',
    defaultModel: 'gpt-4o-2024-11-20',
    supportedModels: [
      'gpt-4o-2024-11-20',
      'gpt-4-turbo',
      'claude-3-sonnet'
    ],
    capabilities: [
      'web-search',
      'data-analysis',
      'report-generation',
      'fact-checking'
    ],
    metadata: {
      supportsMCP: true,
      supportsStreaming: true,
      searchEnabled: true
    }
  },
  
  planner: {
    id: 'planner',
    name: 'Planning Agent',
    type: 'planner',
    description: '专门进行项目规划和任务管理的智能代理',
    websocketUrl: process.env.NEXT_PUBLIC_PLANNER_WS_URL || 'ws://localhost:8083/ws/ag-ui',
    defaultModel: 'gpt-4o-2024-11-20',
    supportedModels: [
      'gpt-4o-2024-11-20',
      'gpt-4-turbo',
      'claude-3-sonnet'
    ],
    capabilities: [
      'project-planning',
      'task-breakdown',
      'timeline-creation',
      'resource-allocation'
    ],
    metadata: {
      supportsMCP: true,
      supportsStreaming: true,
      planningTools: true
    }
  },
  
  coder: {
    id: 'coder',
    name: 'Coding Agent',
    type: 'coder',
    description: '专门进行代码编写和调试的智能代理',
    websocketUrl: process.env.NEXT_PUBLIC_CODER_WS_URL || 'ws://localhost:8084/ws/ag-ui',
    defaultModel: 'gpt-4o-2024-11-20',
    supportedModels: [
      'gpt-4o-2024-11-20',
      'gpt-4-turbo',
      'claude-3-sonnet',
      'claude-3-haiku'
    ],
    capabilities: [
      'code-generation',
      'code-review',
      'debugging',
      'refactoring',
      'testing'
    ],
    metadata: {
      supportsMCP: true,
      supportsStreaming: true,
      codeExecution: true,
      languages: ['javascript', 'typescript', 'python', 'java', 'go', 'rust']
    }
  }
};

/**
 * 获取 Agent 配置
 * @param agentId - Agent ID
 * @returns Agent 配置或 undefined
 */
export function getAgentConfig(agentId: string): AgentConfig | undefined {
  return AGENT_CONFIGS[agentId];
}

/**
 * 获取所有 Agent 配置
 * @returns 所有 Agent 配置数组
 */
export function getAllAgentConfigs(): AgentConfig[] {
  return Object.values(AGENT_CONFIGS);
}

/**
 * 根据类型获取 Agent 配置
 * @param agentType - Agent 类型
 * @returns 匹配的 Agent 配置数组
 */
export function getAgentConfigsByType(agentType: string): AgentConfig[] {
  return Object.values(AGENT_CONFIGS).filter(config => config.type === agentType);
}

/**
 * 检查 Agent 是否支持特定功能
 * @param agentId - Agent ID
 * @param capability - 功能名称
 * @returns 是否支持该功能
 */
export function hasCapability(agentId: string, capability: string): boolean {
  const config = getAgentConfig(agentId);
  return config?.capabilities?.includes(capability) ?? false;
}

/**
 * 获取 Agent 支持的模型列表
 * @param agentId - Agent ID
 * @returns 支持的模型列表
 */
export function getSupportedModels(agentId: string): string[] {
  const config = getAgentConfig(agentId);
  return config?.supportedModels ?? [];
}

/**
 * 验证 Agent 配置
 * @param config - Agent 配置
 * @returns 验证结果
 */
export function validateAgentConfig(config: AgentConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!config.id) {
    errors.push('Agent ID 不能为空');
  }

  if (!config.name) {
    errors.push('Agent 名称不能为空');
  }

  if (!config.type) {
    errors.push('Agent 类型不能为空');
  }

  if (config.websocketUrl && !isValidWebSocketUrl(config.websocketUrl)) {
    errors.push('WebSocket URL 格式无效');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证 WebSocket URL 格式
 * @param url - WebSocket URL
 * @returns 是否有效
 */
function isValidWebSocketUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.protocol === 'ws:' || parsedUrl.protocol === 'wss:';
  } catch {
    return false;
  }
}

/**
 * 动态注册新的 Agent 配置
 * @param config - Agent 配置
 * @returns 注册结果
 */
export function registerAgentConfig(config: AgentConfig): {
  success: boolean;
  error?: string;
} {
  const validation = validateAgentConfig(config);
  
  if (!validation.isValid) {
    return {
      success: false,
      error: validation.errors.join(', ')
    };
  }

  if (AGENT_CONFIGS[config.id]) {
    return {
      success: false,
      error: `Agent ${config.id} 已存在`
    };
  }

  AGENT_CONFIGS[config.id] = config;
  
  return { success: true };
}

/**
 * 注销 Agent 配置
 * @param agentId - Agent ID
 * @returns 是否成功注销
 */
export function unregisterAgentConfig(agentId: string): boolean {
  if (AGENT_CONFIGS[agentId]) {
    delete AGENT_CONFIGS[agentId];
    return true;
  }
  return false;
}