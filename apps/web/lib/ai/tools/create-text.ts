import { generateUUID } from '@/lib/utils';
import { type DataStreamWriter, tool } from 'ai';
import { z } from 'zod';
import { getAgentDocumentHandler } from '@/lib/agent-server';

interface CreateTextProps {
    dataStream: DataStreamWriter;
    username?: string;
    agentName?: string;
    agentVersion?: string;
}

export const createText = ({ dataStream, username, agentName, agentVersion }: CreateTextProps) =>
    tool({
        description: '创建文本内容。使用 textAgent 生成各种类型的文本文档，如邮件、报告、文章等。',
        parameters: z.object({
            title: z.string().describe('文本文档的标题'),
            description: z.string().describe('文本内容的描述或要求')
        }),
        execute: async ({ title, description }) => {
            console.log('🔧 [CREATE-TEXT-TOOL] Creating text document:', { title, description });

            const documentId = generateUUID();

            try {
                // 使用 textAgent 处理器
                const handler = getAgentDocumentHandler('text');
                if (!handler) {
                    throw new Error('Text agent handler not found');
                }

                await handler.onCreateDocument({
                    id: documentId,
                    title,
                    dataStream,
                    username,
                    agentName: agentName || 'TextAgent',
                    agentVersion: agentVersion || '0.1.0'
                });

                console.log('✅ [CREATE-TEXT-TOOL] Text document created successfully');

                return {
                    id: documentId,
                    title,
                    kind: 'text',
                    content: '文本内容已生成'
                };
            } catch (error) {
                console.error('❌ [CREATE-TEXT-TOOL] Error creating text document:', error);
                throw error;
            }
        }
    });
