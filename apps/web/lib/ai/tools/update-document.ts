import { type DataStreamWriter, tool } from 'ai';
import { z } from 'zod';
import { getDocumentById } from '@/lib/db/queries';
import { getAgentDocumentHandler } from '@/lib/agent-server';

interface UpdateDocumentProps {
    dataStream: DataStreamWriter;
}

export const updateDocument = ({ dataStream }: UpdateDocumentProps) =>
    tool({
        description: 'Update a document with the given description using the new agent architecture.',
        parameters: z.object({
            id: z.string().describe('The ID of the document to update'),
            description: z.string().describe('The description of changes that need to be made')
        }),
        execute: async ({ id, description }) => {
            console.log(`🔄 [UPDATE-DOCUMENT] Updating document:`, id);

            const document = await getDocumentById({ id });

            if (!document) {
                console.error(`❌ [UPDATE-DOCUMENT] Document not found:`, id);
                return {
                    error: 'Document not found'
                };
            }

            console.log(`📋 [UPDATE-DOCUMENT] Found document:`, {
                title: document.title,
                kind: document.kind,
                contentLength: document.content?.length || 0
            });

            dataStream.writeData({
                type: 'clear',
                content: document.title
            });

            const agentHandler = getAgentDocumentHandler(document.kind);

            if (!agentHandler) {
                throw new Error(`No agent handler found for kind: ${document.kind}`);
            }

            console.log(`📋 [UPDATE-DOCUMENT] Using ${document.kind} agent for document update`);

            await agentHandler.onUpdateDocument({
                document,
                description,
                dataStream
            });

            dataStream.writeData({ type: 'finish', content: '' });

            console.log(`✅ [UPDATE-DOCUMENT] Document update completed for ${document.kind}`);

            return {
                id,
                title: document.title,
                kind: document.kind,
                content: 'The document has been updated successfully using the new agent architecture.'
            };
        }
    });
