import { generateUUID } from '@/lib/utils';
import { type DataStreamWriter, tool } from 'ai';
import { z } from 'zod';
import { artifactKinds, getAgentDocumentHandler } from '@/lib/agent-server';

interface CreateDocumentProps {
    dataStream: DataStreamWriter;
    username?: string;
    agentName?: string;
    agentVersion?: string;
}

export const createDocument = ({ dataStream, username, agentName, agentVersion }: CreateDocumentProps) =>
    tool({
        description:
            'Create a document for a writing or content creation activities. This tool will use the new agent architecture to generate the contents of the document based on the title and kind.',
        parameters: z.object({
            title: z.string(),
            kind: z.enum(artifactKinds)
        }),
        execute: async ({ title, kind }) => {
            console.log(`🚀 [CREATE-DOCUMENT] Creating ${kind} document:`, title);

            const id = generateUUID();

            dataStream.writeData({
                type: 'kind',
                content: kind
            });

            dataStream.writeData({
                type: 'id',
                content: id
            });

            dataStream.writeData({
                type: 'title',
                content: title
            });

            dataStream.writeData({
                type: 'clear',
                content: ''
            });

            const agentHandler = getAgentDocumentHandler(kind);

            if (!agentHandler) {
                throw new Error(`No agent handler found for kind: ${kind}`);
            }

            console.log(`📋 [CREATE-DOCUMENT] Using ${kind} agent for document generation`);

            await agentHandler.onCreateDocument({
                id,
                title,
                dataStream,
                username,
                agentName,
                agentVersion
            });

            dataStream.writeData({ type: 'finish', content: '' });

            console.log(`✅ [CREATE-DOCUMENT] Document creation completed for ${kind}`);

            return {
                id,
                title,
                kind,
                content: 'A document was created using the new agent architecture and is now visible to the user.'
            };
        }
    });
