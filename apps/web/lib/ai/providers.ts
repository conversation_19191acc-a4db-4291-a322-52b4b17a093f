import { customProvider, extractReasoningMiddleware, wrapLanguageModel } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { isTestEnvironment } from '../constants';

// 创建自定义OpenAI实例
const customOpenAI = createOpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL
});

// 测试环境的模拟模型
const mockModel = customOpenAI('gpt-4o-mini');

export const myProvider = isTestEnvironment
    ? customProvider({
          languageModels: {
              // Agent 模型映射 (测试环境)
              'gpt-4': mockModel,
              'gpt-4o-2024-11-20': mockModel,
              'dall-e-3': mockModel,

              // 原有配置
              'chat-model': mockModel,
              'chat-model-reasoning': mockModel,
              'title-model': mockModel,
              'artifact-model': mockModel
          }
      })
    : customProvider({
          languageModels: {
              // OpenAI 模型
              'gpt-4o-2024-11-20': customOpenAI('gpt-4o-2024-11-20'),
              'gpt-4o-mini': customOpenAI('gpt-4o-mini'),
              'gpt-4.1': customOpenAI('gpt-4o-2024-11-20'), // 映射到实际的GPT-4o模型
              'gpt-4.1-mini': customOpenAI('gpt-4o-mini'),

              // Anthropic 模型 (暂时映射到OpenAI，后续可以添加Anthropic提供者)
              'anthropic.claude-3.5-sonnet': customOpenAI('gpt-4o-2024-11-20'),
              'anthropic.claude-3.5-sonnet-v2': customOpenAI('gpt-4o-2024-11-20'),
              'bedrock.claude-3.5-sonnet': customOpenAI('gpt-4o-2024-11-20'),
              'bedrock.claude-3.5-sonnet-v2': customOpenAI('gpt-4o-2024-11-20'),

              // DeepSeek 模型 (暂时映射到OpenAI，后续可以添加DeepSeek提供者)
              'deepseek-chat': customOpenAI('gpt-4o-2024-11-20'),
              'deepseek-coder': customOpenAI('gpt-4o-2024-11-20'),
              'deepseek-r1-huawei': customOpenAI('gpt-4o-2024-11-20'),
              'Doubao-deepseek-r1': customOpenAI('gpt-4o-2024-11-20'),

              // Agent 模型映射 (用于各种 Agent 的默认模型)
              'gpt-4': customOpenAI('gpt-4o-2024-11-20'), // CodeAgent, TextAgent, SheetAgent 默认模型
              'dall-e-3': customOpenAI('gpt-4o-2024-11-20'), // ImageAgent 默认模型 (暂时映射到文本模型)

              // 保留原有配置以兼容
              'chat-model': customOpenAI('gpt-4o-2024-11-20'),
              'chat-model-reasoning': wrapLanguageModel({
                  model: customOpenAI('gpt-4o-2024-11-20'),
                  middleware: extractReasoningMiddleware({ tagName: 'think' })
              }),
              'title-model': customOpenAI('gpt-4o-2024-11-20'),
              'artifact-model': customOpenAI('gpt-4o-2024-11-20')
          }
      });
