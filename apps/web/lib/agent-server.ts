import type { DataStreamWriter } from 'ai';
import { saveDocument } from '@/lib/db/queries';
import type { ArtifactKind } from '@/components/artifact';
import type { Document } from '@/lib/db/schema';
import { myProvider } from '@/lib/ai/providers';

export const artifactKinds = ['text', 'code', 'image', 'sheet', 'mario'] as const;

export interface CreateDocumentCallbackProps {
    id: string;
    title: string;
    dataStream: DataStreamWriter;
    username?: string;
    agentName?: string;
    agentVersion?: string;
}

export interface UpdateDocumentCallbackProps {
    document: Document;
    description: string;
    dataStream: DataStreamWriter;
}

export interface AgentDocumentHandler<T extends ArtifactKind> {
    kind: T;
    agent: any; // Agent 实例
    onCreateDocument: (params: CreateDocumentCallbackProps) => Promise<void>;
    onUpdateDocument: (params: UpdateDocumentCallbackProps) => Promise<void>;
}

// 通用的 Agent 文档处理器创建函数
function createAgentDocumentHandler<T extends ArtifactKind>(config: {
    kind: T;
    generateContent: (prompt: string, dataStream?: DataStreamWriter, kind?: string) => Promise<string>;
}): AgentDocumentHandler<T> {
    return {
        kind: config.kind,
        agent: null, // 不再使用 agent 实例
        onCreateDocument: async (args: CreateDocumentCallbackProps) => {
            console.log(`🚀 [AGENT-SERVER] Creating ${config.kind} document:`, args.title);

            try {
                // 使用流式生成内容
                const content = await config.generateContent(args.title, args.dataStream, config.kind);

                // 注意：finish事件已经在generateContent函数中发送了，这里不需要重复发送

                // 保存文档
                await saveDocument({
                    id: args.id,
                    title: args.title,
                    content: content,
                    kind: config.kind,
                    username: args.username,
                    agentName: args.agentName,
                    agentVersion: args.agentVersion
                });

                console.log(`✅ [AGENT-SERVER] ${config.kind} document created successfully`);
            } catch (error: any) {
                console.error(`❌ [AGENT-SERVER] ${config.kind} creation failed:`, error);
                args.dataStream.writeData({
                    type: 'error',
                    content: `Failed to create ${config.kind}: ${error.message}`
                });
                throw error;
            }
        },
        onUpdateDocument: async (args: UpdateDocumentCallbackProps) => {
            console.log(`🔄 [AGENT-SERVER] Updating ${config.kind} document:`, args.document.title);

            try {
                // 构建更新提示
                const updatePrompt = `Current content: ${args.document.content}\n\nUpdate request: ${args.description}`;

                // 使用流式生成内容
                const content = await config.generateContent(updatePrompt, args.dataStream, config.kind);

                // 注意：finish事件已经在generateContent函数中发送了，这里不需要重复发送

                // 保存更新的文档
                await saveDocument({
                    id: args.document.id,
                    title: args.document.title,
                    content: content,
                    kind: config.kind
                });

                console.log(`✅ [AGENT-SERVER] ${config.kind} document updated successfully`);
            } catch (error: any) {
                console.error(`❌ [AGENT-SERVER] ${config.kind} update failed:`, error);
                args.dataStream.writeData({
                    type: 'error',
                    content: `Failed to update ${config.kind}: ${error.message}`
                });
                throw error;
            }
        }
    };
}

// 内容生成函数
async function generateCodeContent(prompt: string, dataStream?: DataStreamWriter, kind?: string): Promise<string> {
    // 设置artifact的基本信息
    if (dataStream && kind) {
        dataStream.writeData({
            type: 'kind',
            content: kind
        });
        dataStream.writeData({
            type: 'title',
            content: 'Python代码'
        });
    }

    try {
        const { streamObject } = await import('ai');
        const { z } = await import('zod');

        const codeSchema = z.object({
            code: z.string().describe('Generated Python code')
        });

        const result = await streamObject({
            model: myProvider.languageModel('gpt-4o-2024-11-20'),
            system: 'You are a Python code generator. Generate clean, well-commented Python code based on the user request.',
            prompt: prompt,
            schema: codeSchema
        });

        let finalCode = '';
        let previousCode = '';
        for await (const partialObject of result.partialObjectStream) {
            if (partialObject.code) {
                finalCode = partialObject.code;

                // 计算增量内容
                const deltaContent = finalCode.slice(previousCode.length);

                // 发送增量数据
                if (dataStream && kind && deltaContent) {
                    dataStream.writeData({
                        type: `${kind}-delta`,
                        content: deltaContent,
                        isComplete: false
                    });
                }

                previousCode = finalCode;
            }
        }

        // 发送完成标记
        if (dataStream && kind) {
            dataStream.writeData({
                type: `${kind}-delta`,
                content: '',
                isComplete: true
            });
            // 发送finish事件
            dataStream.writeData({
                type: 'finish',
                content: ''
            });
        }

        return finalCode || generateMockCode(prompt);
    } catch (error) {
        console.error('Error generating code:', error);
        const mockContent = generateMockCode(prompt);

        // 即使是 mock 内容也要发送流式数据
        if (dataStream && kind) {
            dataStream.writeData({
                type: `${kind}-delta`,
                content: mockContent,
                isComplete: true
            });
            // 发送finish事件
            dataStream.writeData({
                type: 'finish',
                content: ''
            });
        }

        return mockContent;
    }
}

async function generateTextContent(prompt: string, dataStream?: DataStreamWriter, kind?: string): Promise<string> {
    // 设置artifact的基本信息
    if (dataStream && kind) {
        dataStream.writeData({
            type: 'kind',
            content: kind
        });
        dataStream.writeData({
            type: 'title',
            content: '生成的文本'
        });
    }

    try {
        const { streamText } = await import('ai');

        const result = await streamText({
            model: myProvider.languageModel('gpt-4o-2024-11-20'),
            system: 'You are a professional writer. Generate high-quality text content based on the user request.',
            prompt: prompt,
            temperature: 0.7,
            maxTokens: 4000
        });

        let finalText = '';
        for await (const delta of result.textStream) {
            finalText += delta;

            // 发送增量数据，而不是累积数据
            if (dataStream && kind) {
                dataStream.writeData({
                    type: `${kind}-delta`,
                    content: delta, // 发送增量内容
                    isComplete: false
                });
            }
        }

        // 发送完成标记
        if (dataStream && kind) {
            dataStream.writeData({
                type: `${kind}-delta`,
                content: '',
                isComplete: true
            });
            // 发送finish事件
            dataStream.writeData({
                type: 'finish',
                content: ''
            });
        }

        return finalText || generateMockText(prompt);
    } catch (error) {
        console.error('Error generating text:', error);
        const mockContent = generateMockText(prompt);

        // 即使是 mock 内容也要发送流式数据
        if (dataStream && kind) {
            dataStream.writeData({
                type: `${kind}-delta`,
                content: mockContent,
                isComplete: true
            });
            // 发送finish事件
            dataStream.writeData({
                type: 'finish',
                content: ''
            });
        }

        return mockContent;
    }
}

async function generateImageContent(prompt: string, dataStream?: DataStreamWriter, kind?: string): Promise<string> {
    // 设置artifact的基本信息
    if (dataStream && kind) {
        dataStream.writeData({
            type: 'kind',
            content: kind
        });
        dataStream.writeData({
            type: 'title',
            content: '生成的图片'
        });
    }

    // 图片生成的占位符实现
    const content = `# 图片生成请求

**提示词：** ${prompt}

**状态：** 图片生成功能正在开发中

**说明：** 此功能将集成图片生成AI模型来创建图片内容。`;

    // 发送流式数据
    if (dataStream && kind) {
        dataStream.writeData({
            type: `${kind}-delta`,
            content: content,
            isComplete: true
        });
        // 发送finish事件
        dataStream.writeData({
            type: 'finish',
            content: ''
        });
    }

    return content;
}

async function generateSheetContent(prompt: string, dataStream?: DataStreamWriter, kind?: string): Promise<string> {
    // 设置artifact的基本信息
    if (dataStream && kind) {
        dataStream.writeData({
            type: 'kind',
            content: kind
        });
        dataStream.writeData({
            type: 'title',
            content: '生成的表格'
        });
    }

    // 表格生成的占位符实现
    const content = `名称,类型,数量,价格
产品A,电子产品,10,299.99
产品B,书籍,25,19.99
产品C,服装,15,89.99
产品D,食品,50,9.99`;

    // 发送流式数据
    if (dataStream && kind) {
        dataStream.writeData({
            type: `${kind}-delta`,
            content: content,
            isComplete: true
        });
        // 发送finish事件
        dataStream.writeData({
            type: 'finish',
            content: ''
        });
    }

    return content;
}

// Mock 内容生成函数
function generateMockCode(prompt: string): string {
    if (prompt.toLowerCase().includes('plot') || prompt.toLowerCase().includes('graph')) {
        return `import matplotlib.pyplot as plt
import numpy as np

# 生成示例数据
x = np.linspace(0, 10, 100)
y = np.sin(x)

# 创建图表
plt.figure(figsize=(10, 6))
plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
plt.xlabel('X轴')
plt.ylabel('Y轴')
plt.title('正弦函数图表')
plt.legend()
plt.grid(True)
plt.show()

print("图表已生成")`;
    }

    return `# Python 代码示例
def main():
    print("Hello, World!")

    # 示例计算
    numbers = [1, 2, 3, 4, 5]
    total = sum(numbers)
    average = total / len(numbers)

    print(f"数字列表: {numbers}")
    print(f"总和: {total}")
    print(f"平均值: {average}")

    return average

if __name__ == "__main__":
    result = main()
    print(f"Result: {result}")
`;
}

function generateMockText(prompt: string): string {
    if (prompt.toLowerCase().includes('email') || prompt.toLowerCase().includes('邮件')) {
        return `主题：关于项目进展的更新

亲爱的团队成员，

希望这封邮件能找到您一切安好。我想向大家更新一下我们当前项目的进展情况。

项目进展：
- 已完成需求分析和设计阶段
- 开发工作正在按计划进行
- 预计在下周完成第一个里程碑

如果您有任何问题或建议，请随时与我联系。

最好的祝愿，
[您的姓名]`;
    }

    return `# 欢迎使用文本生成助手

这是一个由AI驱动的文本生成工具，可以帮助您创建各种类型的内容。

## 功能特点

- **智能文本生成**：根据您的需求生成高质量的文本内容
- **多种文本类型**：支持邮件、报告、文章、创意写作等
- **个性化定制**：根据您的具体要求调整内容风格和结构

让我们开始创作吧！请告诉我您需要什么类型的文本内容。`;
}

// 创建各种 Agent 文档处理器
export const agentDocumentHandlers: Array<AgentDocumentHandler<ArtifactKind>> = [
    createAgentDocumentHandler({
        kind: 'text',
        generateContent: generateTextContent
    }),
    createAgentDocumentHandler({
        kind: 'code',
        generateContent: generateCodeContent
    }),
    createAgentDocumentHandler({
        kind: 'image',
        generateContent: generateImageContent
    }),
    createAgentDocumentHandler({
        kind: 'sheet',
        generateContent: generateSheetContent
    })
];

// 根据 kind 获取对应的处理器
export function getAgentDocumentHandler(kind: ArtifactKind): AgentDocumentHandler<ArtifactKind> | undefined {
    return agentDocumentHandlers.find((handler) => handler.kind === kind);
}
