import { drizzle } from 'drizzle-orm/mysql2';
import { and, asc, count, desc, eq, gte } from 'drizzle-orm';
import * as mysql from 'mysql2/promise';

import {
    chat,
    document,
    type Suggestion,
    suggestion,
    message,
    type DBMessage,
    type Chat,
    stream
} from './schema';
import { generateUUID } from '../utils';
import { ChatSDKError } from '../errors';
import { dbConfig } from './config';

// 创建MySQL连接池
const connection = mysql.createPool(dbConfig);

// 创建Drizzle实例
const db = drizzle(connection);

// 初始化数据库表结构
async function initializeDatabase() {
    try {
        // 这里可以添加表创建逻辑，或者使用drizzle-kit进行迁移
        console.log('Database initialized');
    } catch (error) {
        console.error('Database initialization error:', error);
    }
}

// 在模块加载时初始化数据库
initializeDatabase();

/**
 * 保存聊天记录
 * 移除了visibility参数，所有聊天通过UUID都可以被访问
 * @param id - 聊天UUID
 * @param title - 聊天标题
 * @param username - 创建者用户名
 */
export async function saveChat({
    id,
    title,
    username
}: {
    id: string;
    title: string;
    username?: string;
}) {
    try {
        console.log('💾 [DEBUG] saveChat - 保存chat记录:', { id, title, username });

        // 使用事务确保数据一致性
        const result = await db.transaction(async (tx) => {
            const insertResult = await tx.insert(chat).values({
                id,
                title,
                username,
                createdAt: new Date()
            });

            // 立即验证插入是否成功
            const [verifyResult] = await tx.select().from(chat).where(eq(chat.id, id));
            if (!verifyResult) {
                throw new Error('Failed to verify chat insertion');
            }

            console.log('💾 [DEBUG] saveChat - 事务内验证成功:', verifyResult.id);
            return verifyResult;
        });

        console.log('💾 [DEBUG] saveChat - 事务提交成功:', id);
        return result;
    } catch (error) {
        console.error('Save chat error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to save chat');
    }
}

export async function deleteChatById({ id }: { id: string }) {
    try {
        // 先获取要删除的聊天记录
        const chatToDelete = await getChatById({ id });

        await db.delete(message).where(eq(message.chatId, id));
        await db.delete(stream).where(eq(stream.chatId, id));

        await db.delete(chat).where(eq(chat.id, id));

        return chatToDelete;
    } catch (error) {
        console.error('Delete chat error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to delete chat by id');
    }
}

export async function getChatsByUserId({ id }: { id: string }): Promise<Array<Chat>> {
    try {
        // 根据真实用户ID过滤聊天记录，同时处理username为NULL的情况
        return await db.select().from(chat).where(eq(chat.username, id)).orderBy(desc(chat.createdAt));
    } catch (error) {
        console.error('Get chats error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get chats by user id');
    }
}

export async function getChatById({ id }: { id: string }) {
    try {
        console.log('🔍 [DEBUG] getChatById - 查询chat ID:', id);
        const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
        console.log('🔍 [DEBUG] getChatById - 查询结果:', selectedChat ? '找到记录' : '未找到记录');
        if (selectedChat) {
            console.log('🔍 [DEBUG] getChatById - 记录详情:', {
                id: selectedChat.id,
                username: selectedChat.username,
                title: selectedChat.title,
                createdAt: selectedChat.createdAt
            });
        }
        return selectedChat;
    } catch (error) {
        console.error('Get chat by id error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get chat by id');
    }
}

/**
 * 保存消息列表
 * 支持新的metadata字段和头像信息
 * @param messages - 消息数组
 */
export async function saveMessages({ messages }: { messages: Array<DBMessage> }) {
    try {
        await db.insert(message).values(messages);
        return messages;
    } catch (error) {
        console.error('Save messages error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to save messages');
    }
}

/**
 * 保存单条消息
 * @param messageData - 消息数据
 */
export async function saveMessage(messageData: {
    id: string;
    chatId: string;
    role: string;
    parts: any;
    attachments: any;
    userAvatar?: string;
    assistantAvatar?: string;
    modelId?: string;
    metadata?: any;
}) {
    try {
        await db.insert(message).values({
            ...messageData,
            createdAt: new Date()
        });
        
        // 获取刚插入的消息
        const savedMessage = await db.select().from(message).where(eq(message.id, messageData.id)).limit(1);
        return savedMessage[0];
    } catch (error) {
        console.error('Save message error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to save message');
    }
}

/**
 * 获取聊天的所有消息
 * @param id - 聊天ID
 */
export async function getMessagesByChatId({ id }: { id: string }) {
    try {
        return await db.select().from(message).where(eq(message.chatId, id)).orderBy(asc(message.createdAt));
    } catch (error) {
        console.error('Get messages error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get messages by chat id');
    }
}

/**
 * 通过UUID获取公开消息
 * 任何人都可以通过有效的UUID访问消息
 * @param id - 消息UUID
 */
export async function getPublicMessageById({ id }: { id: string }) {
    try {
        const [selectedMessage] = await db.select().from(message).where(eq(message.id, id));
        return selectedMessage;
    } catch (error) {
        console.error('Get public message by id error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get public message by id');
    }
}

/**
 * 获取消息详情（保持向后兼容）
 * @param id - 消息ID
 */
export async function getMessageById({ id }: { id: string }) {
    try {
        const [selectedMessage] = await db.select().from(message).where(eq(message.id, id));
        return selectedMessage;
    } catch (error) {
        console.error('Get message by id error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get message by id');
    }
}

/**
 * 通过UUID获取公开聊天的消息列表
 * 任何人都可以通过有效的聊天UUID访问其消息
 * @param chatId - 聊天UUID
 */
export async function getPublicMessagesByChatId({ chatId }: { chatId: string }) {
    try {
        return await db.select().from(message).where(eq(message.chatId, chatId)).orderBy(asc(message.createdAt));
    } catch (error) {
        console.error('Get public messages by chat id error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get public messages by chat id');
    }
}

export async function deleteMessagesByChatIdAfterTimestamp({ chatId, timestamp }: { chatId: string; timestamp: Date }) {
    try {
        await db.delete(message).where(and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)));
    } catch (error) {
        console.error('Delete messages error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to delete messages by chat id after timestamp');
    }
}

/**
 * 通过UUID获取公开聊天记录
 * 任何人都可以通过有效的UUID访问聊天记录
 * @param id - 聊天UUID
 */
export async function getPublicChatById({ id }: { id: string }) {
    try {
        console.log('🔍 [DEBUG] getPublicChatById - 查询公开chat ID:', id);
        const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
        console.log('🔍 [DEBUG] getPublicChatById - 查询结果:', selectedChat ? '找到记录' : '未找到记录');
        return selectedChat;
    } catch (error) {
        console.error('Get public chat by id error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get public chat by id');
    }
}

/**
 * 获取最近的公开聊天记录
 * @param limit - 限制返回数量，默认20
 */
export async function getRecentPublicChats({ limit = 20 }: { limit?: number } = {}) {
    try {
        return await db.select().from(chat).orderBy(desc(chat.createdAt)).limit(limit);
    } catch (error) {
        console.error('Get recent public chats error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get recent public chats');
    }
}

export async function getMessageCountByUserId({ id }: { id: string }) {
    try {
        // 根据用户ID统计消息数量 - 通过chat表关联
        const [result] = await db
            .select({ count: count() })
            .from(message)
            .innerJoin(chat, eq(message.chatId, chat.id))
            .where(eq(chat.username, id));
        return result?.count || 0;
    } catch (error) {
        console.error('Get message count error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get message count by user id');
    }
}

/**
 * 获取用户最近的消息数量（用于速率限制）
 * @param id 用户ID
 * @param hours 小时数，默认24小时
 * @returns 最近指定小时内的消息数量
 */
export async function getRecentMessageCountByUserId({ id, hours = 24 }: { id: string; hours?: number }) {
    try {
        const hoursAgo = new Date();
        hoursAgo.setHours(hoursAgo.getHours() - hours);

        // 根据用户ID统计最近消息数量 - 通过chat表关联
        const [result] = await db
            .select({ count: count() })
            .from(message)
            .innerJoin(chat, eq(message.chatId, chat.id))
            .where(and(eq(chat.username, id), gte(message.createdAt, hoursAgo)));
        return result?.count || 0;
    } catch (error) {
        console.error('Get recent message count error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get recent message count by user id');
    }
}



export async function getDocumentById({ id }: { id: string }) {
    try {
        const [selectedDocument] = await db.select().from(document).where(eq(document.id, id));
        return selectedDocument;
    } catch (error) {
        console.error('Get document error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get document by id');
    }
}

export async function getDocumentsById({ id }: { id: string }) {
    try {
        return await db.select().from(document).where(eq(document.id, id)).orderBy(asc(document.createdAt));
    } catch (error) {
        console.error('Get documents error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get documents by id');
    }
}

export async function saveDocument({
    id,
    title,
    content,
    kind,
    username,
    agentName,
    agentVersion
}: {
    id: string;
    title: string;
    content: string;
    kind: ArtifactKind;
    username?: string;
    agentName?: string;
    agentVersion?: string;
}) {
    try {
        await db.insert(document).values({
            id,
            title,
            content,
            kind,
            username,
            agentName,
            agentVersion,
            createdAt: new Date()
        });

        return { id, title, content, kind, username, agentName, agentVersion, createdAt: new Date() };
    } catch (error) {
        console.error('Save document error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to save document');
    }
}

export async function deleteDocumentsByIdAfterTimestamp({ id, timestamp }: { id: string; timestamp: Date }) {
    try {
        await db.delete(document).where(and(eq(document.id, id), gte(document.createdAt, timestamp)));
    } catch (error) {
        console.error('Delete documents error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to delete documents by id after timestamp');
    }
}

export async function updateDocument({ id, title, content }: { id: string; title: string; content: string }) {
    try {
        await db
            .update(document)
            .set({
                title,
                content
            })
            .where(eq(document.id, id));

        const updatedDocument = await getDocumentById({ id });
        return updatedDocument;
    } catch (error) {
        console.error('Update document error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to update document');
    }
}

export async function getSuggestionsByDocumentId({ documentId }: { documentId: string }) {
    try {
        return await db
            .select()
            .from(suggestion)
            .where(eq(suggestion.documentId, documentId))
            .orderBy(asc(suggestion.createdAt));
    } catch (error) {
        console.error('Get suggestions error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get suggestions by document id');
    }
}

export async function saveSuggestions({ suggestions }: { suggestions: Array<Suggestion> }) {
    try {
        await db.insert(suggestion).values(suggestions);
        return suggestions;
    } catch (error) {
        console.error('Save suggestions error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to save suggestions');
    }
}

/**
 * 创建新的聊天流
 * @param chatId - 聊天ID
 * @param status - 流状态，默认为'active'
 * @param metadata - 流的元数据
 */
export async function createStreamId({ 
    chatId, 
    status = 'active', 
    metadata 
}: { 
    chatId: string; 
    status?: string; 
    metadata?: any; 
}) {
    try {
        const streamId = generateUUID();
        await db.insert(stream).values({
            id: streamId,
            chatId,
            status,
            metadata,
            createdAt: new Date()
        });
        return streamId;
    } catch (error) {
        console.error('Create stream id error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to create stream id');
    }
}

/**
 * 更新流状态
 * @param streamId - 流ID
 * @param status - 新状态
 * @param metadata - 更新的元数据
 */
export async function updateStreamStatus({ 
    streamId, 
    status, 
    metadata 
}: { 
    streamId: string; 
    status: string; 
    metadata?: any; 
}) {
    try {
        const updateData: any = { status };
        if (metadata !== undefined) {
            updateData.metadata = metadata;
        }
        
        await db.update(stream).set(updateData).where(eq(stream.id, streamId));
    } catch (error) {
        console.error('Update stream status error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to update stream status');
    }
}

/**
 * 获取聊天的所有流ID
 * @param chatId - 聊天ID
 */
export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
    try {
        const streams = await db.select().from(stream).where(eq(stream.chatId, chatId)).orderBy(desc(stream.createdAt));
        return streams.map((s: { id: string }) => s.id);
    } catch (error) {
        console.error('Get stream ids error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get stream ids by chat id');
    }
}

/**
 * 获取流的详细信息
 * @param streamId - 流ID
 */
export async function getStreamById({ streamId }: { streamId: string }) {
    try {
        const [selectedStream] = await db.select().from(stream).where(eq(stream.id, streamId));
        return selectedStream;
    } catch (error) {
        console.error('Get stream by id error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get stream by id');
    }
}

/**
 * 获取活跃的流
 * @param chatId - 聊天ID（可选）
 */
export async function getActiveStreams({ chatId }: { chatId?: string } = {}) {
    try {
        let whereCondition = eq(stream.status, 'active');
        
        if (chatId) {
            whereCondition = and(eq(stream.status, 'active'), eq(stream.chatId, chatId));
        }
        
        return await db.select().from(stream)
            .where(whereCondition)
            .orderBy(desc(stream.createdAt));
    } catch (error) {
        console.error('Get active streams error:', error);
        throw new ChatSDKError('bad_request:database', 'Failed to get active streams');
    }
}
