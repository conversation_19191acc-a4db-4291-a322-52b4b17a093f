CREATE TABLE `Chat` (
	`id` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`title` text NOT NULL,
	`username` varchar(255),
	CONSTRAINT `Chat_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `Document` (
	`id` varchar(255) NOT NULL,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	`title` text NOT NULL,
	`content` text,
	`kind` varchar(50) NOT NULL DEFAULT 'text',
	`username` varchar(255),
	`agentName` varchar(100),
	`agentVersion` varchar(50),
	CONSTRAINT `Document_id_createdAt_pk` PRIMARY KEY(`id`,`createdAt`)
);
--> statement-breakpoint
CREATE TABLE `Message` (
	`id` varchar(255) NOT NULL,
	`chatId` varchar(255) NOT NULL,
	`role` varchar(50) NOT NULL,
	`parts` json NOT NULL,
	`attachments` json NOT NULL,
	`avatar` varchar(500),
	`modelId` varchar(100),
	`metadata` json,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `Message_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `Stream` (
	`id` varchar(255) NOT NULL,
	`chatId` varchar(255) NOT NULL,
	`status` varchar(50) NOT NULL DEFAULT 'active',
	`metadata` json,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `Stream_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `Suggestion` (
	`id` varchar(255) NOT NULL,
	`documentId` varchar(255) NOT NULL,
	`documentCreatedAt` timestamp NOT NULL,
	`originalText` text NOT NULL,
	`suggestedText` text NOT NULL,
	`description` text,
	`isResolved` boolean NOT NULL DEFAULT false,
	`createdAt` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `Suggestion_id` PRIMARY KEY(`id`)
);

--> statement-breakpoint
CREATE INDEX `idx_chat_username` ON `Chat` (`username`);--> statement-breakpoint
CREATE INDEX `idx_chat_createdAt` ON `Chat` (`createdAt`);--> statement-breakpoint
CREATE INDEX `idx_username` ON `Document` (`username`);--> statement-breakpoint
CREATE INDEX `idx_agentName` ON `Document` (`agentName`);--> statement-breakpoint
CREATE INDEX `idx_message_chatId` ON `Message` (`chatId`);--> statement-breakpoint
CREATE INDEX `idx_message_createdAt` ON `Message` (`createdAt`);--> statement-breakpoint
CREATE INDEX `idx_message_role` ON `Message` (`role`);--> statement-breakpoint
CREATE INDEX `idx_stream_chatId` ON `Stream` (`chatId`);--> statement-breakpoint
CREATE INDEX `idx_stream_status` ON `Stream` (`status`);--> statement-breakpoint
CREATE INDEX `idx_stream_createdAt` ON `Stream` (`createdAt`);--> statement-breakpoint
CREATE INDEX `idx_suggestion_documentId` ON `Suggestion` (`documentId`);--> statement-breakpoint
CREATE INDEX `idx_suggestion_isResolved` ON `Suggestion` (`isResolved`);--> statement-breakpoint
CREATE INDEX `idx_suggestion_createdAt` ON `Suggestion` (`createdAt`);