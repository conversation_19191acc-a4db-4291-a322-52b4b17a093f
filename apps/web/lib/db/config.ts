// 手动加载环境变量以确保在所有环境中都能正确加载
import { config } from 'dotenv';

// 检查是否在服务器端环境
if (typeof window === 'undefined') {
    // 只在服务器端导入 Node.js 模块
    const { fileURLToPath } = await import('url');
    const { dirname, join } = await import('path');
    const { existsSync } = await import('fs');

    // 获取当前文件的目录
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);

    // 加载环境变量
    const projectRoot = join(__dirname, '../..');
    const baseEnvPath = join(projectRoot, '.env');
    if (existsSync(baseEnvPath)) {
        config({ path: baseEnvPath });
    }

    // 根据NODE_ENV加载对应的环境文件
    const nodeEnv = process.env.NODE_ENV || 'development';
    const envFile = nodeEnv === 'production' ? '.env.production' : nodeEnv === 'test' ? '.env.test' : '.env.development';
    const envPath = join(projectRoot, envFile);
    if (existsSync(envPath)) {
        config({ path: envPath, override: false });
    }
}

// 从环境变量解析数据库配置
function parseDatabaseUrl(url: string) {
    const match = url.match(/mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
    if (!match) {
        throw new Error('Invalid DATABASE_URL format');
    }

    const [, user, password, host, port, database] = match;
    return {
        host: host!,
        port: parseInt(port!, 10),
        user: user!,
        password: password!,
        database: database!
    };
}

const databaseUrl = process.env.DATABASE_URL;
if (!databaseUrl) {
    console.error('❌ DATABASE_URL environment variable is required');
    console.error(
        'Available env vars:',
        Object.keys(process.env).filter((key) => key.includes('DATABASE'))
    );
    throw new Error('DATABASE_URL environment variable is required');
}

const dbCredentials = parseDatabaseUrl(databaseUrl);

export const dbConfig = {
    ...dbCredentials,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    timezone: '+08:00' // 设置为中国标准时间
};
