import type { InferSelectModel } from 'drizzle-orm';
import { mysqlTable, varchar, text, timestamp, json, primaryKey, boolean, index } from 'drizzle-orm/mysql-core';

export const chat = mysqlTable(
  'Chat',
  {
    id: varchar('id', { length: 255 }).primaryKey().notNull(),
    createdAt: timestamp('createdAt').notNull().defaultNow(),
    title: text('title').notNull(),
    visibility: varchar('visibility', { length: 50 }).$type<'public' | 'private'>().notNull().default('public'),
    username: varchar('username', { length: 255 })
  },
  (table) => ({
    usernameIdx: index('idx_chat_username').on(table.username)
  })
);

export type Chat = InferSelectModel<typeof chat>;

export const message = mysqlTable(
  'Message',
  {
    id: varchar('id', { length: 255 }).primaryKey().notNull(),
    chatId: varchar('chatId', { length: 255 }).notNull(),
    role: varchar('role', { length: 50 }).notNull(),
    parts: json('parts').notNull(),
    attachments: json('attachments').notNull(),
    userAvatar: varchar('userAvatar', { length: 500 }), // 用户头像URL
    assistantAvatar: varchar('assistantAvatar', { length: 500 }), // AI助手头像URL
    modelId: varchar('modelId', { length: 100 }), // 使用的模型ID
    createdAt: timestamp('createdAt').notNull().defaultNow()
  },
  (table) => ({
    chatIdIdx: index('idx_chatId').on(table.chatId),
    createdAtIdx: index('idx_createdAt').on(table.createdAt)
  })
);

export type DBMessage = InferSelectModel<typeof message>;

// 扩展的消息类型，包含头像信息
export type DBMessageWithAvatars = DBMessage & {
  userAvatar?: string;
  assistantAvatar?: string;
  modelId?: string;
};

export const vote = mysqlTable(
  'Vote',
  {
    chatId: varchar('chatId', { length: 255 }).notNull(),
    messageId: varchar('messageId', { length: 255 }).notNull(),
    isUpvoted: boolean('isUpvoted').notNull()
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] })
    };
  }
);

export type Vote = InferSelectModel<typeof vote>;

export const document = mysqlTable(
  'Document',
  {
    id: varchar('id', { length: 255 }).notNull(),
    createdAt: timestamp('createdAt').notNull().defaultNow(),
    title: text('title').notNull(),
    content: text('content'),
    kind: varchar('kind', { length: 50 })
      .$type<'text' | 'code' | 'image' | 'sheet' | 'mario'>()
      .notNull()
      .default('text'),
    username: varchar('username', { length: 255 }),
    agentName: varchar('agentName', { length: 100 }),
    agentVersion: varchar('agentVersion', { length: 50 })
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] }),
      usernameIdx: index('idx_username').on(table.username),
      agentNameIdx: index('idx_agentName').on(table.agentName)
    };
  }
);

export type Document = InferSelectModel<typeof document>;

export const suggestion = mysqlTable(
  'Suggestion',
  {
    id: varchar('id', { length: 255 }).primaryKey().notNull(),
    documentId: varchar('documentId', { length: 255 }).notNull(),
    documentCreatedAt: timestamp('documentCreatedAt').notNull(),
    originalText: text('originalText').notNull(),
    suggestedText: text('suggestedText').notNull(),
    description: text('description'),
    isResolved: boolean('isResolved').notNull().default(false),
    createdAt: timestamp('createdAt').notNull().defaultNow()
  },
  (table) => ({
    documentIdIdx: index('idx_documentId').on(table.documentId)
  })
);

export type Suggestion = InferSelectModel<typeof suggestion>;

export const stream = mysqlTable(
  'Stream',
  {
    id: varchar('id', { length: 255 }).primaryKey().notNull(),
    chatId: varchar('chatId', { length: 255 }).notNull(),
    createdAt: timestamp('createdAt').notNull().defaultNow()
  },
  (table) => ({
    chatIdIdx: index('idx_chatId').on(table.chatId)
  })
);

export type Stream = InferSelectModel<typeof stream>;
