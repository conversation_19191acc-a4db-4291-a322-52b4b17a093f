/**
 * 消息类型注册系统
 * 管理不同类型的消息渲染器
 */

import { MessageTypeDefinition } from '../types/artifact';

class MessageRegistry {
  private types = new Map<string, MessageTypeDefinition>();
  
  /**
   * 注册消息类型
   */
  register(definition: MessageTypeDefinition) {
    this.types.set(definition.type, definition);
  }
  
  /**
   * 获取指定类型的渲染器
   */
  getRenderer(type: string) {
    return this.types.get(type)?.renderer || null;
  }
  
  /**
   * 获取消息类型定义
   */
  getDefinition(type: string): MessageTypeDefinition | undefined {
    return this.types.get(type);
  }
  
  /**
   * 获取支持的消息类型列表
   */
  getSupportedTypes(): string[] {
    return Array.from(this.types.keys());
  }
  
  /**
   * 检查消息类型是否可以创建 Artifact
   */
  canCreateArtifact(type: string): boolean {
    const definition = this.types.get(type);
    return definition?.options?.canCreateArtifact || false;
  }
  
  /**
   * 获取消息类型支持的 Artifact 类型
   */
  getSupportedArtifactTypes(type: string): string[] {
    const definition = this.types.get(type);
    return definition?.options?.supportedArtifactTypes || [];
  }
  
  /**
   * 获取所有可以创建 Artifact 的消息类型
   */
  getArtifactCapableTypes(): string[] {
    return Array.from(this.types.entries())
      .filter(([, definition]) => definition.options?.canCreateArtifact)
      .map(([type]) => type);
  }
}

// 导出单例实例
export const messageRegistry = new MessageRegistry();

// 导出类型用于扩展
export { MessageRegistry };