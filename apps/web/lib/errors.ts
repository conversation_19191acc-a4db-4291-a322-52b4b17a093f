/**
 * 聊天SDK错误类
 */
export class ChatSDKError extends Error {
  public readonly code: string;
  public readonly type: string;

  constructor(code: string, message: string) {
    super(message);
    this.name = 'ChatSDKError';
    this.code = code;

    // 解析错误类型
    const [type] = code.split(':');
    this.type = type || 'unknown';
  }

  /**
   * 判断是否为数据库错误
   */
  isDatabaseError(): boolean {
    return this.code.includes('database');
  }

  /**
   * 判断是否为验证错误
   */
  isValidationError(): boolean {
    return this.code.includes('validation');
  }

  /**
   * 判断是否为权限错误
   */
  isPermissionError(): boolean {
    return this.code.includes('permission');
  }
}

/**
 * 创建数据库错误
 */
export function createDatabaseError(message: string): ChatSDKError {
  return new ChatSDKError('bad_request:database', message);
}

/**
 * 创建验证错误
 */
export function createValidationError(message: string): ChatSDKError {
  return new ChatSDKError('bad_request:validation', message);
}

/**
 * 创建权限错误
 */
export function createPermissionError(message: string): ChatSDKError {
  return new ChatSDKError('forbidden:permission', message);
}
