#!/usr/bin/python3
# coding: utf-8

import traceback
import requests
import base64
from settings.settings import logger


class CommonGit:
    def __init__(self):
        self.gitSecretInfo = {
            "key": "git_cd",
            "secret": self.get_KMS("git_cd")
        }

        self.gitSecretInfo["basicAuthSecret"] = "Basic %s" % base64.b64encode(
            (self.gitSecretInfo["key"] + ":" + self.gitSecretInfo["secret"]).encode('utf-8')).decode('utf-8')
        self.gitSecretInfo["keyString"] = self.gitSecretInfo["key"] + ":" + self.gitSecretInfo["secret"]

    def getGitSecretInfo(self):
        return self.gitSecretInfo

    def get_KMS(self, kms):
        url = "http://qa.sankuai.com/cq/cq" + "/portal/kms"
        payload = {
            "kms_key": kms,
            "token": "bmliVG9vbA=="
        }
        headers = {
            'content-type': "application/json"
        }
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=8)
            return base64.b64decode(response.text).decode('utf-8')
        except Exception as e:
            logger.error(e)
            logger.error(traceback.format_exc())
            return ""


if __name__ == "__main__":
    common_git = CommonGit()
