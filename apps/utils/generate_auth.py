import base64
from requests.auth import HTTPBasicAuth
import requests
import json
import hashlib
import hmac
from urllib.parse import urlparse
from datetime import datetime

# kms相关配置
URL_KMS = "http://qa.sankuai.com/cq/cq/portal/kms"
TOKEN_KMS = 'bmliVG9vbA=='


SONAR_UN = "git_cd"


def get_auth():
    payload = {
        "kms_key": SONAR_UN,
        "token": TOKEN_KMS
    }
    headers = {
        'content-type': "application/json"
    }
    kms_response = requests.post(URL_KMS, data=json.dumps(payload), headers=headers)
    sonar_key = base64.b64decode(kms_response.text).decode('ascii')
    auth = HTTPBasicAuth(SONAR_UN, sonar_key)
    return auth


SONAR_AUTH = get_auth()




def get_kms(user):
    payload = {
        "kms_key": user,
        "token": TOKEN_KMS
    }
    headers = {
        'content-type': "application/json"
    }
    kms_response = requests.post(URL_KMS, data=json.dumps(payload), headers=headers)
    key = base64.b64decode(kms_response.text).decode('ascii')
    return key


TOOL_CHAIN_USER = 'tool_panorama'
def gen_ba_header(method, dest_url):
    """生成工具链服务内部认证BA认证头"""
    date = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
    headers = {
        "Content-Type": "application/json",
        "Authorization": "",
        "Date": date
    }
    try:
        uri = urlparse(dest_url).path
        secret = get_kms(TOOL_CHAIN_USER)
        m = hmac.new(secret.encode(), ("%s %s\n%s" % (method, uri, date)).encode(), hashlib.sha1).digest()
        signature = base64.b64encode(m).decode()
        authorization = "MWS" + " " + TOOL_CHAIN_USER + ":" + signature
        headers['Authorization'] = authorization
    except Exception as e:
        print("生成BA认证头抛异常：%s" % repr(e))
    return headers

