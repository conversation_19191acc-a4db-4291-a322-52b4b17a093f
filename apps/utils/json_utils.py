#!/usr/bin/python3
# coding: utf-8

import json
import traceback
import json_repair
from settings.settings import logger


def repair_json_output(content: str) -> str:
    """
    修复和规范化 JSON 输出。

    Args:
        content (str): 可能包含 JSON 的字符串内容

    Returns:
        str: 修复后的 JSON 字符串，如果不是 JSON 则返回原始内容
    """
    content = content.strip()
    if content.startswith(("{","[")) or "```json" in content:
        try:
            # 如果内容被包裹在```json代码块中，提取JSON部分
            if content.startswith("```json"):
                content = content.removeprefix("```json")

            if content.endswith("```"):
                content = content.removesuffix("```")

            # 尝试修复并解析JSON
            repaired_content = json_repair.loads(content)
            return json.dumps(repaired_content)
        except Exception as e:
            logger.error(e)
            logger.error(traceback.format_exc())
    return content


def extract_pure_json(content: str) -> str:
    """
    从包含描述性文字的内容中提取纯JSON对象。
    专门处理LLM输出中包含解释文字的情况，只保留JSON对象部分。

    Args:
        content (str): 包含JSON和描述性文字的字符串内容

    Returns:
        str: 提取出的纯JSON字符串
    """
    content = content.strip()
    
    try:
        # 查找JSON对象的开始和结束位置
        json_start = -1
        json_end = -1
        brace_count = 0
        
        # 找到第一个 '{' 的位置
        for i, char in enumerate(content):
            if char == '{':
                if json_start == -1:
                    json_start = i
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0 and json_start != -1:
                    json_end = i + 1
                    break
        
        if json_start != -1 and json_end != -1:
            # 提取JSON部分
            json_content = content[json_start:json_end]
            
            # 验证提取的内容是否为有效JSON
            parsed_json = json.loads(json_content)
            return json.dumps(parsed_json, ensure_ascii=False)
        else:
            # 如果没有找到完整的JSON对象，尝试使用repair_json_output
            return repair_json_output(content)
            
    except Exception as e:
        logger.error(f"提取纯JSON失败: {str(e)}")
        logger.error(traceback.format_exc())
        # 如果提取失败，尝试使用原有的修复方法
        return repair_json_output(content)
