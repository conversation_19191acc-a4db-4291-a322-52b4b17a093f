from langgraph.prebuilt import create_react_agent

from apps.agents.model import get_llm_by_type, get_llm_coder_type
from apps.prompts.template import apply_prompt_template
from apps.tools.administrator_tool import judge_admin
from apps.tools.code_tool import code_executor
from apps.tools.markdown_tool import markdown_executor

administrator_agent = create_react_agent(
    model=get_llm_by_type(),
    tools=[judge_admin],
    prompt=lambda state: apply_prompt_template("administrator", state),
)

coder_agent = create_react_agent(
    model=get_llm_coder_type(),
    tools=[code_executor],
    prompt=lambda state: apply_prompt_template("terminalor", state),
)

markdown_agent = create_react_agent(
    model=get_llm_by_type(),
    tools=[markdown_executor],
    prompt=lambda state: apply_prompt_template("coder", state),
)

