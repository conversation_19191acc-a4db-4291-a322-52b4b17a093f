import requests
from settings.settings import logger

import os


def upload_all_file_to_s3(uuid_dir):
    file_name = uuid_dir.split("/")[-1]
    bucket_name = "testcase-autogen"
    # 遍历目录下的所有文件
    try:
        for root, dirs, files in os.walk(uuid_dir):
            for file in files:
                file_path = os.path.join(root, file)

                # 调用上传函数
                upload_file_to_s3(file_path, bucket_name, file_name)
                logger.info(f"已上传文件: {file_path} 到 {file_name}")
    except Exception as e:
        logger.error(f"上传文件失败: {e}")


def upload_file_to_s3(file_path, bucket, path="", file_name=None):
    """
    上传文件到 S3 存储

    Args:
        file_path: 本地文件路径
        bucket: 存储桶名称
        path: 存储路径
        file_name: 文件名，如果为 None 则使用原文件名

    Returns:
        响应对象
    """
    if file_name is None:
        import os
        file_name = os.path.basename(file_path)

    url = "http://qa.sankuai.com/cloud/file/upload"

    # 准备文件和表单数据
    files = {
        'file': (file_name, open(file_path, 'rb'))
    }

    data = {
        'bucket': bucket,
        'path': path + '/',
        'filename': file_name
    }

    # 发送请求
    response = requests.post(url, files=files, data=data)

    return response


