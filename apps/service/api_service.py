from apps.agents.model import get_llm_by_type
from langchain.schema import HumanMessage, SystemMessage


def get_llm_response_for_muse(interrupt_type, input_content):
    # 只有当interrupt_type是approve时才进行语意判断
    if interrupt_type != "approve":
        # 暂时不做处理，可以返回None或者原样返回
        return None
    
    llm = get_llm_by_type()
    
    # 判断用户输入是否表示确定语气的prompt
    system_prompt = """
    你是一个语意分析助手。你的任务是判断用户的输入是否表示确定、同意或继续的语气。
    
    确定语气的典型表达包括但不限于：
    - 中文：是、好、可以、继续、同意、确定、行、没问题、OK、开始吧、进行、执行
    - 英文：yes、ok、okay、sure、continue、proceed、go ahead、start、begin、agree
    - 其他表示肯定、同意、继续的表达
    
    请只回答 "YES" 或 "NO"，不要添加任何解释。
    - 如果用户输入表示确定/同意/继续的语气，回答：YES
    - 如果用户输入不表示确定/同意/继续的语气，回答：NO
    """
    
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"用户输入：{input_content}")
    ]
    
    response = llm.invoke(messages)
    
    return response.content


# if __name__ == "__main__":
#     input_content = "可以，请继续吧"
#     interrupt_type = "approve"
#     response = get_llm_response_for_muse(interrupt_type, input_content)
#     print(response)