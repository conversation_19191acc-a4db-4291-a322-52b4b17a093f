import requests
import uuid
import time
import re
import subprocess
from urllib.parse import quote
from requests.exceptions import RequestException

from apps.service.teminal_service import open_terminal
from apps.utils import generate_auth
from apps.utils.config import CommonGit
from settings.settings import logger


def git_permission(repo, mis_id):
    try:
        repo_name, project = parse_repo(repo)
        url = f"http://git.sankuai.com/rest/api/2.0/projects/{project}/repos/{repo_name}/hasPermissionSpec"

        headers = {
            "authorization": CommonGit().getGitSecretInfo()["basicAuthSecret"],
            "cache-control": "no-cache"
        }

        permissions = ["REPO_GUEST", "REPO_READ", "REPO_WRITE", "REPO_ADMIN"]

        with requests.Session() as session:
            for permission in permissions:
                params = {
                    "permission": permission,
                    "user": mis_id
                }
                response = session.get(url, params=params, headers=headers, timeout=10)

                if response.status_code == 200:
                    try:
                        if response.json().get("allow", False):
                            logger.info(f"User {mis_id} has permission {permission} for repo {repo}")
                            return "有权限"
                    except ValueError:
                        continue
        return "无权限"
    except (RequestException, ValueError, KeyError) as e:
        logger.error(f"Error occurred while checking git permission: {e}")
        return "检查权限失败"


def get_suite_jar(appkey, repo_class):
    repo = get_repo_by_appkey(appkey)
    files_name = get_specific_files(repo, repo_class)
    if not files_name:
        return ""
    specific_file = files_name[0]
    module = specific_file.split("/")[0]
    # 如果单模块会不会有问题todo
    module_pom = f"{module}/pom.xml"
    combined_text = get_pom_file_content(repo, module_pom)
    dependency = get_dependency_content(combined_text)
    return dependency


def get_repo_by_appkey(appkey):
    try:
        url = f"http://common.vip.sankuai.com/basic/sc/byAppKey"
        if not isinstance(appkey, list):
            appkeys = [appkey]
        params = {"appKeys": appkeys}
        response = requests.get(url, params=params)
        if response.status_code != 200:
            raise Exception(f"请求失败，状态码: {response.status_code}, 响应内容: {response.text}")
        response_content = response.json()
        if response_content.get("status") != 0:
            raise Exception(f"请求失败，状态码: {response_content.get('status')}, 响应内容: {response_content.get('message')}")
        datas = response_content.get("data")
        repo = next((data.get("gitRepository") for data in datas if data.get("appKey") == appkey), None)
        logger.info(f"appkey: {appkey}, repo: {repo}")
        return repo
    except Exception as e:
        logger.error(f"获取仓库地址失败: {e}")
        return None


def get_specific_files(repo, repo_class):
    """
    获取仓库中匹配指定类别的所有文件

    Args:
        repo (str): 仓库URL
        repo_class (str): 文件名称

    Returns:
        list: 匹配的文件列表，如果出错则返回空列表
    """
    try:
        file_list = get_repo_all_files(repo)
        matched_files = [file for file in file_list if repo_class in file]

        logger.info(f"匹配的文件名称为: {matched_files}")
        return matched_files
    except Exception as e:
        logger.error(f"获取仓库{repo}的文件{repo_class}出现异常，原因: {e}")
        return []


def get_repo_all_files(repo):
    try:
        repo_name, project = parse_repo(repo)
        url = f"http://git.sankuai.com/rest/api/2.0/projects/{project}/repos/{repo_name}/files"
        headers = {
            "authorization": CommonGit().getGitSecretInfo()["basicAuthSecret"],
            "cache-control": "no-cache"
        }
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code != 200:
            logger.error(f"API request failed with status {response.status_code} for repo {repo}")
            return []

        all_files = response.json()
        file_list = all_files.get("values", [])

        return file_list
    except Exception as e:
        logger.error(f"获取仓库{repo}的全部文件出现异常，原因: {e}")
        return []


def get_pom_file_content(repo, module_pom):
    repo_name, project = parse_repo(repo)
    url = f"http://git.sankuai.com/rest/api/2.0/projects/{project}/repos/{repo_name}/browse/{module_pom}"
    headers = {
        "authorization": CommonGit().getGitSecretInfo()["basicAuthSecret"],
        "cache-control": "no-cache"
    }
    response = requests.get(url, headers=headers, timeout=10)
    if response.status_code != 200:
        return

    pom_content = response.json()
    lines = pom_content.get("lines")
    combined_text = ""
    for line in lines:
        text = line.get("text")
        combined_text += text + "\n"
    return combined_text


def get_dependency_content(combined_text):
    combined_text = re.sub(r'<parent>.*?</parent>', '', combined_text, flags=re.DOTALL)
    combined_text = re.sub(r'<dependencies>.*?</dependencies>', '', combined_text, flags=re.DOTALL)
    combined_text = re.sub(r'<build>.*?</build>', '', combined_text, flags=re.DOTALL)

    dependency_match = re.search(r'<groupId>(.*?)</groupId>\s*<artifactId>(.*?)</artifactId>\s*<version>(.*?)</version>', combined_text, flags=re.DOTALL)
    dependency = ""
    if dependency_match:
        group_id, artifact_id, version = dependency_match.groups()
        dependency = f"<dependency>\n  <groupId>{group_id}</groupId>\n  <artifactId>{artifact_id}</artifactId>\n  <version>{version}</version>\n</dependency>"
        logger.info(f"本次Jar包匹配的dependency是: \n{dependency}")
    return dependency



def parse_repo(repo):
    repo_name = quote(repo.split('/')[-1].replace('.git', ''))
    project = quote(repo.split('/')[-2])
    return repo_name, project


def muse_repo_pr_command(repo, remote_port, user_mis):
    open_terminal(remote_port)
    time.sleep(6)
    config_result = user_git_config(user_mis, remote_port)
    if not config_result:
        return
    branch_uuid = str(uuid.uuid4())
    branch_name = f"feature/muse/{branch_uuid}"

    checkout_command = f"git checkout -b {branch_name}"
    checkout_cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d '
        f'\'{{"command":"custom.runInTerminal", "args": ["{checkout_command}"]}}\''
    ]
    checkout_result = subprocess.run(checkout_cmd, capture_output=True, text=True)

    # 检查命令是否成功执行，如果不成功直接返回
    if checkout_result.returncode != 0:
        return

    commit_message = "feat: Muse发起本次Mario生成用例PR"
    add_command = "git add ."
    add_cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d '
        f'\'{{"command":"custom.runInTerminal", "args": ["{add_command}"]}}\''
    ]
    add_result = subprocess.run(add_cmd, capture_output=True, text=True)
    if add_result.returncode != 0:
        return

    commit_command = f"git commit -m \\\"{commit_message}\\\""
    commit_cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d '
        f'\'{{"command":"custom.runInTerminal", "args": ["{commit_command}"]}}\''
    ]
    commit_result = subprocess.run(commit_cmd, capture_output=True, text=True)
    if commit_result.returncode != 0:
        return

    push_command = f"git push origin {branch_name}"
    push_cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d '
        f'\'{{"command":"custom.runInTerminal", "args": ["{push_command}"]}}\''
    ]
    push_result = subprocess.run(push_cmd, capture_output=True, text=True)
    if push_result.returncode != 0:
        logger.error(f"推送分支失败: {push_result.stderr}")
        return "推送分支失败"
    time.sleep(3)
    muse_pull_request(repo, branch_name, user_mis)


def muse_pull_request(repo, branch_name, user_mis):
    temp = repo.split('/')
    repo_name = temp[len(temp) - 1].split(".git")[0]
    project = temp[len(temp) - 2]
    url = "http://git.sankuai.com/rest/api/2.0/projects/" + project + "/repos/" + repo_name + "/pull-requests"
    headers = {
        'Content-Type': 'application/json',
        'Code-Real-User': user_mis,
    }
    payload = {
        "deleteSourceRefAfterMerge": True,
        "description": "Muse发起本次Mario生成用例PR[^_^]",
        "fromRef": {
            "id": f"refs/heads/{branch_name}",
            "displayId": f"{branch_name}",
            "repository": {
                "slug": repo_name,
                "project": {
                    "key": project
                }
            }
        },
        "title": "Muse发起本次Mario用例生成PR[AI生成辛苦验证后再合并]",
        "toRef": {
            "id": "refs/heads/master",
            "displayId": "master"
        }
    }
    logger.info(f"payload:{payload}")
    try:
        resp = requests.post(url=url, json=payload, headers=headers, auth=generate_auth.SONAR_AUTH)
        if resp.status_code != 200:
            print(f"创建PR失败 - 状态码: {resp.status_code}")
            print(f"错误信息: {resp.text}")
        else:
            response_data = resp.json()
            links = response_data.get('links', {})
            print(f"成功推送改动到master: {repo_name}")
    except Exception as e:
        print(f"请求异常: {e}")


def user_git_config(user_mis, remote_port):
    """
    配置 Git 用户信息

    Args:
        user_mis: 用户 MIS ID
        remote_port: 远程端口

    Returns:
        bool: 配置成功返回 True，否则返回 False
    """
    logger.info(f"开始为用户 {user_mis} 配置 Git 信息")
    user_mis = "git_cd"
    # 配置用户名
    user_mis_command = f"git config user.name {user_mis}"
    user_mis_cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d '
        f'\'{{"command":"custom.runInTerminal", "args": ["{user_mis_command}"]}}\''
    ]
    try:
        user_name_result = subprocess.run(user_mis_cmd, capture_output=True, text=True, timeout=30)
        if user_name_result.returncode != 0:
            logger.error(f"配置用户名失败: {user_name_result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        logger.error(f"配置用户名命令执行超时")
        return False
    logger.info(f"配置用户名 {user_mis} 成功")

    # 配置邮箱
    user_email_command = f"git config user.email {user_mis}@meituan.com"
    user_email_cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d '
        f'\'{{"command":"custom.runInTerminal", "args": ["{user_email_command}"]}}\''
    ]
    user_email_result = subprocess.run(user_email_cmd, capture_output=True, text=True)
    if user_email_result.returncode != 0:
        logger.error(f"配置用户邮箱 {user_mis}@meituan.com 失败")
        return False
    logger.info(f"配置用户邮箱 {user_mis}@meituan.com 成功")

    logger.info(f"用户 {user_mis} Git 信息配置完成")
    return True


def run_mario_test_command(mario_case, remote_port):
    open_terminal(remote_port)
    time.sleep(6)
    mario_test_command = f"mvn clean test -Dtest={mario_case}"
    mario_test_cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d '
        f'\'{{"command":"custom.runInTerminal", "args": ["{mario_test_command}"]}}\''
    ]
    mario_test_result = subprocess.run(mario_test_cmd, capture_output=True, text=True)
    if mario_test_result.returncode != 0:
        logger.error(f"执行Mario用例失败：{mario_test_result.stderr}")
        return False
    logger.info(f"执行Mario用例成功：{mario_test_result.stdout}")
    return True




if __name__ == "__main__":
    # get_pom_file_content("ssh://**********************/vc/vc-deal-home.git")
    # repo = "ssh://**********************/vc/vc-deal-home.git"
    # get_specific_files(repo, "DealProductService")
    get_suite_jar("vc-deal-service")


