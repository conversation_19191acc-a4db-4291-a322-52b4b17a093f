import json
import os
import re
from typing import Dict, List, Any, Optional
from settings.settings import logger


class ModelResponseParser:
    """
    模型响应解析器类
    用于解析和格式化模型返回的 JSON 内容
    """

    def __init__(self):
        pass

    def parse_json_content(self, content: str) -> Optional[Dict[str, Any]]:
        """
        解析 JSON 字符串内容

        Args:
            content (str): JSON 字符串内容

        Returns:
            Optional[Dict[str, Any]]: 解析后的字典对象，解析失败时返回 None
        """
        try:
            parsed_content = json.loads(content)
            return parsed_content
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON content: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error while parsing content: {e}")
            return None

    def extract_plan_steps(self, model_response: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """
        从解析后的模型响应中提取计划步骤

        Args:
            model_response (Dict[str, Any]): 包含模型响应信息的字典

        Returns:
            Optional[List[Dict[str, Any]]]: 计划步骤列表，提取失败时返回 None
        """
        try:
            content = model_response.get('content')
            if not content:
                logger.warning("No content found in model response")
                return None

            parsed_content = self.parse_json_content(content)
            if not parsed_content:
                return None

            steps = parsed_content.get('steps', [])
            return steps

        except Exception as e:
            logger.error(f"Error extracting plan steps: {e}")
            return None

    def extract_plan_info(self, model_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        从模型响应中提取完整的计划信息

        Args:
            model_response (Dict[str, Any]): 包含模型响应信息的字典

        Returns:
            Dict[str, Any]: 包含解析后计划信息的字典
        """
        result = {
            'type': model_response.get('type'),
            'node': model_response.get('node'),
            'model_name': model_response.get('model_name'),
            'tokens': model_response.get('tokens'),
            'message': model_response.get('message'),
            'parsed_content': None,
            'thought': None,
            'title': None,
            'steps': []
        }

        try:
            content = model_response.get('content')
            if content:
                parsed_content = self.parse_json_content(content)
                if parsed_content:
                    result['parsed_content'] = parsed_content
                    result['thought'] = parsed_content.get('thought')
                    result['title'] = parsed_content.get('title')
                    result['steps'] = parsed_content.get('steps', [])

        except Exception as e:
            logger.error(f"Error extracting plan info: {e}")

        return result

    def format_plan_response(self, model_response: Dict[str, Any]) -> str:
        """
        格式化计划响应为可读的字符串内容

        Args:
            model_response (Dict[str, Any]): 模型响应字典

        Returns:
            str: 格式化后的字符串内容
        """
        plan_info = self.extract_plan_info(model_response)

        # 构建格式化的输出内容
        output_lines = []

        # 基本信息
        # output_lines.append(f"节点类型: {plan_info['node'] or 'Unknown'}")
        output_lines.append(f"计划标题: {plan_info['title'] or 'Unknown'}")
        output_lines.append(f"思考过程: {plan_info['thought'] or 'No thought provided'}")
        # output_lines.append(f"使用模型: {plan_info['model_name'] or 'Unknown'}")
        # output_lines.append(f"消耗令牌: {plan_info['tokens'] or 0}")

        # 执行步骤
        output_lines.append("")
        output_lines.append("执行步骤:")

        steps = plan_info['steps']
        if steps:
            for i, step in enumerate(steps, 1):
                output_lines.append(f"{i}. {step.get('title', 'Unknown')}")
                output_lines.append(f"   Agent_Tool: {step.get('agent_name', 'Unknown')}")
                output_lines.append(f"   描述: {step.get('description', 'No description')}")
                if step.get('note'):
                    output_lines.append(f"   备注: {step.get('note')}")
                output_lines.append("")  # 空行分隔
        else:
            output_lines.append("   暂无执行步骤")

        return "\n".join(output_lines)

    def format_steps_summary(self, model_response: Dict[str, Any]) -> str:
        """
        格式化步骤摘要信息

        Args:
            model_response (Dict[str, Any]): 模型响应字典

        Returns:
            str: 步骤摘要字符串
        """
        steps = self.extract_plan_steps(model_response)
        if not steps:
            return "暂无步骤信息"

        output_lines = []
        for i, step in enumerate(steps, 1):
            output_lines.append(f"{i}. {step.get('title', 'Unknown')} - Agent_Tool: {step.get('agent_name', 'Unknown')}")

        return "\n".join(output_lines)

    def get_formatted_content(self, model_response: Dict[str, Any], format_type: str = "full") -> str:
        """
        获取格式化的内容

        Args:
            model_response (Dict[str, Any]): 模型响应字典
            format_type (str): 格式化类型，可选值: "full", "summary", "steps_only"

        Returns:
            str: 格式化后的内容字符串
        """
        if format_type == "full":
            return self.format_plan_response(model_response)
        elif format_type == "summary":
            return self.format_steps_summary(model_response)
        elif format_type == "steps_only":
            steps = self.extract_plan_steps(model_response)
            return json.dumps(steps, ensure_ascii=False, indent=2) if steps else "暂无步骤信息"
        else:
            return "不支持的格式化类型"

    # 这是格式化校验仓库权限的方法
    def format_permission_check_message(self, content: Dict[str, Any]) -> str:
        """
        格式化权限校验消息

        Args:
            content (Dict[str, Any]): 包含repository和user_mis的字典

        Returns:
            str: 格式化后的权限校验消息
        """
        if isinstance(content, dict):
            repository = content.get("repository", "")
            user_mis = content.get("user_mis", "")
            return f"正在校验用户{user_mis}是否有{repository}仓库的权限..."

        return "权限校验信息格式错误"

    # 这是提取工具结果中的Markdown文件内容的方法
    def get_markdown_file_content(self, tool_result):
        """
        从工具结果中提取文件夹路径，并读取该路径下的 result.md 文件内容

        Args:
            tool_result (str): 包含文件夹路径的工具调用结果

        Returns:
            dict: 包含状态、内容和错误信息的字典
        """
        result = {
            "success": False,
            "content": None,
            "error": None
        }
        # 直接从字典中获取文件夹路径
        if isinstance(tool_result, str):
            try:
                tool_result = json.loads(tool_result)
            except json.JSONDecodeError as e:
                result["error"] = f"无法解析工具结果字符串: {str(e)}"
                logger.error(f"JSON 解析失败: {e}")
                return result
        folder_path = tool_result.get("folder_path")
        if not folder_path:
            result["error"] = "无法从结果中获取文件夹路径"
            return result
        md_file_path = os.path.join(folder_path, "result.md")

        # 检查文件是否存在
        if not os.path.exists(md_file_path):
            result["error"] = f"Markdown 文件不存在: {md_file_path}"
            return result

        # 读取文件内容
        try:
            with open(md_file_path, 'r', encoding='utf-8') as f:
                result["content"] = f.read()
                result["success"] = True
        except Exception as e:
            result["error"] = f"读取 Markdown 文件失败: {str(e)}"
            logger.error(f"读取 Markdown 文件失败: {e}", exc_info=True)

        return result


    def format_reporter_for_muse(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化输入内容为 Muse 所需的格式

        Args:
            content (Dict[str, Any]): 包含输入信息的字典

        Returns:
            Dict[str, Any]: 包含提取元素的字典
        """
        try:
            # 获取 port 元素
            port = content.get("port", "")
            mario_case = content.get("mario_case", "")
            mario_case_without_extension = mario_case.replace(".java", "")
            # 获取 user_info 字典及其中的元素
            user_info = content.get("user_info", {})
            repository = user_info.get("repository", "")
            user_mis = user_info.get("user_mis", "")

            # 构建返回字典
            result = {
                "port": port,
                "repository": repository,
                "user_mis": user_mis,
                "mario_case": mario_case_without_extension
            }
            return result

        except Exception as e:
            logger.error(f"Error formatting input for Muse: {e}")
            return {"error": "格式化输入失败"}
