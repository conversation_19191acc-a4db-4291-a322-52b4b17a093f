#!/usr/bin/python3
# coding: utf-8

import subprocess
import os
import urllib.parse
import json
import time
import random
import string
import glob
from settings.settings import logger


def git_repo(repo, remote_port):
    """
    克隆Git仓库到指定目录，并在目录名后添加随机6位字符

    Args:
        repo: Git仓库地址
        remote_port: 远程端口，默认为9013

    Returns:
        bool: 操作是否成功
    """
    try:
        repo_name = os.path.basename(repo)
        project_dir = repo_name.split('.git')[0] if repo_name.endswith('.git') else repo_name

        # random_suffix = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
        # project_dir = f"{project_dir}_{random_suffix}"

        command = f"git clone {repo} {project_dir}"
        cmd = [
            '/bin/bash',
            '-c',
            f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d \'{{"command":"custom.runInTerminal", "args": ["{command}"]}}\''
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"Git克隆失败: {result.stderr}")

        logger.info(f"Git克隆成功: {project_dir}")

        target_path = f"/home/<USER>/{project_dir}"
        cd_command = f"cd {target_path}"
        cd_cmd = [
            '/bin/bash',
            '-c',
            f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d \'{{"command":"custom.runInTerminal", "args": ["{cd_command}"]}}\''
        ]
        cd_result = subprocess.run(cd_cmd, capture_output=True, text=True)
        if cd_result.returncode != 0:
            logger.error(f"进入目录失败: {cd_result.stderr}")
            return False

        logger.info(f"成功进入目录: {target_path}")
        return True, target_path

    except Exception as e:
        logger.error(f"Git克隆过程发生错误: {str(e)}")
        return False


def open_terminal(remote_port):
    request_data = {
        "command": "workbench.action.terminal.new"
    }
    json_data = json.dumps(request_data)
    cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d \'{json_data}\''
    ]
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        logger.error(f"打开终端失败: {result.stderr}")
        return False


def open_vscode_folder(repo, remote_port):
    temp = repo.split('/')
    repo_name = temp[len(temp) - 1].split(".git")[0]

    # 构建 VSCode 工作区更新命令
    vscode_command = (f"vscode.workspace.updateWorkspaceFolders(0, vscode.workspace.workspaceFolders ? "
                      f"vscode.workspace.workspaceFolders.length : 0, {{ uri: vscode.Uri.file(\"/home/<USER>/{repo_name}\") "
                      f"}})")

    # 构建请求体并使用 json 模块正确处理转义
    request_data = {
        "command": "custom.eval",
        "args": [vscode_command]
    }

    # 将请求体转换为 JSON 字符串
    json_data = json.dumps(request_data)

    cmd = [
        '/bin/bash',
        '-c',
        f'curl --insecure -X POST -H "Content-Type: application/json" https://10.101.151.42:{remote_port}/remote/ -d \'{json_data}\''
    ]
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode == 0:
        return True
    else:
        print(f"执行VSCode命令失败: {result.stderr}")
        return False



def get_all_files(folder_path: str) -> tuple[list[str], bool]:
    try:
        # 确保 folder_path 是字符串类型
        if not isinstance(folder_path, str):
            logger.error(f"folder_path 必须是字符串类型")
            return [], False

        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        full_path = os.path.join(current_dir, folder_path)

        if not os.path.exists(str(full_path)):
            logger.error(f"文件夹不存在: {full_path}")
            return [], False

        files = os.listdir(str(full_path))
        file_list = []

        for file in files:
            # 跳过以.md结尾的文件
            if file.endswith('.md'):
                logger.info(f"跳过 Markdown 文件: {file}")
                continue
            file_path = os.path.join(str(full_path), str(file))
            if os.path.isfile(file_path):
                file_list.append(file)
                logger.info(f"文件: {file}")

        return file_list, True

    except Exception as e:
        logger.error(f"获取文件列表时发生错误: {str(e)}")
        return [], False


def get_dir_name(repo):
    repo_name = os.path.basename(repo)
    project_dir = repo_name.split('.git')[0] if repo_name.endswith('.git') else repo_name

    random_suffix = ''.join(random.choices(string.ascii_letters + string.digits, k=6))
    project_dir = f"{project_dir}_{random_suffix}"
    return project_dir


def download_file_s3(repo, uuid_file, port, case_type):
    try:
        success, project_dir = git_repo(repo, port)
        if not success:
            return False

        files, success = get_all_files(uuid_file)
        if not success:
            return False

        for file in files:
            try:
                result = uuid_file.split("/")[-1]
                filename = result + "/" + file
                encoded_filename = urllib.parse.quote(filename)
                download_url = f"http://qa.sankuai.com/cloud/file/download?bucket=testcase-autogen&filename={encoded_filename}"

                target_dir = get_target_directory(file, project_dir, case_type)
                logger.info(f"文件下载路径target_dir为: {target_dir}")

                # 创建目标目录的命令
                mkdir_cmd = f'mkdir -p "{target_dir}"'
                mkdir_remote_cmd = {
                    "command": "custom.runInTerminal",
                    "args": [mkdir_cmd]
                }

                mkdir_cmd_full = [
                    '/bin/bash',
                    '-c',
                    f'curl --insecure https://10.101.151.42:{port}/remote/ -d \'{json.dumps(mkdir_remote_cmd)}\''
                ]

                # 执行创建目录命令
                mkdir_result = subprocess.run(mkdir_cmd_full, capture_output=True, text=True)
                if mkdir_result.returncode != 0:
                    logger.warning(f"创建目录失败，使用默认路径 {project_dir}: {mkdir_result.stderr}")
                    target_dir = project_dir

                # 下载文件到指定目录
                target_file_path = f"{target_dir}/{file}"
                curl_cmd = f'wget "{download_url}" -O "{target_file_path}"'
                remote_cmd = {
                    "command": "custom.runInTerminal",
                    "args": [curl_cmd]
                }

                cmd = [
                    '/bin/bash',
                    '-c',
                    f'curl --insecure https://10.101.151.42:{port}/remote/ -d \'{json.dumps(remote_cmd)}\''
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    logger.error(f"下载文件失败 {file}: {result.stderr}")
                    continue

                logger.info(f"成功下载文件: {file} 到 {target_dir}")

            except Exception as e:
                logger.error(f"下载文件 {file} 时发生错误: {str(e)}")
                continue
        logger.info(f"下载完代码文件，应该跳转")
        time.sleep(2)
        open_vscode_folder(repo, port)
        return True

    except Exception as e:
        logger.error(f"下载过程发生错误: {str(e)}")
        return False


def get_target_directory(file, project_dir, case_type):

    """
    根据case_type和文件名确定目标目录

    Args:
        project_dir: 项目根目录
        file: 文件名
        case_type: 案例类型 (MarioHttpCaseDev, MarioMapiHttpCaseDev, MarioThriftCaseDev, MarioPigeonCaseDev)

    Returns:
        str: 目标目录路径
    """
    target_dir = project_dir
    if case_type in ["MarioHttpCaseDev.md", "MarioMapiHttpCaseDev.md"]:
        if file.endswith("Test.json"):
            target_dir = f"{project_dir}/src/test/java/http/data"
        elif file.endswith("Test.java"):
            target_dir = f"{project_dir}/src/test/java/http/testsuites"
        elif file.endswith("Api.java"):
            target_dir = f"{project_dir}/src/test/java/http"
        elif file.endswith("Schema.json"):
            target_dir = f"{project_dir}/src/test/java/http/schema"
        else:
            target_dir = project_dir
    if case_type == "MarioThriftCaseDev.md":
        if file.endswith("Test.json"):
            target_dir = f"{project_dir}/src/test/java/thrift/data"
        elif file.endswith("Test.java"):
            target_dir = f"{project_dir}/src/test/java/thrift/testsuites"
        elif file.endswith("Schema.json"):
            target_dir = f"{project_dir}/src/test/java/thrift/schema"
    if case_type == "MarioPigeonCaseDev.md":
        if file.endswith("Test.json"):
            target_dir = f"{project_dir}/src/test/java/pigeon/data"
        elif file.endswith("Test.java"):
            target_dir = f"{project_dir}/src/test/java/pigeon/testsuites"
        elif file.endswith("Schema.json"):
            target_dir = f"{project_dir}/src/test/java/pigeon/schema"
    return target_dir




if __name__ == "__main__":
    open_terminal(9004)
    # s_ = download_file_s3("ssh://*******************/nibqa/mario-demo.git", "fc07d08e-386e-487f-baea-9d4d9f96f34f", 9000)
