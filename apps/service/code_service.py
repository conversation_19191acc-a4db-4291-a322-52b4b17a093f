
import uuid
import json
import time
import traceback
import re
import os
import xml.etree.ElementTree as ET
from markdownify import markdownify as md
from langchain_core.messages import HumanMessage

from apps.agents.model import get_llm_coder_type
from apps.prompts.template import prompt_origin_template
from apps.service.ec_service import get_ec
from apps.service.repo_service import get_suite_jar, get_repo_all_files, get_pom_file_content
from apps.service.s3_service import upload_all_file_to_s3
from apps.service.teminal_service import download_file_s3, open_terminal

from apps.utils.json_utils import extract_pure_json
from settings.settings import logger


def generate_markdown_file(uuid_dir, ec_content):
    file_name = f"ec.md"
    file_path = os.path.join(uuid_dir, file_name)
    with open(file_path, 'w', encoding='utf-8') as md_file:
        md_file.write(ec_content)
    logger.info("markdown文本生成完毕")
    return file_name, ec_content


def convert_ec_to_markdown(test_case_id, user_mis):
    """
    将测试用例的EC内容转换为Markdown格式

    Args:
        test_case_id: 测试用例ID
        user_mis: 用户MIS

    Returns:
        str: 转换后的Markdown内容
    """
    ec_content = get_ec(test_case_id, user_mis)
    # 使用 html2markdown 库直接转换
    markdown_content = md(ec_content,
                          heading_style="ATX",  # 使用 # 风格的标题
                          bullets="-",  # 使用 - 作为列表符号
                          strip=['a']  # 移除链接标签但保留文本
                          )
    return markdown_content


def make_uuid_dir():
    """
    在src目录下创建一个以UUID命名的文件夹

    Returns:
        str: 创建的UUID文件夹的完整路径
    """
    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    # 生成UUID
    unique_id = str(uuid.uuid4())
    # 创建UUID文件夹路径
    uuid_dir = os.path.join(current_dir, unique_id)
    # 创建文件夹
    os.makedirs(uuid_dir, exist_ok=True)

    logger.info(f"已在src目录下创建UUID文件夹: {unique_id}")
    return uuid_dir


def process_with_coder_prompt(uuid_dir, template_file_name, input_file_name):
    # 读取coder.md文件
    coder_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "prompts", "coder.md")
    with open(coder_path, "r", encoding="utf-8") as f:
        coder_content = f.read()

    prompt_content = None

    if template_file_name == "MarioHttpCaseDev.md":
        template_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "prompts",
                                          "MarioHttpCaseDev.md")
        with open(template_file_path, "r", encoding="utf-8") as f:
            http_template_content = f.read()
        prompt_content = coder_content.replace("{HTTP_TEMPLATE_FILE}", http_template_content)

    if template_file_name == "MarioMapiHttpCaseDev.md":
        template_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "prompts",
                                          "MarioMapiHttpCaseDev.md")
        with open(template_file_path, "r", encoding="utf-8") as f:
            mapi_http_template_content = f.read()
        prompt_content = coder_content.replace("{MAPI_HTTP_TEMPLATE_FILE}", mapi_http_template_content)

    if template_file_name == "MarioThriftCaseDev.md":
        template_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "prompts",
                                          "MarioThriftCaseDev.md")
        with open(template_file_path, "r", encoding="utf-8") as f:
            thrift_template_content = f.read()
        prompt_content = coder_content.replace("{THRIFT_TEMPLATE_FILE}", thrift_template_content)

    if template_file_name == "MarioPigeonCaseDev.md":
        template_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "prompts",
                                          "MarioPigeonCaseDev.md")
        with open(template_file_path, "r", encoding="utf-8") as f:
            pigeon_template_content = f.read()
        prompt_content = coder_content.replace("{PIGEON_TEMPLATE_FILE}", pigeon_template_content)

    input_file_path = os.path.join(uuid_dir, input_file_name)
    # 读取输入文件
    with open(input_file_path, "r", encoding="utf-8") as f:
        input_content = f.read()
    if template_file_name == "MarioThriftCaseDev.md" or template_file_name == "MarioPigeonCaseDev.md":
        # 查询依赖jar包信息
        dependency_content = get_dependency_content(input_content)
        if dependency_content is not None and dependency_content != "":
            # 写入依赖文件
            dependency_file_path = os.path.join(uuid_dir, "dependency.md")
            with open(dependency_file_path, "w", encoding="utf-8") as f:
                f.write(dependency_content)
        else:
            logger.info("本次没有查询到对应的dependency信息")
            # 替换"Jar包直接调用"为"泛化调用"
            input_content = input_content.replace("Jar包直接调用", "泛化调用")
            # 保存修改后的内容
            with open(input_file_path, 'w', encoding='utf-8') as f:
                f.write(input_content)

    # 组合最终的prompt
    messages = [{"role": "system", "content": prompt_content}] + [{"role": "user", "content": input_content}]
    # 获取Claude模型
    llm = get_llm_coder_type()

    # 发送请求并获取响应
    response = llm.invoke(messages)
    result_file_path = os.path.join(uuid_dir, "result.md")
    with open(result_file_path, "w", encoding="utf-8") as f:
        f.write(response.content)
    return result_file_path


def get_dependency_content(input_content):
    # 匹配服务端Appkey
    appkey_pattern = r"服务端Appkey:\s*([^\s\n]+)"
    appkey_match = re.search(appkey_pattern, input_content)
    appkey = appkey_match.group(1).strip() if appkey_match else None

    # 匹配服务名称
    service_name_pattern = r"服务名称:\s*([^\s\n]+)"
    service_name_match = re.search(service_name_pattern, input_content)
    service_name = service_name_match.group(1).strip() if service_name_match else None
    if service_name is None:
        return ""
    repo_class = service_name.split(".")[-1]
    return get_suite_jar(appkey, repo_class)


def process_main(repo, uuid_dir, port, case_type):
    try:
        open_terminal(port)
        logger.info("终端已经打开")
        time.sleep(12)
        logger.info("等待10s")

        # 路径类似/Users/<USER>/downDataSouces/toolchain-muse-app/apps/2a0f6196-c7eb-4793-b122-43984585666d
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        result_md_path = os.path.join(current_dir, uuid_dir)
        md_result = create_code_file(uuid_dir)
        logger.info("代码文件已经生成完")

        compare_pom(uuid_dir, repo)

        upload_all_file_to_s3(result_md_path)
        logger.info(f"文件上传到S3: {md_result}")
        filename = get_test_class_filenames(md_result)
        download_file_s3(repo, result_md_path, port, case_type)
        return filename
    except Exception as e:
        error_msg = f"处理失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return error_msg


def get_test_class_filenames(md_result):
    """
    从extracted_files中获取测试类实现的文件名

    Args:
        md_result: create_code_file函数返回的文件列表

    Returns:
        str: 测试类实现的文件名，如果没找到则返回None
    """
    for file_info in md_result:
        # 使用正则表达式匹配description中包含"测试类实现"的文件
        if re.search(r'测试类实现', file_info['description']):
            return file_info['filename']
    return ""


def analyze_content_with_llm(content):
    """
    使用LLM分析文件内容，判断接口类型并返回对应的模板文件名

    Args:
        content: 文件内容

    Returns:
        str: 对应的模板文件名
    """
    llm = get_llm_coder_type()
    # 获取analyze_content的prompt模板
    template = prompt_origin_template("analyze_content")
    # 构建完整的消息列表
    messages = template + [{"role": "user", "content": content}]
    response = llm.invoke(messages)
    return response.content.strip()


def read_file_content(file_path):
    """
    读取指定路径文件的内容

    Args:
        file_path: 文件的完整路径

    Returns:
        str: 文件内容，如果文件不存在则返回错误信息
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        return content
    except FileNotFoundError:
        return f"错误：文件 {file_path} 不存在"
    except Exception as e:
        return f"读取文件时发生错误: {str(e)}"


def create_code_file1(markdown_file_path: str) -> list[str] | str:
    """
    根据markdown文件路径生成代码文件
    
    Args:
        markdown_file_path: markdown文件所在的目录路径
        
    Returns:
        list[str]: 成功时返回创建的文件路径列表
        str: 失败时返回错误信息
    """
    if not markdown_file_path or not isinstance(markdown_file_path, str):
        error_msg = f"无效的markdown文件路径: {markdown_file_path}"
        logger.error(error_msg)
        return error_msg
    
    try:
        # 构建文件路径
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        result_md_path = os.path.join(current_dir, markdown_file_path, "result.md")
        
        # 检查result.md文件是否存在
        if not os.path.exists(result_md_path):
            error_msg = f"result.md文件不存在: {result_md_path}"
            logger.error(error_msg)
            return error_msg
            
        # 读取markdown内容
        with open(result_md_path, 'r', encoding='utf-8') as file:
            markdown_content = file.read()
            
        if not markdown_content.strip():
            error_msg = f"result.md文件内容为空: {result_md_path}"
            logger.error(error_msg)
            return error_msg

        # 读取系统提示词
        coder_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "prompts", "MatchingCode.md")
        
        if not os.path.exists(coder_path):
            error_msg = f"系统提示词文件不存在: {coder_path}"
            logger.error(error_msg)
            return error_msg
            
        with open(coder_path, "r", encoding="utf-8") as f:
            system_prompt = f.read()

        final_prompt = f"{system_prompt}\n\n{markdown_content}"

        # 获取LLM实例
        try:
            llm = get_llm_coder_type()
        except Exception as e:
            error_msg = f"获取LLM实例失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

        # 使用流式响应并处理可能的截断
        accumulated_content = ""
        max_iterations = 10  # 防止无限循环
        iteration_count = 0

        while iteration_count < max_iterations:
            iteration_count += 1
            try:
                # 获取流式响应
                stream = llm.stream([
                    HumanMessage(content=final_prompt + "\n已生成内容:\n" + accumulated_content + "\n请继续生成:")
                ])

                chunk_content = ""
                # 迭代处理流式响应
                for chunk in stream:
                    if hasattr(chunk, 'content'):
                        chunk_content += chunk.content

                # 检查是否因为 token 限制被截断
                if hasattr(chunk, 'response_metadata') and chunk.response_metadata.get('stop_reason') == 'max_tokens':
                    # 累积内容并继续生成
                    accumulated_content += chunk_content
                    logger.info(f"LLM响应被截断，继续生成 (第{iteration_count}次)")
                else:
                    # 正常完成，合并最后的内容并退出循环
                    accumulated_content += chunk_content
                    break
            except Exception as e:
                error_msg = f"LLM流式响应处理失败 (第{iteration_count}次): {str(e)}"
                logger.error(error_msg)
                return error_msg
        
        if iteration_count >= max_iterations:
            error_msg = f"LLM响应生成超过最大迭代次数({max_iterations})，可能存在问题"
            logger.error(error_msg)
            return error_msg

        # 修复JSON格式
        try:
            json_content = extract_pure_json(accumulated_content)
        except Exception as e:
            error_msg = f"JSON格式修复失败: {str(e)}\n原始内容: {accumulated_content[:500]}..."
            logger.error(error_msg)
            return error_msg

        # 解析JSON并创建文件
        code_list = []
        try:
            code_files = json.loads(json_content)
            
            # 验证JSON结构
            if not isinstance(code_files, dict):
                error_msg = f"LLM返回的不是JSON对象格式，而是 {type(code_files).__name__}。\n" \
                           f"期望格式: {{\"文件名\": \"文件内容\"}}\n" \
                           f"实际内容: {str(code_files)[:200]}..."
                logger.error(error_msg)
                return error_msg
                
            if len(code_files) == 0:
                error_msg = "LLM返回的JSON对象为空，没有生成任何文件"
                logger.error(error_msg)
                return error_msg

            # 获取markdown文件所在的目录
            output_dir = os.path.dirname(result_md_path)

            # 遍历JSON中的key和value，创建文件
            for file_name, file_content in code_files.items():
                if not isinstance(file_name, str) or not isinstance(file_content, str):
                    error_msg = f"无效的文件数据类型 - 文件名: {type(file_name).__name__}, 文件内容: {type(file_content).__name__}"
                    logger.error(error_msg)
                    return error_msg
                    
                if not file_name.strip():
                    error_msg = "文件名不能为空"
                    logger.error(error_msg)
                    return error_msg
                
                # 构建完整的文件路径
                file_path = os.path.join(output_dir, file_name)

                # 确保目录存在
                try:
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                except Exception as e:
                    error_msg = f"创建目录失败 {os.path.dirname(file_path)}: {str(e)}"
                    logger.error(error_msg)
                    return error_msg

                # 写入文件内容
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(file_content)
                    code_list.append(file_path)
                    logger.info(f"成功创建文件: {file_path}")
                except Exception as e:
                    error_msg = f"写入文件失败 {file_path}: {str(e)}"
                    logger.error(error_msg)
                    return error_msg

            logger.info(f"已在 {output_dir} 目录下成功创建了 {len(code_files)} 个文件")
            return code_list
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON解析失败: {str(e)}\n" \
                       f"修复后的JSON内容: {json_content[:500]}...\n" \
                       f"原始LLM输出: {accumulated_content[:500]}..."
            logger.error(error_msg)
            return error_msg
            
    except FileNotFoundError as e:
        error_msg = f"文件未找到: {str(e)}"
        logger.error(error_msg)
        return error_msg
    except PermissionError as e:
        error_msg = f"文件权限错误: {str(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"创建代码文件时发生未知错误: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return error_msg


def markdown_generate(uuid_dir):
    md_file_path = os.path.join(uuid_dir, "ec.md")
    with open(md_file_path, 'r', encoding='utf-8') as f:
        md_content = f.read()
    interface_md = analyze_content_with_llm(md_content)
    logger.info(f"本次用例对应的是{interface_md}")
    result = process_with_coder_prompt(uuid_dir, interface_md, "ec.md")
    logger.info(f"markdown文件已生成完{result}")
    return uuid_dir, interface_md


def compare_pom(uuid_dir, repo):
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    md_file_path = os.path.join(current_dir, uuid_dir, "dependency.md")
    # 检查文件是否存在，如果不存在则直接返回
    if not os.path.exists(md_file_path):
        logger.info(f"dependency.md 文件不存在: {md_file_path}")
        return
    with open(md_file_path, 'r', encoding='utf-8') as f:
        md_content = f.read()
    group_id_match = re.search(r'<groupId>(.*?)</groupId>', md_content)
    artifact_id_match = re.search(r'<artifactId>(.*?)</artifactId>', md_content)
    version_match = re.search(r'<version>(.*?)</version>', md_content)
    group_id = group_id_match.group(1) if group_id_match else None
    artifact_id = artifact_id_match.group(1) if artifact_id_match else None
    version_match = version_match.group(1) if version_match else None

    pom_file_content = get_pom_file_content(repo, "pom.xml")
    is_modify, modify_pom_content = check_dependency_exists_xml(pom_file_content, group_id, artifact_id, version_match)
    if is_modify:
        md_file_path = os.path.join(current_dir, uuid_dir, "pom.xml")
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(modify_pom_content)
            logger.info(f"uuid_dir:{uuid_dir}中已写入新的修改后的pom.xml文件")



def check_dependency_exists_xml(pom_content, target_group_id, target_artifact_id, version_match):
    """使用 ElementTree 检查 pom.xml 中是否存在指定 groupId 的依赖"""
    namespaces = {'mvn': 'http://maven.apache.org/POM/4.0.0'}
    ET.register_namespace('', namespaces['mvn'])
    root = ET.fromstring(pom_content)

    dependencies = root.find('mvn:dependencies', namespaces)
    for dependency in dependencies.findall('mvn:dependency', namespaces):
        group_id_element = dependency.find('mvn:groupId', namespaces)
        artifact_id_element = dependency.find('mvn:artifactId', namespaces)

        if (group_id_element is not None and group_id_element.text == target_group_id and
                artifact_id_element is not None and artifact_id_element.text == target_artifact_id):
            logger.info(f"pom.xml中已存在依赖：{target_group_id}:{target_artifact_id}")
            return False, ET.tostring(root, encoding='unicode')

    # 依赖不存在，添加新的依赖
    logger.info(f"添加新依赖：{target_group_id}:{target_artifact_id}")
    new_dependency = ET.SubElement(dependencies, '{http://maven.apache.org/POM/4.0.0}dependency')

    group_id_elem = ET.SubElement(new_dependency, '{http://maven.apache.org/POM/4.0.0}groupId')
    group_id_elem.text = target_group_id

    artifact_id_elem = ET.SubElement(new_dependency, '{http://maven.apache.org/POM/4.0.0}artifactId')
    artifact_id_elem.text = target_artifact_id

    # 如果提供了版本信息，添加版本
    if version_match:
        version_elem = ET.SubElement(new_dependency, '{http://maven.apache.org/POM/4.0.0}version')
        version_elem.text = version_match
    ET.indent(root, space="    ")
    # 返回修改后的 XML 内容
    return True, ET.tostring(root, encoding='unicode')




def create_code_file(markdown_file_path):
    """
    从result.md文件中灵活提取代码块，自动识别文件类型和名称
    """

    # 读取result.md文件
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    write_dir = os.path.join(current_dir, markdown_file_path)

    result_md_path = os.path.join(current_dir, markdown_file_path, "result.md")

    with open(result_md_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 提取所有代码块的通用模式
    code_blocks = re.findall(r'```(\w+)\n(.*?)\n```', content, re.DOTALL)

    extracted_files = []

    for i, (language, code_content) in enumerate(code_blocks):

        # 根据语言类型和代码内容确定文件名
        if language == 'java':
            # 提取Java类名
            class_match = re.search(r'public class (\w+)', code_content)
            if class_match:
                class_name = class_match.group(1)
                filename = f"{class_name}.java"

                # 判断是接口类还是测试类
                if 'extends HttpRequestService' in code_content:
                    description = f"接口类实现 ({class_name})"
                elif '@Test' in code_content:
                    description = f"测试类实现 ({class_name})"
                else:
                    description = f"Java类 ({class_name})"
            else:
                filename = f"JavaCode_{i + 1}.java"
                description = f"Java代码块 {i + 1}"

        elif language == 'json':
            # 分析JSON内容确定类型
            if '"comments"' in code_content and '"request"' in code_content:
                # 测试数据文件
                # 尝试从前面的Java测试类中提取类名
                test_class_match = re.search(r'public class (\w+Test)', content)
                if test_class_match:
                    test_class_name = test_class_match.group(1)
                    filename = f"{test_class_name}.json"
                else:
                    filename = "TestData.json"
                description = "测试数据文件"

            elif '"$schema"' in code_content and '"type"' in code_content:
                # Schema文件
                # 尝试从前面的Java类中提取类名
                api_class_match = re.search(r'public class (\w+).*extends HttpRequestService', content)
                if api_class_match:
                    api_class_name = api_class_match.group(1)
                    filename = f"{api_class_name}Schema.json"
                else:
                    filename = "ApiSchema.json"
                description = "Schema文件"

            else:
                filename = f"JsonData_{i + 1}.json"
                description = f"JSON数据块 {i + 1}"

        else:
            # 其他类型的代码块
            filename = f"Code_{i + 1}.{language}"
            description = f"{language.upper()}代码块 {i + 1}"

        if filename:
            # 构建完整的文件路径
            file_path = os.path.join(str(write_dir), str(filename))

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(code_content)

            extracted_files.append({
                'path': file_path,
                'filename': filename,
                'description': description,
                'language': language
            })

            logger.info(f"✅ 成功提取并保存 {description}: {filename}")

    logger.info(f"🎉 代码提取完成！共提取了 {len(extracted_files)} 个文件")

    return extracted_files




if __name__ == "__main__":
    # with open("/Users/<USER>/downDataSouces/toolchain-muse-app/ec_test.md", "r", encoding="utf-8") as f:
    #     input_content = f.read()
    # get_dependency_content(input_content)
    compare_pom("/Users/<USER>/downDataSouces/toolchain-muse-app/123445555555", "ssh://**********************/dz-qa-auto/dzview-autotest.git")
#     file_path = "/Users/<USER>/downDataSouces/toolchain-muse-app/output.md"
#
#     # 读取文件内容
#     try:
#         with open(file_path, 'r', encoding='utf-8') as file:
#             content = file.read()
#     except Exception as e:
#         print(f"读取文件时出错: {e}")
#     print(analyze_content_with_llm(content))
    # create_code_file("2cd5e9f6-4946-45ed-be20-8c3149dfd8d3")


