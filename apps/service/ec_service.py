import requests
from settings.settings import logger

def get_ec(test_case_id, user_name):
    url = "http://common.vip.sankuai.com/basic/ec/case/byCaseId"

    # 构建请求参数
    params = {
        "testCaseId": test_case_id,
        "fromSystem": 6,
        "operaterMis": user_name
    }

    # 发送 GET 请求
    response = requests.get(url, params=params)

    # 检查响应状态
    if response.status_code == 200:
        return response.json().get("data").get("desc")
    else:
        raise Exception(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")


if __name__ == "__main__":
    logger.info(get_ec(2193279, "linju"))
