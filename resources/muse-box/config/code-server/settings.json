{"security.workspace.trust.startupPrompt": "never", "security.workspace.trust.enabled": false, "workbench.startupEditor": "none", "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "vs-seti", "extensions.autoUpdate": false, "extensions.autoCheckUpdates": false, "extensions.ignoreRecommendations": true, "locale": "zh-cn", "configurationSync.store.maxSize": 128, "update.mode": "none", "restRemoteControl.port": 59895, "workbench.sideBar.location": "left", "editor.fontSize": 14, "editor.fontFamily": "'JetBrains Mono', 'Droid Sans Mono', 'monospace'", "editor.fontLigatures": true, "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "editor.formatOnSave": true, "editor.minimap.enabled": true, "editor.renderWhitespace": "boundary", "editor.cursorBlinking": "smooth", "editor.cursorSmoothCaretAnimation": "on", "editor.linkedEditing": true, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "terminal.integrated.fontFamily": "'JetBrains Mono', 'Droid Sans Mono', 'monospace'", "terminal.integrated.fontSize": 13, "terminal.integrated.cursorBlinking": true, "terminal.integrated.cursorStyle": "line", "workbench.editor.enablePreview": false, "workbench.list.openMode": "doubleClick", "workbench.tree.indent": 16, "workbench.tree.renderIndentGuides": "always", "workbench.editor.tabSizing": "shrink", "breadcrumbs.enabled": true, "telemetry.telemetryLevel": "off", "window.menuBarVisibility": "toggle", "window.titleBarStyle": "custom", "window.zoomLevel": 0, "window.dialogStyle": "custom", "window.title": "${activeEditorShort}${separator}${rootName}", "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "explorer.compactFolders": false, "cline.preferredLanguage": "Simplified Chinese - 简体中文", "roo-cline.allowedCommands": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"]}