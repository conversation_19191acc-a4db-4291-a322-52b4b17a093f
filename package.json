{"name": "muse-studio", "version": "0.0.1", "private": true, "scripts": {"dev": "turbo dev --concurrency=15", "build": "turbo build", "start": "turbo run start", "lint": "turbo lint", "check-types": "turbo run check-types", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,mdx,json,css,scss}\"", "clean": "rm -rf node_modules && rm -rf .next && rm -rf .turbo && rm -rf pnpm-lock.yaml && find . -name node_modules -type d -prune -exec rm -rf '{}' \\; && find . -name .next -type d -prune -exec rm -rf '{}' \\; && find . -name .turbo -type d -prune -exec rm -rf '{}' \\; && find . -name dist -type d -prune -exec rm -rf '{}' \\; && find . -name pnpm-lock.yaml -type f -delete", "prepare": "simple-git-hooks", "update:deps": "taze -r -w", "update:deps:check": "taze -r", "update:deps:major": "taze -r -w major", "update:deps:minor": "taze -r -w minor", "update:deps:patch": "taze -r -w patch", "update:install": "pnpm update:deps && pnpm install"}, "dependencies": {"@ag-ui/client": "^0.0.28", "@ag-ui/core": "^0.0.28", "@ag-ui/encoder": "^0.0.28", "@ag-ui/proto": "^0.0.28", "@copilotkit/react-core": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@copilotkit/runtime": "^1.9.1", "@copilotkit/runtime-client-gql": "^1.9.1", "@copilotkit/sdk-js": "^1.9.1", "@copilotkit/shared": "^1.9.1", "rxjs": "^7.8.2"}, "devDependencies": {"@types/node": "^24.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "cross-env": "^7.0.3", "eslint": "^9.29.0", "lint-staged": "^16.1.2", "prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.13", "simple-git-hooks": "^2.13.0", "taze": "^19.1.0", "turbo": "^2.5.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.12.2", "engines": {"node": ">=22"}, "simple-git-hooks": {"pre-commit": "npx --no -- cross-env NODE_OPTIONS=--max-old-space-size=8192 lint-staged"}, "lint-staged": {"*.mdx": ["prettier --write --no-error-on-unmatched-pattern", "eslint --fix"], "packages/ui/src/components/**/*.{js,jsx,ts,tsx}": ["echo \"Skipping shadcn components\""], "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css}": "prettier --write"}}