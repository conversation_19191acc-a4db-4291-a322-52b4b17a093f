{"name": "tersa", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently -n \"Next.js,Supabase,Email,Stripe\" \"next dev --turbopack\" \"npx supabase start\" \"email dev --port 3001\" \"stripe listen --forward-to localhost:3000/api/webhooks/stripe\"", "bump-deps": "npx npm-check-updates -u && pnpm install", "bump-ui": "npx shadcn@latest add --all --overwrite", "build": "next build --turbopack", "start": "next start", "lint": "next lint", "export": "email export", "migrate": "npx drizzle-kit push", "generate": "npx -y openapi-typescript@latest https://api.us1.bfl.ai/openapi.json -o ./openapi/bfl.d.ts"}, "dependencies": {"@ai-sdk/amazon-bedrock": "^2.2.10", "@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/cerebras": "^0.2.14", "@ai-sdk/cohere": "^1.2.10", "@ai-sdk/deepinfra": "^0.2.15", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/fal": "^0.1.12", "@ai-sdk/fireworks": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/hume": "^0.0.2", "@ai-sdk/lmnt": "^0.0.2", "@ai-sdk/luma": "^0.1.8", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@ai-sdk/react": "^1.2.12", "@ai-sdk/replicate": "^0.2.8", "@ai-sdk/togetherai": "^0.2.14", "@ai-sdk/vercel": "^0.0.1", "@ai-sdk/xai": "^1.2.16", "@hookform/resolvers": "^5.1.1", "@icons-pack/react-simple-icons": "^13.1.0", "@marsidev/react-turnstile": "^1.1.0", "@monaco-editor/react": "4.7.0", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@react-email/components": "0.0.42", "@runwayml/sdk": "^2.3.0", "@shikijs/transformers": "^3.6.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@t3-oss/env-core": "^0.13.8", "@t3-oss/env-nextjs": "^0.13.8", "@tiptap/core": "^2.14.0", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-code-block-lowlight": "^2.14.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-subscript": "^2.14.0", "@tiptap/extension-superscript": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-typography": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@tiptap/suggestion": "^2.14.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "@vercel/analytics": "^1.5.0", "@xyflow/react": "^12.7.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "embla-carousel-react": "^8.6.0", "fuse.js": "^7.1.0", "input-otp": "^1.4.2", "jotai": "^2.12.5", "lowlight": "^3.3.0", "lucide-react": "^0.515.0", "lumaai": "^1.13.0", "modern-screenshot": "^4.6.4", "motion": "^12.18.1", "nanoid": "^5.1.5", "next": "15.3.3", "next-themes": "^0.4.6", "openai": "^5.3.0", "openapi-fetch": "^0.14.0", "perfect-cursors": "^1.0.5", "postgres": "^3.4.7", "posthog-js": "^1.252.0", "posthog-node": "^5.1.0", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.57.0", "react-hotkeys-hook": "^5.1.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-tweet": "^3.2.2", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "replicate": "^1.0.1", "resend": "^4.6.0", "shadcn-prose": "^1.0.8", "shiki": "^3.6.0", "sonner": "^2.0.5", "standardwebhooks": "^1.0.0", "stripe": "^18.2.1", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "tippy.js": "^6.3.7", "tunnel-rat": "^0.1.2", "tw-animate-css": "^1.3.4", "use-debounce": "^10.0.5", "use-stick-to-bottom": "^1.1.1", "vaul": "^1.1.2", "zod": "^3.25.64"}, "devDependencies": {"@auto-it/first-time-contributor": "^11.3.0", "@auto-it/git-tag": "^11.3.0", "@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "drizzle-kit": "^0.31.1", "prettier": "^3.5.3", "react-email": "4.0.16", "tailwindcss": "^4", "typescript": "^5", "ultracite": "4.2.10"}, "packageManager": "pnpm@10.12.1"}