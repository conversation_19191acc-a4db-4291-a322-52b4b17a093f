{"name": "muse-studio", "version": "0.0.1", "private": true, "scripts": {"dev": "turbo dev", "build": "turbo build", "start": "turbo run start", "lint": "turbo lint", "check-types": "turbo run check-types", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,mdx,json,css,scss}\"", "clean": "rm -rf node_modules && rm -rf .next && rm -rf .turbo && rm -rf pnpm-lock.yaml && find . -name node_modules -type d -prune -exec rm -rf '{}' \\; && find . -name .next -type d -prune -exec rm -rf '{}' \\; && find . -name .turbo -type d -prune -exec rm -rf '{}' \\; && find . -name dist -type d -prune -exec rm -rf '{}' \\; && find . -name pnpm-lock.yaml -type f -delete", "prepare": "simple-git-hooks", "update:deps": "taze -r -w", "update:deps:check": "taze -r", "update:deps:major": "taze -r -w major", "update:deps:minor": "taze -r -w minor", "update:deps:patch": "taze -r -w patch", "update:install": "pnpm update:deps && pnpm install"}, "devDependencies": {"@types/node": "^24.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "cross-env": "^7.0.3", "eslint": "^9.29.0", "lint-staged": "^16.1.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "simple-git-hooks": "^2.13.0", "taze": "^19.1.0", "turbo": "^2.5.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.12.1", "pnpm": {"overrides": {"@langchain/community@<0.3.3": ">=0.3.3", "openai": "^5.5.1", "rxjs": "7.8.1"}}, "engines": {"node": ">=22"}, "simple-git-hooks": {"pre-commit": "npx --no -- cross-env NODE_OPTIONS=--max-old-space-size=8192 lint-staged"}, "lint-staged": {"*.mdx": ["prettier --write --no-error-on-unmatched-pattern", "eslint --fix"], "packages/ui/src/components/**/*.{js,jsx,ts,tsx}": ["echo \"Skipping shadcn components\""], "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css}": "prettier --write"}}