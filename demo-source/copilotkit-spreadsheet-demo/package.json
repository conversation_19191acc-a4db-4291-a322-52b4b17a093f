{"name": "spreadsheet-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@copilotkit/react-core": "^1.9.1", "@copilotkit/react-textarea": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@copilotkit/runtime": "^1.9.1", "@copilotkit/shared": "^1.9.1", "@heroicons/react": "^2.2.0", "@langchain/community": "^0.3.46", "@langchain/core": "^0.3.59", "@langchain/langgraph": "^0.3.4", "@langchain/openai": "^0.5.13", "@syncfusion/ej2-react-spreadsheet": "^29.2.10", "langchain": "^0.3.28", "next": "15.0.3", "openai": "^5.5.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-spreadsheet": "^0.10.1", "scheduler": "^0.26.0"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-next": "15.0.3", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.12.1"}