name: Deploy Production

on:
  push:
    branches:
      - production   # Trigger on pushes to the production branch
  workflow_dispatch:  # Allow manual triggering from the GitHub UI

permissions:
  actions: read
  contents: read

jobs:
  deploy-production:
    runs-on: ubuntu-latest
    env:
      CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
      CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'pnpm'
      - run: pnpm install --frozen-lockfile --child-concurrency=10

      - uses: nrwl/nx-set-shas@v4

      - run: pnpm nx affected -t lint test:ci type-check build deploy:production
