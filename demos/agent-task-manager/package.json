{"name": "agent-task-manager", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "npm run build && vite preview", "deploy": "npm run build && wrangler deploy", "format": "biome format --write", "test": "vitest", "test:ci": "vitest --watch=false", "cf-typegen": "wrangler types", "lint": "biome lint --error-on-warnings", "lint:fix": "biome lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"agents": "^0.0.95", "ai": "^4.3.16", "hono": "^4.8.2", "workers-ai-provider": "0.7.0", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "^2.0.4", "@cloudflare/vite-plugin": "^1.7.4", "@cloudflare/vitest-pool-workers": "^0.8.43", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "~3.2.4", "wrangler": "^4.20.5"}}