{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "mcp-slack-oauth",
  "main": "src/index.ts",
  "compatibility_date": "2025-03-10",
  "compatibility_flags": ["nodejs_compat"],
  "migrations": [
    {
      "new_sqlite_classes": ["SlackMCP"],
      "tag": "v1",
    },
  ],
  "vars": {
    // replace this in .dev.vars
    "SLACK_CLIENT_ID": "1234567890",
    "SLACK_CLIENT_SECRET": "1234567890",
  },
  "durable_objects": {
    "bindings": [
      {
        "class_name": "SlackMCP",
        "name": "MCP_OBJECT",
      },
    ],
  },
  "kv_namespaces": [
    {
      "binding": "OAUTH_KV",
      "id": "<YOUR_KV_NAMESPACE_ID>",
    },
  ],
  "observability": {
    "enabled": true,
  },
  "dev": {
    "port": 8788,
  },
}
