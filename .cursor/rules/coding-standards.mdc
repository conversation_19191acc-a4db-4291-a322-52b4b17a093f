---
description: 编码标准和最佳实践指南
globs: ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"]
alwaysApply: true
---

# 编码标准和最佳实践

## 代码风格
项目使用 Prettier 和 ESLint 进行代码格式化和质量检查。

### 配置文件
- [.prettierrc.json](mdc:.prettierrc.json) - Prettier 配置
- [eslint.config.js](mdc:eslint.config.js) - ESLint 配置
- [.prettierignore](mdc:.prettierignore) - Prettier 忽略文件

## TypeScript 规范
- 使用严格的 TypeScript 配置
- 所有组件和函数都应该有明确的类型定义
- 优先使用接口 (interface) 而不是类型别名 (type) 来定义对象结构

## React 组件规范
- 使用函数组件和 React Hooks
- 组件文件使用 PascalCase 命名
- 导出组件时使用 named export 和 default export
- 组件 props 应该有明确的 TypeScript 接口定义

## 文件命名约定
- 组件文件: `ComponentName.tsx`
- 工具函数: `utils.ts` 或 `helpers.ts`
- 类型定义: `types.ts` 或 `interfaces.ts`
- 常量: `constants.ts`

## 导入顺序
1. React 相关导入
2. 第三方库导入
3. 内部组件导入
4. 相对路径导入
5. 类型导入 (使用 `import type`)

## Git 提交规范
项目使用 simple-git-hooks 和 lint-staged 进行提交前检查：
- 代码会自动格式化
- ESLint 会自动修复可修复的问题
- 提交前会运行类型检查

## 包管理
- 使用 pnpm 而不是 npm 或 yarn
- 新增依赖时使用 `pnpm add` 命令
- 工作区依赖使用 `workspace:*` 协议
