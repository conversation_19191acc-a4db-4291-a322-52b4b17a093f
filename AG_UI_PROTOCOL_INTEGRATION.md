# AG-UI 协议集成指南

本文档描述了如何将现有的 Mario Agent 系统升级为支持 AG-UI 协议的实现。

## 概述

AG-UI 协议是一个标准化的 AI Agent 通信协议，提供了统一的事件格式和消息流处理机制。通过集成 AG-UI 协议，Mario Agent 系统可以：

- 支持标准化的事件驱动通信
- 提供更好的流式消息处理
- 实现更精确的状态管理
- 支持工具调用和中断处理

## 主要变更

### 1. 环境配置更新

**文件**: `apps/web/.env.development`

```bash
# 修改前
MARIO_WS_ENDPOINT=ws://0.0.0.0:8080/ws/chat

# 修改后
MARIO_WS_ENDPOINT=ws://localhost:8080/ws/ag-ui
```

### 2. 新增 AG-UI WebSocket 钩子

**文件**: `integrations/mario-agent/src/hooks/useAGUIWebSocket.ts`

这是一个全新的 React Hook，专门用于处理 AG-UI 协议的 WebSocket 通信：

```typescript
import { useAGUIWebSocket } from '@mario-agent/hooks/useAGUIWebSocket';

const {
  isConnected,
  isLoading,
  messages,
  currentStreamingMessage,
  sendMessage,
  stopCurrentOperation,
  clearMessages
} = useAGUIWebSocket({
  serverUrl: 'ws://localhost:8080/ws/ag-ui',
  onError: (error) => console.error(error)
});
```

#### 主要特性

- **事件驱动**: 支持 AG-UI 标准事件类型
- **流式处理**: 实时处理文本消息增量更新
- **状态管理**: 自动管理连接状态和消息历史
- **错误处理**: 完善的错误处理和重连机制
- **工具调用**: 支持工具调用事件的处理

#### 支持的 AG-UI 事件类型

| 事件类型 | 描述 | 处理方式 |
|---------|------|----------|
| `run_started` | 运行开始 | 设置加载状态 |
| `text_message_start` | 文本消息开始 | 初始化流式消息 |
| `text_message_delta` | 文本消息增量 | 累积消息内容 |
| `text_message_end` | 文本消息结束 | 完成消息并添加到历史 |
| `tool_calls_start` | 工具调用开始 | 显示工具调用状态 |
| `tool_calls_end` | 工具调用结束 | 显示工具调用结果 |
| `run_finished` | 运行完成 | 清除加载状态 |
| `error` | 错误事件 | 显示错误消息 |
| `interrupt` | 中断事件 | 处理中断状态 |

### 3. 后端 API 路由更新

**文件**: `apps/web/app/(backend)/api/integration/mario/[agentId]/route.ts`

#### 主要变更

1. **WebSocket 端点更新**:
   ```typescript
   // 修改前
   const MARIO_WS_ENDPOINT = 'ws://0.0.0.0:8080/ws/chat';
   
   // 修改后
   const MARIO_WS_ENDPOINT = 'ws://localhost:8080/ws/ag-ui';
   ```

2. **消息格式转换**:
   ```typescript
   // AG-UI 格式的请求
   const aguiRequest = {
     type: 'run',
     messages: [{
       role: 'user',
       content: message.message
     }],
     thread_id: message.sessionId,
     run_id: `run-${Date.now()}`,
     tools: [],
     state: {
       agentId: message.agentId,
       agentConfig: message.agentConfig
     }
   };
   ```

3. **事件处理逻辑**:
   - 支持流式消息处理
   - 处理 AG-UI 标准事件
   - 增强的错误处理
   - 更长的超时时间（60秒）

### 4. 删除废弃文件

**删除**: `apps/web/app/(backend)/api/mario/chat/route.ts`

该文件已被新的 AG-UI 集成路由替代，不再需要。

## 使用示例

### 基础使用

```typescript
import React from 'react';
import { useAGUIWebSocket } from '@mario-agent/hooks/useAGUIWebSocket';

function MarioChat() {
  const {
    isConnected,
    messages,
    sendMessage,
    currentStreamingMessage
  } = useAGUIWebSocket();

  const handleSend = async () => {
    await sendMessage({
      message: 'Hello, Mario!',
      mario_case: 'web_development',
      port: 3000,
      model: 'gpt-4',
      mcpServers: []
    });
  };

  return (
    <div>
      {messages.map(msg => (
        <div key={msg.id}>{msg.content}</div>
      ))}
      {currentStreamingMessage && (
        <div>{currentStreamingMessage}</div>
      )}
      <button onClick={handleSend} disabled={!isConnected}>
        发送消息
      </button>
    </div>
  );
}
```

### 完整示例

查看 `integrations/mario-agent/examples/ag-ui-example.tsx` 获取完整的使用示例。

## 兼容性

### 向后兼容

- 原有的 `useMarioWebSocket` 钩子仍然可用
- 现有的组件和类型定义保持不变
- 原有的 Agent 类和配置继续工作

### 迁移建议

1. **新项目**: 直接使用 `useAGUIWebSocket`
2. **现有项目**: 逐步迁移到 AG-UI 协议
3. **混合使用**: 可以同时使用两种协议

## 配置选项

### useAGUIWebSocket 配置

```typescript
interface UseAGUIWebSocketOptions {
  serverUrl?: string;                    // WebSocket 服务器地址
  onContainerCreated?: (id: string, port: number) => void;  // 容器创建回调
  onError?: (error: string) => void;     // 错误处理回调
}
```

### 环境变量

```bash
# AG-UI WebSocket 端点
MARIO_WS_ENDPOINT=ws://localhost:8080/ws/ag-ui

# 前端 WebSocket URL（可选）
NEXT_PUBLIC_MARIO_WS_URL=ws://localhost:8080/ws/ag-ui
```

## 调试和监控

### 日志输出

AG-UI 集成提供了详细的日志输出：

```typescript
// 在浏览器控制台中查看
console.log('Received AG-UI event:', event.type);
console.log('AG-UI run started');
console.log('AG-UI text message completed');
```

### 连接状态监控

```typescript
const {
  isConnected,      // 是否已连接
  isConnecting,     // 是否正在连接
  connectionError,  // 连接错误信息
  isLoading,        // 是否正在处理
  loadingMessage    // 加载状态消息
} = useAGUIWebSocket();
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 `MARIO_WS_ENDPOINT` 配置
   - 确认后端服务正在运行
   - 检查防火墙和网络设置

2. **消息不显示**
   - 检查 AG-UI 事件处理逻辑
   - 查看浏览器控制台错误
   - 确认消息格式正确

3. **流式消息中断**
   - 检查网络连接稳定性
   - 增加超时时间
   - 实现重连机制

### 调试技巧

1. **启用详细日志**:
   ```typescript
   const ws = useAGUIWebSocket({
     onError: (error) => {
       console.error('AG-UI Error:', error);
     }
   });
   ```

2. **监控 WebSocket 连接**:
   ```javascript
   // 在浏览器开发者工具的网络标签中查看 WebSocket 连接
   ```

3. **检查后端日志**:
   ```bash
   # 查看 Python 后端日志
   tail -f /path/to/mario/logs/app.log
   ```

## 性能优化

### 消息处理优化

- 使用 `useCallback` 优化事件处理函数
- 实现消息去重和缓存
- 限制消息历史长度

### 连接管理优化

- 实现智能重连策略
- 使用连接池管理多个会话
- 优化心跳检测机制

## 未来规划

1. **增强功能**
   - 支持更多 AG-UI 事件类型
   - 实现消息持久化
   - 添加离线模式支持

2. **性能改进**
   - 优化大量消息的渲染性能
   - 实现虚拟滚动
   - 减少内存占用

3. **开发体验**
   - 提供更多调试工具
   - 增加类型安全性
   - 完善文档和示例

## 总结

AG-UI 协议集成为 Mario Agent 系统带来了以下优势：

- **标准化**: 遵循 AG-UI 协议标准
- **实时性**: 更好的流式消息处理
- **可靠性**: 增强的错误处理和状态管理
- **扩展性**: 支持更多高级功能
- **兼容性**: 保持向后兼容

通过这次升级，Mario Agent 系统现在可以更好地与其他 AG-UI 兼容的系统集成，提供更优秀的用户体验。