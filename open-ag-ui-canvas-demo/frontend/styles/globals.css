@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}


.animated-fade-in { 
  /* Use the new pop-in animation */
  animation: pop-in 0.6s ease-out forwards;
}

.haiku-card {
  /* Subtle animated gradient background */
  background: linear-gradient(120deg, #ffffff 0%, #fdfdfd 50%, #ffffff 100%);
  background-size: 200% 200%; 
  animation: animated-gradient 10s ease infinite; 

  /* === Explicit Border Override Attempt === */
  /* 1. Set the default grey border for all sides */
  border: 1px solid #dee2e6; 
  
  /* 2. Explicitly override the top border immediately after */
  border-top: 10px solid #ff6f61 !important; /* Orange top - Added !important */
  /* === End Explicit Border Override Attempt === */
  
  padding: 2.5rem 3rem; 
  border-radius: 20px; 
  
  /* Default glow intensity */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07), 
              inset 0 1px 2px rgba(0, 0, 0, 0.01), 
              0 0 15px rgba(255, 111, 97, 0.25); 
  text-align: left;
  max-width: 745px; 
  margin: 3rem auto;
  min-width: 600px;
  
  /* Transition */
  transition: transform 0.35s ease, box-shadow 0.35s ease, border-top-width 0.35s ease, border-top-color 0.35s ease;
}

.haiku-card:hover {
  transform: translateY(-8px) scale(1.03); 
  /* Enhanced shadow + Glow */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 
              inset 0 1px 2px rgba(0, 0, 0, 0.01), 
              0 0 25px rgba(255, 91, 74, 0.5); 
  /* Modify only top border properties */
  border-top-width: 14px !important; /* Added !important */
  border-top-color: #ff5b4a !important; /* Added !important */
}

.haiku-card .flex {
  margin-bottom: 1.5rem;
}

.haiku-card .flex.haiku-line { /* Target the lines specifically */
  margin-bottom: 1.5rem;
  opacity: 1; /* Start hidden for animation */
  animation: fade-slide-in 0.5s ease-out forwards;
  /* animation-delay is set inline in page.tsx */
}

/* Remove previous explicit color overrides - rely on Tailwind */
/* .haiku-card p.text-4xl {
  color: #212529; 
}

.haiku-card p.text-base {
  color: #495057; 
} */

.haiku-card.applied-flash {
  /* Apply the flash animation once */
  /* Note: animation itself has !important on border-top-color */
  animation: flash-border-glow 0.6s ease-out forwards; 
}

/* Styling for images within the main haiku card */
.haiku-card-image {
  width: 9.5rem; /* Increased size (approx w-48) */
  height: 9.5rem; /* Increased size (approx h-48) */
  object-fit: cover;
  border-radius: 1.5rem; /* rounded-xl */
  border: 1px solid #e5e7eb;
  /* Enhanced shadow with subtle orange hint */
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1),
              0 3px 6px rgba(0, 0, 0, 0.08),
              0 0 10px rgba(255, 111, 97, 0.2);
  /* Inherit animation delay from inline style */
  animation-name: fadeIn;
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

/* Styling for images within the suggestion card */
.suggestion-card-image {
  width: 6.5rem; /* Increased slightly (w-20) */
  height: 6.5rem; /* Increased slightly (h-20) */
  object-fit: cover;
  border-radius: 1rem; /* Equivalent to rounded-md */
  border: 1px solid #d1d5db; /* Equivalent to border (using Tailwind gray-300) */
  margin-top: 0.5rem;
  /* Added shadow for suggestion images */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1),
              0 2px 4px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out; /* Added for smooth deselection */
}

/* Styling for the focused suggestion card image */
.suggestion-card-image-focus {
  width: 6.5rem; 
  height: 6.5rem; 
  object-fit: cover;
  border-radius: 1rem; 
  margin-top: 0.5rem;
  /* Highlight styles */
  border: 2px solid #ff6f61; /* Thicker, themed border */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), /* Base shadow for depth */
              0 0 12px rgba(255, 111, 97, 0.6); /* Orange glow */
  transform: scale(1.05); /* Slightly scale up */
  transition: all 0.2s ease-in-out; /* Smooth transition for focus */
}

/* Styling for the suggestion card container in the sidebar */
.suggestion-card {
  border: 1px solid #dee2e6; /* Same default border as haiku-card */
  border-top: 10px solid #ff6f61; /* Same orange top border */
  border-radius: 0.375rem; /* Default rounded-md */
  /* Note: background-color is set by Tailwind bg-gray-100 */
  /* Other styles like padding, margin, flex are handled by Tailwind */
}

.suggestion-image-container {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  width: 100%;
  height: 6.5rem;
}