# Agent 模板使用指南

本指南介绍如何使用提供的 agent 模板创建新的独立 agent。

## 📁 模板结构

```
apps/web/
├── app/(main)/
│   ├── types.ts                    # 通用类型定义
│   └── example-agent/              # 示例 agent
│       ├── page.tsx                # Agent 页面
│       ├── layout.tsx              # Agent 布局
│       ├── globals.css             # Agent 样式
│       └── config.ts               # Agent 配置
├── app/(backend)/api/
│   └── example-agent/
│       └── actions/
│           └── route.ts            # Agent API 路由
└── components/
    ├── providers/
    │   └── agent-provider.tsx      # Agent 状态管理
    ├── copilot-kit-wrapper.tsx     # CopilotKit 封装
    └── workspaces/
        └── example-workspace.tsx   # Agent 工作区组件
```

## 🚀 创建新 Agent

### 1. 复制模板文件

```bash
# 复制 agent 页面目录
cp -r apps/web/app/(main)/example-agent apps/web/app/(main)/your-agent-name

# 复制 API 路由目录
cp -r apps/web/app/(backend)/api/example-agent apps/web/app/(backend)/api/your-agent-name

# 复制 workspace 组件
cp apps/web/components/workspaces/example-workspace.tsx apps/web/components/workspaces/your-agent-workspace.tsx
```

### 2. 修改配置文件

编辑 `apps/web/app/(main)/your-agent-name/config.ts`：

```typescript
import { AgentConfig } from '../types';

export const YOUR_AGENT_CONFIG: AgentConfig = {
  id: 'your-agent-name',
  name: 'Your Agent Name',
  description: 'Your agent description',
  version: '1.0.0',
  capabilities: [
    'your-capability-1',
    'your-capability-2'
  ],
  settings: {
    maxTokens: 4000,
    temperature: 0.7,
    model: 'gpt-4'
  },
  endpoints: {
    actions: '/api/your-agent-name/actions',
    chat: '/api/copilotkit'
  },
  ui: {
    theme: 'default',
    layout: 'sidebar',
    features: {
      chat: true,
      taskList: true,
      statusIndicator: true
    }
  }
};
```

### 3. 更新页面组件

编辑 `apps/web/app/(main)/your-agent-name/page.tsx`：

```typescript
'use client';

import { YourAgentWorkspace } from '@/components/workspaces/your-agent-workspace';
import { AgentProvider } from '@/components/providers/agent-provider';
import { CopilotKitWrapper } from '@/components/copilot-kit-wrapper';
import { YOUR_AGENT_CONFIG } from './config';

export default function YourAgentPage() {
  return (
    <AgentProvider agent={YOUR_AGENT_CONFIG}>
      <CopilotKitWrapper
        runtimeUrl="/api/copilotkit"
        agent={YOUR_AGENT_CONFIG.id}
      >
        <YourAgentWorkspace />
      </CopilotKitWrapper>
    </AgentProvider>
  );
}
```

### 4. 自定义 Workspace 组件

编辑 `apps/web/components/workspaces/your-agent-workspace.tsx`，根据你的需求修改：

- 更新组件名称
- 修改 UI 结构
- 添加自定义功能
- 配置 CopilotKit actions

### 5. 实现 API 路由

编辑 `apps/web/app/(backend)/api/your-agent-name/actions/route.ts`，实现你的业务逻辑：

```typescript
export async function POST(request: NextRequest) {
  // 实现你的 API 逻辑
}
```

### 6. 更新样式

编辑 `apps/web/app/(main)/your-agent-name/globals.css`，添加你的自定义样式。

## 🎨 自定义指南

### Agent 配置

`AgentConfig` 接口提供了丰富的配置选项：

- **id**: Agent 唯一标识符
- **name**: 显示名称
- **description**: 描述信息
- **capabilities**: 能力列表
- **settings**: AI 模型设置
- **endpoints**: API 端点配置
- **ui**: UI 配置选项

### CopilotKit 集成

模板已集成 CopilotKit，提供以下功能：

- **useCopilotAction**: 定义 AI 可执行的操作
- **useCopilotReadable**: 让 AI 读取应用状态
- **useCoAgent**: 管理 agent 状态
- **useCoAgentStateRender**: 渲染 agent 状态

### 状态管理

使用 `AgentProvider` 进行状态管理：

```typescript
const { agent, status, tasks, updateStatus, addTask } = useAgent();
```

## 🔧 最佳实践

### 1. 独立性原则

- 每个 agent 应该有独立的配置、样式和 API
- 避免 agent 之间的直接依赖
- 使用共享的类型定义和工具函数

### 2. 命名规范

- Agent ID 使用 kebab-case: `my-agent`
- 组件名使用 PascalCase: `MyAgentWorkspace`
- 文件名使用 kebab-case: `my-agent-workspace.tsx`

### 3. 错误处理

- 在 API 路由中添加适当的错误处理
- 在组件中处理加载和错误状态
- 提供用户友好的错误信息

### 4. 性能优化

- 使用 React.memo 优化组件渲染
- 合理使用 useCallback 和 useMemo
- 避免不必要的状态更新

### 5. 类型安全

- 充分利用 TypeScript 类型系统
- 为 API 请求和响应定义类型
- 使用 zod 进行运行时类型验证

## 📚 扩展功能

### 添加新的 CopilotKit Action

```typescript
useCopilotAction({
  name: 'yourCustomAction',
  description: 'Description of your action',
  parameters: [
    {
      name: 'param1',
      type: 'string',
      description: 'Parameter description',
      required: true
    }
  ],
  handler: async ({ param1 }) => {
    // 实现你的逻辑
    return 'Action completed';
  }
});
```

### 添加数据持久化

```typescript
// 在 API 路由中集成数据库
import { db } from '@/lib/database';

export async function POST(request: NextRequest) {
  // 使用数据库存储数据
  const result = await db.tasks.create({
    data: taskData
  });
  
  return NextResponse.json(result);
}
```

### 集成外部服务

```typescript
// 在 agent 中集成外部 API
const response = await fetch('https://api.external-service.com/data', {
  headers: {
    'Authorization': `Bearer ${process.env.API_KEY}`
  }
});
```

## 🐛 故障排除

### 常见问题

1. **CopilotKit 连接失败**
   - 检查 runtimeUrl 是否正确
   - 确认 API 路由是否正常工作

2. **Agent 状态不更新**
   - 确认使用了 AgentProvider
   - 检查状态更新逻辑

3. **样式不生效**
   - 确认 globals.css 已正确导入
   - 检查 Tailwind CSS 类名

### 调试技巧

- 使用浏览器开发者工具检查网络请求
- 在控制台查看 CopilotKit 日志
- 使用 React DevTools 检查组件状态

## 📖 参考资源

- [CopilotKit 文档](https://docs.copilotkit.ai/)
- [Next.js 文档](https://nextjs.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)

---

通过遵循这个指南，你可以快速创建功能完整、独立的 agent，并根据具体需求进行定制和扩展。