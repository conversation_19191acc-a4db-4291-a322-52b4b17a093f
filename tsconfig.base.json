{"compilerOptions": {"lib": ["ESNext"], "target": "ESNext", "module": "ESNext", "moduleDetection": "force", "jsx": "react-jsx", "allowJs": true, "moduleResolution": "bundler", "verbatimModuleSyntax": true, "noEmit": true, "strict": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true}, "include": ["libs/**/*", "workers/**/*"], "exclude": ["node_modules", "dist"]}