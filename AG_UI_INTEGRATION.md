# AG-UI 集成指南

本文档介绍如何在 toolchain-muse-app 中使用 AG-UI 协议支持。

## 概述

我们已经为现有的 LangGraph WebSocket 服务添加了 AG-UI 协议支持，使其能够与 AG-UI 兼容的客户端进行通信。

## 新增的端点

### WebSocket 端点

- **URL**: `ws://localhost:8080/ws/ag-ui`
- **协议**: AG-UI WebSocket 协议
- **功能**: 实时双向通信，支持工作流执行和中断处理

### HTTP 端点

#### 1. 运行工作流
- **URL**: `POST /ag-ui/run`
- **功能**: 执行工作流并返回事件流
- **请求体**:
  ```json
  {
    "messages": [
      {
        "role": "user",
        "content": "你的问题"
      }
    ],
    "thread_id": "可选的线程ID"
  }
  ```

#### 2. 健康检查
- **URL**: `GET /ag-ui/health`
- **功能**: 检查服务状态
- **响应**:
  ```json
  {
    "status": "healthy",
    "service": "LangGraph AG-UI Adapter",
    "timestamp": "2024-01-01T00:00:00Z"
  }
  ```

#### 3. 服务信息
- **URL**: `GET /ag-ui/info`
- **功能**: 获取服务信息和支持的功能
- **响应**:
  ```json
  {
    "name": "LangGraph AG-UI Adapter",
    "version": "1.0.0",
    "protocol_version": "1.0",
    "features": {
      "websocket": true,
      "http_streaming": true,
      "interrupts": true,
      "state_snapshots": true
    }
  }
  ```

## AG-UI 事件类型

我们的适配器支持以下 AG-UI 标准事件：

### 工作流事件
- `run_started`: 工作流开始执行
- `run_finished`: 工作流执行完成

### 消息事件
- `text_message_start`: 开始新的文本消息
- `text_message_delta`: 文本消息增量更新
- `text_message_end`: 文本消息结束

### 工具调用事件
- `tool_calls_start`: 开始工具调用
- `tool_calls_end`: 工具调用完成

### 状态事件
- `state_snapshot`: 完整状态快照
- `state_delta`: 状态增量更新
- `messages_snapshot`: 消息快照

### 中断事件
- `interrupt`: 中断请求
- `error`: 错误事件

## 使用示例

### 1. WebSocket 客户端示例

```python
import asyncio
import json
import websockets

async def test_ag_ui_websocket():
    uri = "ws://localhost:8080/ws/ag-ui"
    
    async with websockets.connect(uri) as websocket:
        # 发送运行请求
        request = {
            "type": "run",
            "messages": [
                {
                    "role": "user",
                    "content": "请帮我生成一个Python函数"
                }
            ]
        }
        
        await websocket.send(json.dumps(request))
        
        # 接收事件
        async for message in websocket:
            event = json.loads(message)
            print(f"收到事件: {event['type']}")
            
            if event['type'] == 'run_finished':
                break

# 运行示例
asyncio.run(test_ag_ui_websocket())
```

### 2. HTTP 客户端示例

```python
import requests

def test_ag_ui_http():
    url = "http://localhost:8080/ag-ui/run"
    
    data = {
        "messages": [
            {
                "role": "user",
                "content": "请解释什么是机器学习"
            }
        ]
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    print(f"收到 {len(result['events'])} 个事件")
    for event in result['events']:
        print(f"事件类型: {event['type']}")

# 运行示例
test_ag_ui_http()
```

### 3. 完整客户端示例

我们提供了一个完整的客户端示例，位于 `examples/ag_ui_client_example.py`。运行方式：

```bash
cd toolchain-muse-app
python examples/ag_ui_client_example.py
```

## 启动服务

1. 确保依赖已安装：
   ```bash
   pip install -r requirements/base.txt
   ```

2. 启动服务：
   ```bash
   python main.py
   ```
   或者使用提供的脚本：
   ```bash
   ./run_app_locally.sh
   ```

3. 服务将在 `http://localhost:8080` 启动

## 兼容性

- **原有端点**: 原有的 `/ws/chat` 端点保持不变，确保向后兼容
- **AG-UI 协议**: 新的 AG-UI 端点完全符合 AG-UI 协议标准
- **事件映射**: LangGraph 事件被自动转换为 AG-UI 标准事件

## 架构说明

### 核心组件

1. **AGUIAdapter** (`apps/endpoints/llm/ag_ui_adapter.py`)
   - 负责将 LangGraph 事件转换为 AG-UI 事件
   - 处理状态管理和消息格式化

2. **AG-UI API** (`apps/endpoints/llm/ag_ui_api.py`)
   - 提供 AG-UI 兼容的 WebSocket 和 HTTP 端点
   - 处理客户端连接和请求路由

3. **事件映射**
   - `on_chain_start` → `run_started`
   - `on_chat_model_start` → `text_message_start`
   - `on_chat_model_stream` → `text_message_delta`
   - `on_chat_model_end` → `text_message_end`
   - `on_tool_start` → `tool_calls_start`
   - `on_tool_end` → `tool_calls_end`

### 数据流

```
客户端请求 → AG-UI API → LangGraph 工作流 → AGUIAdapter → AG-UI 事件 → 客户端
```

## 故障排除

### 常见问题

1. **连接失败**
   - 确保服务正在运行在正确的端口 (8080)
   - 检查防火墙设置

2. **事件格式错误**
   - 确保客户端发送的消息符合 AG-UI 协议
   - 检查 JSON 格式是否正确

3. **工作流执行失败**
   - 查看服务日志获取详细错误信息
   - 确保 LangGraph 配置正确

### 调试技巧

1. **启用详细日志**：
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **使用健康检查端点**：
   ```bash
   curl http://localhost:8080/ag-ui/health
   ```

3. **测试基本连接**：
   ```bash
   curl -X POST http://localhost:8080/ag-ui/run \
     -H "Content-Type: application/json" \
     -d '{"messages":[{"role":"user","content":"hello"}]}'
   ```

## 扩展和自定义

### 添加自定义事件类型

1. 在 `AGUIEventType` 枚举中添加新的事件类型
2. 在 `AGUIAdapter` 中添加相应的处理逻辑
3. 更新事件模型定义

### 修改事件映射

在 `AGUIAdapter.convert_langgraph_event()` 方法中修改事件转换逻辑。

### 添加中间件

可以在 AG-UI API 路由中添加认证、限流等中间件。

## 性能优化

1. **连接池**: 对于高并发场景，考虑使用连接池
2. **事件缓存**: 对于重复的状态快照，可以实现缓存机制
3. **压缩**: 对于大型响应，可以启用 WebSocket 压缩

## 安全考虑

1. **认证**: 在生产环境中添加适当的认证机制
2. **限流**: 实现请求限流以防止滥用
3. **输入验证**: 严格验证客户端输入
4. **CORS**: 配置适当的 CORS 策略

## 更新日志

### v1.0.0 (当前版本)
- 初始 AG-UI 协议支持
- WebSocket 和 HTTP 端点
- 完整的事件映射
- 中断处理支持
- 状态快照功能