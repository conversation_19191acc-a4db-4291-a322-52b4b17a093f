import logging
import asyncio

from apps.service.work_flow import run_agent_workflow

logger = logging.getLogger(__name__)


async def specific_tasks():
    """
    测试特定任务场景
    """
    logger.info("===== 开始测试特定任务场景 =====")

    # 定义不同的测试场景
    test_scenarios = [
        {
            "name": "测试任务",
            "input": "生成Mario用例,服务名称:com.sankuai.web.cart.api.service.CartService,方法名称:queryCartItemCount,服务AppKey:com.sankuai.cart.service,Thrift用例,请求参数:mtUserId,响应参数:commonResponse"
            # "input": "你好"
        }
    ]

    for scenario in test_scenarios:
        logger.info(f"\n测试场景: {scenario['name']}")
        logger.info(f"输入: {scenario['input']}")

        try:
            async for event in run_agent_workflow(
                user_input_messages=[{"role": "user", "content": scenario["input"]}],
                deep_thinking_mode=True
            ):
                if event["event"] == "message" and "delta" in event.get("data", {}):
                    content = event["data"]["delta"].get("content", "")
                    if content:
                        logger.info(f"收到消息: {content[:100]}..." if len(content) > 100 else f"收到消息: {content}")

        except Exception as e:
            logger.error(f"场景 '{scenario['name']}' 执行失败: {e}", exc_info=True)

    logger.info("===== 特定任务场景测试完成 =====\n")


async def main():
    await specific_tasks()

if __name__ == "__main__":
    """
    当直接运行此脚本时，执行主函数
    """
    try:
        # 运行异步主函数
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断了程序")
    except Exception as e:
        logger.error(f"运行过程中发生错误: {e}", exc_info=True)